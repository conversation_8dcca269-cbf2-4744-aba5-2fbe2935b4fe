# 🎯 **Admin Guide: Notification System**

## 📋 **Table of Contents**

1. [System Overview](#system-overview)
2. [Initial Setup](#initial-setup)
3. [Slack Integration Setup](#slack-integration-setup)
4. [Database Management](#database-management)
5. [Daily Operations](#daily-operations)
6. [Troubleshooting](#troubleshooting)
7. [Security & Best Practices](#security--best-practices)

---

## 🏗️ **System Overview**

### **What This System Does**
The Notification System is a **multi-provider notification platform** that allows applications to send notifications through various channels (email, SMS, push, in-app) with automatic fallback strategies.

### **Key Components**
- **Applications Table**: Main table for all active applications using the system
- **App Registrations**: Approval workflow for new application requests
- **App API Keys**: Secure authentication for applications
- **Slack Integration**: Admin interface for instant approvals/rejections
- **Multi-Provider Support**: Novu, Resend, Twilio, and direct integrations

### **Architecture Overview**
```
Dev<PERSON><PERSON> submits app → app_registrations (PENDING)
         ↓
    Admin approves via Slack
         ↓
Active application → applications (MAIN TABLE)
         ↓
API key generated → app_api_keys (AUTH)
         ↓
App uses system → Validates against app_api_keys
```

---

## 🚀 **Initial Setup**

### **1. Environment Configuration**

Create a `.env` file in the `apps/api` directory:

```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/notifications"

# Admin Configuration
ADMIN_SECRET="your-super-secure-admin-secret-here"
ADMIN_EMAIL="<EMAIL>"

# Novu Configuration (Global defaults)
NOVU_API_KEY="your-novu-api-key"
NOVU_APP_ID="your-novu-app-id"

# Resend Configuration (Global defaults)
RESEND_API_KEY="your-resend-api-key"
RESEND_FROM_EMAIL="<EMAIL>"

# Twilio Configuration (Global defaults)
TWILIO_ACCOUNT_SID="your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"

# Slack Configuration
SLACK_BOT_TOKEN="xoxb-your-slack-bot-token"
SLACK_ADMIN_CHANNEL="#admin"
SLACK_WEBHOOK_SECRET="your-webhook-secret-for-slack"

# Base URL
NEXT_PUBLIC_BASE_URL="https://your-domain.com"
```

### **2. Database Setup**

```bash
# Navigate to API directory
cd apps/api

# Install dependencies
npm install

# Run database migrations
npx prisma migrate dev --name initial_setup

# Verify database schema
npx prisma studio
```

### **3. Verify Database Schema**

After running migrations, you should see these tables:

- ✅ **`applications`** - Main table for active applications
- ✅ **`app_registrations`** - Approval workflow table
- ✅ **`app_api_keys`** - Authentication table
- ✅ **`users`** - User management
- ✅ **`notifications`** - Notification tracking
- ✅ **`delivery_status`** - Delivery tracking

---

## 🔗 **Slack Integration Setup**

### **1. Create Slack App**

1. Go to [https://api.slack.com/apps](https://api.slack.com/apps)
2. Click **"Create New App"** → **"From scratch"**
3. Name: `Notification System Admin`
4. Choose your workspace

### **2. Configure Bot Token Scopes**

Go to **"OAuth & Permissions"** → **"Bot Token Scopes"**:

```
✅ chat:write          - Send messages
✅ im:write            - Send direct messages  
✅ channels:read       - Read channel info
✅ channels:history    - Read channel messages
```

### **3. Install App to Workspace**

1. Go to **"Install App"**
2. Click **"Install to Workspace"**
3. **Copy the "Bot User OAuth Token"** (starts with `xoxb-`)

### **4. Enable Interactivity**

1. Go to **"Interactivity & Shortcuts"**
2. Toggle **"Interactivity"** to **On**
3. Set **"Request URL"** to: `https://your-domain.com/api/slack/interactive`
4. Click **"Save Changes"**

### **5. Enable Event Subscriptions**

1. Go to **"Event Subscriptions"**
2. Toggle **"Enable Events"** to **On**
3. Set **"Request URL"** to: `https://your-domain.com/api/slack/events`
4. Under **"Subscribe to bot events"**, add: `app_mention`
5. Click **"Save Changes"**

### **6. Add Bot to Admin Channel**

1. Go to your admin channel (e.g., `#admin`)
2. Type: `/invite @Notification System Admin`
3. The bot will now receive notifications in this channel

---

## 🗄️ **Database Management**

### **Database Schema Overview**

#### **Applications Table (Main Table)**
```sql
-- This represents ALL active applications using your system
CREATE TABLE applications (
    app_id TEXT PRIMARY KEY,           -- Primary identifier
    name TEXT NOT NULL,                -- App name
    description TEXT,                   -- App description
    webhook_url TEXT,                  -- Webhook endpoint
    
    -- Provider configurations
    novu_app_id TEXT,
    novu_api_key TEXT,
    resend_api_key TEXT,
    twilio_account_sid TEXT,
    twilio_auth_token TEXT,
    
    -- System configuration
    default_provider TEXT DEFAULT 'novu',
    fallback_strategy TEXT,
    max_retries INTEGER DEFAULT 3,
    retry_delay INTEGER DEFAULT 1000,
    rate_limits JSONB DEFAULT '{}',
    
    -- Status and metadata
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### **App Registrations Table (Approval Workflow)**
```sql
-- This is just for the approval workflow
CREATE TABLE app_registrations (
    id TEXT PRIMARY KEY,
    app_id TEXT UNIQUE,                -- References applications.app_id
    name TEXT NOT NULL,
    description TEXT,
    
    -- Contact information
    contact_email TEXT NOT NULL,
    contact_name TEXT NOT NULL,
    website TEXT,
    business_type TEXT,
    expected_volume INTEGER,
    
    -- Registration status
    status TEXT DEFAULT 'PENDING_APPROVAL',
    admin_notes TEXT,
    approved_at TIMESTAMP,
    approved_by TEXT,
    
    -- Provider configurations (for review)
    novu_app_id TEXT,
    novu_api_key TEXT,
    resend_api_key TEXT,
    twilio_account_sid TEXT,
    twilio_auth_token TEXT,
    
    -- Configuration (for review)
    fallback_strategy TEXT,
    max_retries INTEGER DEFAULT 3,
    retry_delay INTEGER DEFAULT 1000,
    rate_limits JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### **App API Keys Table (Authentication)**
```sql
-- This handles all API key authentication
CREATE TABLE app_api_keys (
    id TEXT PRIMARY KEY,
    app_id TEXT NOT NULL,              -- References applications.app_id
    api_key_hash TEXT NOT NULL,        -- Hashed API key
    name TEXT,                         -- Key name (e.g., "Production")
    
    -- Key status
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP,
    last_used_at TIMESTAMP,
    
    -- Permissions
    permissions TEXT[] DEFAULT '[]',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### **Database Operations**

#### **View All Active Applications**
```sql
SELECT 
    app_id,
    name,
    description,
    default_provider,
    is_active,
    created_at
FROM applications 
WHERE is_active = true
ORDER BY created_at DESC;
```

#### **View Pending Registrations**
```sql
SELECT 
    app_id,
    name,
    contact_email,
    contact_name,
    business_type,
    expected_volume,
    created_at
FROM app_registrations 
WHERE status = 'PENDING_APPROVAL'
ORDER BY created_at ASC;
```

#### **View API Key Usage**
```sql
SELECT 
    ak.name,
    ak.app_id,
    a.name as app_name,
    ak.is_active,
    ak.last_used_at,
    ak.created_at
FROM app_api_keys ak
JOIN applications a ON ak.app_id = a.app_id
ORDER BY ak.last_used_at DESC NULLS LAST;
```

---

## 🔄 **Daily Operations**

### **1. Monitoring New Registrations**

#### **Via Slack (Recommended)**
- New registrations automatically appear in your admin channel
- Each registration shows:
  - App name and ID
  - Contact information
  - Business details
  - Action buttons: [✅ Approve] [❌ Reject] [👁️ View Details]

#### **Via API Endpoint**
```bash
# Get pending registrations
curl -X GET "https://your-domain.com/api/applications/approve?adminSecret=your-admin-secret"
```

#### **Via Database Query**
```sql
SELECT COUNT(*) as pending_count 
FROM app_registrations 
WHERE status = 'PENDING_APPROVAL';
```

### **2. Approving Applications**

#### **Via Slack (One-Click)**
1. Click **✅ Approve** button in Slack
2. Confirm the approval
3. System automatically:
   - Generates secure API key
   - Creates record in `applications` table
   - Updates registration status to `APPROVED`
   - Sends confirmation to admin

#### **Via API Endpoint**
```bash
curl -X POST "https://your-domain.com/api/applications/approve" \
  -H "Content-Type: application/json" \
  -d '{
    "appId": "my-awesome-app",
    "action": "APPROVE",
    "adminNotes": "Approved after review",
    "adminSecret": "your-admin-secret"
  }'
```

### **3. Rejecting Applications**

#### **Via Slack (One-Click)**
1. Click **❌ Reject** button in Slack
2. Confirm the rejection
3. System automatically:
   - Updates registration status to `REJECTED`
   - Records rejection reason
   - Sends confirmation to admin

#### **Via API Endpoint**
```bash
curl -X POST "https://your-domain.com/api/applications/approve" \
  -H "Content-Type: application/json" \
  -d '{
    "appId": "my-awesome-app",
    "action": "REJECT",
    "reason": "Insufficient business information",
    "adminSecret": "your-admin-secret"
  }'
```

### **4. Managing Active Applications**

#### **View Application Details**
```bash
curl -X GET "https://your-domain.com/api/applications?apiKey=app-api-key&appId=my-app"
```

#### **Update Application Configuration**
```bash
curl -X PUT "https://your-domain.com/api/applications" \
  -H "Content-Type: application/json" \
  -d '{
    "apiKey": "app-api-key",
    "appId": "my-app",
    "defaultProvider": "resend",
    "maxRetries": 5
  }'
```

#### **Revoke Application Access**
```bash
curl -X DELETE "https://your-domain.com/api/applications" \
  -H "Content-Type: application/json" \
  -d '{
    "appId": "my-app",
    "adminSecret": "your-admin-secret"
  }'
```

### **5. Slack Bot Commands**

#### **Check Pending Registrations**
```
@Notification Admin pending
@Notification Admin registrations
```

#### **Get Help**
```
@Notification Admin help
```

### **6. Monitoring System Health**

#### **Check API Health**
```bash
curl "https://your-domain.com/api/health"
```

#### **Check Database Status**
```bash
cd apps/api
npx prisma studio
```

---

## 🔧 **Troubleshooting**

### **Common Issues & Solutions**

#### **1. Slack Bot Not Responding**

**Symptoms:**
- Bot doesn't respond to mentions
- No notifications in admin channel
- Buttons not working

**Solutions:**
1. Check bot token is correct in `.env`
2. Verify bot is installed to workspace
3. Check bot has proper scopes
4. Verify request URLs are accessible from internet
5. Check Slack app settings for interactivity and events

#### **2. Database Connection Issues**

**Symptoms:**
- API returns 500 errors
- Prisma commands fail
- Database queries timeout

**Solutions:**
1. Verify `DATABASE_URL` in `.env`
2. Check database server is running
3. Verify network connectivity
4. Check database user permissions
5. Run `npx prisma db push` to sync schema

#### **3. API Key Validation Failing**

**Symptoms:**
- Applications get 401 errors
- API key validation fails
- Authentication errors

**Solutions:**
1. Check API key is active in database
2. Verify API key hasn't expired
3. Check app is active in applications table
4. Verify API key permissions
5. Check API key hash in database

#### **4. Notifications Not Sending**

**Symptoms:**
- Notifications stuck in pending
- Delivery status shows failed
- Provider errors

**Solutions:**
1. Check provider API keys are valid
2. Verify provider configurations
3. Check rate limits and quotas
4. Review delivery status logs
5. Test provider connectivity

### **Debug Commands**

#### **Check Slack Integration Status**
```bash
# Test webhook endpoint
curl -X POST "https://your-domain.com/api/slack/webhook" \
  -H "Content-Type: application/json" \
  -d '{"type": "TEST"}'

# Check Slack bot token
curl -H "Authorization: Bearer YOUR_BOT_TOKEN" \
  "https://slack.com/api/auth.test"
```

#### **Check Database Schema**
```bash
cd apps/api
npx prisma db pull
npx prisma generate
npx prisma studio
```

#### **Check Environment Variables**
```bash
cd apps/api
node -e "console.log(process.env.SLACK_BOT_TOKEN ? 'Slack configured' : 'Slack not configured')"
node -e "console.log(process.env.DATABASE_URL ? 'Database configured' : 'Database not configured')"
```

---

## 🔒 **Security & Best Practices**

### **1. Admin Secret Management**

#### **Generate Strong Admin Secret**
```bash
# Generate a secure random string
openssl rand -base64 32

# Or use a password generator
# Minimum 32 characters, mix of letters, numbers, symbols
```

#### **Rotate Admin Secret Regularly**
1. Generate new admin secret
2. Update `.env` file
3. Update any automated scripts
4. Test admin endpoints with new secret

### **2. API Key Security**

#### **Key Rotation**
- Applications can have multiple API keys
- Rotate keys regularly (every 90 days)
- Use different keys for different environments
- Monitor key usage patterns

#### **Permission Management**
- Grant minimum required permissions
- Use read-only keys for analytics
- Use send-only keys for notifications
- Monitor permission changes

### **3. Database Security**

#### **Connection Security**
- Use SSL connections to database
- Restrict database access to API server only
- Use connection pooling
- Monitor database connections

#### **Data Protection**
- Encrypt sensitive data at rest
- Hash all API keys
- Log all admin actions
- Regular security audits

### **4. Slack Security**

#### **Bot Token Security**
- Keep bot token secure
- Rotate bot token if compromised
- Monitor bot activity
- Restrict bot to admin channels only

#### **Webhook Security**
- Use webhook secrets
- Verify Slack signatures
- Monitor webhook usage
- Rate limit webhook endpoints

### **5. Monitoring & Alerting**

#### **System Monitoring**
- Monitor API response times
- Track notification delivery rates
- Monitor database performance
- Alert on system failures

#### **Security Monitoring**
- Monitor failed authentication attempts
- Track API key usage patterns
- Alert on suspicious activity
- Log all admin actions

---

## 📚 **Reference Materials**

### **API Endpoints**

#### **Application Management**
- `POST /api/applications` - Submit new registration
- `GET /api/applications` - Get app configuration
- `PUT /api/applications` - Update app configuration
- `DELETE /api/applications` - Revoke app access

#### **Admin Operations**
- `POST /api/applications/approve` - Approve/reject applications
- `GET /api/applications/approve` - Get pending registrations

#### **Slack Integration**
- `POST /api/slack/events` - Handle Slack events
- `POST /api/slack/interactive` - Handle button clicks
- `POST /api/slack/webhook` - Send notifications to Slack

### **Environment Variables Reference**

| Variable | Description | Required | Example |
|----------|-------------|----------|---------|
| `DATABASE_URL` | PostgreSQL connection string | Yes | `postgresql://user:pass@localhost:5432/db` |
| `ADMIN_SECRET` | Secret for admin operations | Yes | `your-secure-secret` |
| `ADMIN_EMAIL` | Admin email for notifications | Yes | `<EMAIL>` |
| `SLACK_BOT_TOKEN` | Slack bot OAuth token | Yes | `xoxb-your-token` |
| `SLACK_ADMIN_CHANNEL` | Admin channel for notifications | Yes | `#admin` |
| `SLACK_WEBHOOK_SECRET` | Secret for webhook validation | Yes | `your-webhook-secret` |

### **Database Tables Reference**

| Table | Purpose | Key Fields | Relations |
|-------|---------|------------|-----------|
| `applications` | Active applications | `app_id` (PK) | Main table |
| `app_registrations` | Registration requests | `app_id` (FK) | Approval workflow |
| `app_api_keys` | API key management | `app_id` (FK) | Authentication |
| `users` | User management | `id` (PK) | User data |
| `notifications` | Notification tracking | `id` (PK), `app_id` (FK) | Notification data |
| `delivery_status` | Delivery tracking | `notification_id` (FK) | Delivery status |

---

## 🎉 **Getting Started Checklist**

### **Initial Setup**
- [ ] Environment variables configured
- [ ] Database created and migrated
- [ ] Slack app created and configured
- [ ] Bot installed to workspace
- [ ] Interactivity enabled
- [ ] Event subscriptions enabled
- [ ] Bot added to admin channel

### **First Application**
- [ ] Submit test application registration
- [ ] Verify Slack notification received
- [ ] Test approve button in Slack
- [ ] Verify application activated
- [ ] Test API key generation
- [ ] Verify application can use system

### **Daily Operations**
- [ ] Monitor admin channel for new registrations
- [ ] Review and approve/reject applications
- [ ] Monitor system health
- [ ] Check for any errors or issues
- [ ] Review system metrics

---

## 🆘 **Support & Contact**

### **When You Need Help**
1. **Check this documentation** first
2. **Review troubleshooting section** above
3. **Check system logs** for error details
4. **Test with simple examples** to isolate issues
5. **Contact development team** with specific error details

### **Useful Commands for Support**
```bash
# Check system status
curl "https://your-domain.com/api/health"

# Check database schema
cd apps/api && npx prisma studio

# Check environment
cd apps/api && node -e "console.log(Object.keys(process.env).filter(k => k.includes('SLACK') || k.includes('DATABASE')))"

# Check Slack integration
curl -H "Authorization: Bearer $SLACK_BOT_TOKEN" "https://slack.com/api/auth.test"
```

---

**🎯 This admin guide covers everything you need to manage the notification system effectively. Bookmark it for future reference!** 