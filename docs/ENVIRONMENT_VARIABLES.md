# 🔧 **Environment Variables Reference**

## 📋 **Complete List of Environment Variables**

This document lists all environment variables used in the notification system, their purposes, and where they're used in the code.

---

## 🏗️ **Core Configuration**

### **NODE_ENV**
- **Purpose**: Node.js environment (development/production)
- **Default**: `development`
- **Used in**: `lib/db.ts`, `next.config.js`
- **Example**: `NODE_ENV="production"`

### **PORT**
- **Purpose**: Server port number
- **Default**: `3000`
- **Used in**: `api/health/route.ts`
- **Example**: `PORT=3000`

### **NEXT_PUBLIC_BASE_URL**
- **Purpose**: Base URL for webhooks and links
- **Default**: `http://localhost:3000`
- **Used in**: Multiple API routes for generating URLs
- **Example**: `NEXT_PUBLIC_BASE_URL="https://yourdomain.com"`

---

## 🗄️ **Database Configuration**

### **DATABASE_URL**
- **Purpose**: PostgreSQL connection string
- **Required**: ✅ **YES**
- **Used in**: `lib/db.ts`, `api/health/route.ts`
- **Example**: `DATABASE_URL="postgresql://user:pass@localhost:5432/notifications"`

### **DATABASE_TIMEOUT_MS**
- **Purpose**: Database connection timeout
- **Default**: `5000`
- **Used in**: `next.config.js`
- **Example**: `DATABASE_TIMEOUT_MS=5000`

### **EXTERNAL_API_TIMEOUT_MS**
- **Purpose**: External API request timeout
- **Default**: `10000`
- **Used in**: `next.config.js`
- **Example**: `EXTERNAL_API_TIMEOUT_MS=10000`

### **TRANSACTION_TIMEOUT_MS**
- **Purpose**: Database transaction timeout
- **Default**: `30000`
- **Used in**: `next.config.js`
- **Example**: `TRANSACTION_TIMEOUT_MS=30000`

---

## 🔒 **Security & Administration**

### **ADMIN_SECRET**
- **Purpose**: Secret for admin operations (approve/reject apps)
- **Required**: ✅ **YES**
- **Used in**: `api/applications/approve/route.ts`, `api/applications/route.ts`
- **Example**: `ADMIN_SECRET="your-super-secure-admin-secret"`

### **ADMIN_EMAIL**
- **Purpose**: Admin email for notifications
- **Required**: ✅ **YES**
- **Used in**: Multiple API routes for admin notifications
- **Example**: `ADMIN_EMAIL="<EMAIL>"`

---

## 📧 **Notification Providers**

### **NOVU_API_KEY**
- **Purpose**: Novu API key for push notifications
- **Required**: ❌ **NO** (optional, but recommended)
- **Used in**: `services/notificationService.ts`, multiple API routes
- **Example**: `NOVU_API_KEY="your-novu-api-key"`

### **NOVU_APP_ID**
- **Purpose**: Novu application ID
- **Required**: ❌ **NO** (optional, but recommended)
- **Used in**: Multiple API routes for Novu integration
- **Example**: `NOVU_APP_ID="your-novu-app-id"`

### **RESEND_API_KEY**
- **Purpose**: Resend API key for email notifications
- **Required**: ❌ **NO** (optional, but recommended)
- **Used in**: `services/notificationService.ts`, multiple API routes
- **Example**: `RESEND_API_KEY="your-resend-api-key"`

### **RESEND_FROM_EMAIL**
- **Purpose**: Default sender email for Resend
- **Required**: ❌ **NO** (optional)
- **Used in**: Multiple API routes for email notifications
- **Example**: `RESEND_FROM_EMAIL="<EMAIL>"`

### **TWILIO_ACCOUNT_SID**
- **Purpose**: Twilio account SID for SMS
- **Required**: ❌ **NO** (optional)
- **Used in**: `services/notificationService.ts`, `api/health/route.ts`
- **Example**: `TWILIO_ACCOUNT_SID="your-twilio-account-sid"`

### **TWILIO_AUTH_TOKEN**
- **Purpose**: Twilio authentication token
- **Required**: ❌ **NO** (optional)
- **Used in**: `services/notificationService.ts`, `api/health/route.ts`
- **Example**: `TWILIO_AUTH_TOKEN="your-twilio-auth-token"`

---

## 🔗 **Slack Integration**

### **SLACK_BOT_TOKEN**
- **Purpose**: Slack bot OAuth token for admin notifications
- **Required**: ❌ **NO** (optional, but recommended for admin workflow)
- **Used in**: `api/slack/events/route.ts`, `api/slack/interactive/route.ts`, `api/slack/webhook/route.ts`
- **Example**: `SLACK_BOT_TOKEN="xoxb-your-bot-token"`

### **SLACK_ADMIN_CHANNEL**
- **Purpose**: Admin channel for Slack notifications
- **Required**: ❌ **NO** (optional)
- **Default**: `#admin`
- **Used in**: `api/slack/webhook/route.ts`
- **Example**: `SLACK_ADMIN_CHANNEL="#admin"`

### **SLACK_WEBHOOK_SECRET**
- **Purpose**: Secret for validating Slack webhook requests
- **Required**: ❌ **NO** (optional)
- **Used in**: `api/slack/webhook/route.ts`
- **Example**: `SLACK_WEBHOOK_SECRET="your-webhook-secret"`

---

## 🚀 **Performance & Server**

### **MAX_MEMORY_USAGE**
- **Purpose**: Maximum memory usage for server
- **Default**: `512MB`
- **Used in**: `next.config.js`
- **Example**: `MAX_MEMORY_USAGE="512MB"`

### **REQUEST_TIMEOUT**
- **Purpose**: Request timeout in milliseconds
- **Default**: `30000`
- **Used in**: `next.config.js`
- **Example**: `REQUEST_TIMEOUT=30000`

### **MAX_CONNECTIONS**
- **Purpose**: Maximum database connections
- **Default**: `100`
- **Used in**: `next.config.js`
- **Example**: `MAX_CONNECTIONS=100`

---

## 🎮 **Development & Demo**

### **NEXT_PUBLIC_ADMIN_SECRET**
- **Purpose**: Admin secret for demo mode (client-side accessible)
- **Required**: ❌ **NO** (optional, for development)
- **Used in**: `app/demo/page.tsx`
- **Example**: `NEXT_PUBLIC_ADMIN_SECRET="demo-secret"`

---

## 📊 **Environment Variables Summary**

### **Required Variables (Must Set)**
1. `DATABASE_URL` - PostgreSQL connection
2. `ADMIN_SECRET` - Admin authentication
3. `ADMIN_EMAIL` - Admin notifications

### **Highly Recommended**
1. `SLACK_BOT_TOKEN` - Slack admin workflow
2. `SLACK_ADMIN_CHANNEL` - Admin notifications
3. `RESEND_API_KEY` - Email confirmations
4. `RESEND_FROM_EMAIL` - Sender email

### **Optional but Useful**
1. `NOVU_API_KEY` - Push notifications
2. `NOVU_APP_ID` - Novu integration
3. `TWILIO_ACCOUNT_SID` - SMS notifications
4. `TWILIO_AUTH_TOKEN` - Twilio authentication

### **Development Only**
1. `NEXT_PUBLIC_ADMIN_SECRET` - Demo mode
2. `NODE_ENV` - Set to "production" in production

---

## 🔧 **Setup Recommendations**

### **Production Environment**
```bash
# Required
DATABASE_URL="********************************/db"
ADMIN_SECRET="very-long-random-string"
ADMIN_EMAIL="<EMAIL>"

# Highly Recommended
SLACK_BOT_TOKEN="xoxb-your-bot-token"
SLACK_ADMIN_CHANNEL="#admin"
RESEND_API_KEY="re_your-resend-key"
RESEND_FROM_EMAIL="<EMAIL>"

# Optional
NOVU_API_KEY="your-novu-key"
NOVU_APP_ID="your-novu-app-id"
TWILIO_ACCOUNT_SID="your-twilio-sid"
TWILIO_AUTH_TOKEN="your-twilio-token"
NODE_ENV="production"
NEXT_PUBLIC_BASE_URL="https://yourdomain.com"
```

### **Development Environment**
```bash
# Required
DATABASE_URL="postgresql://user:pass@localhost:5432/notifications"
ADMIN_SECRET="dev-secret-123"
ADMIN_EMAIL="<EMAIL>"

# Optional
NODE_ENV="development"
NEXT_PUBLIC_BASE_URL="http://localhost:3000"
NEXT_PUBLIC_ADMIN_SECRET="demo-secret"
```

---

## 🚨 **Security Notes**

### **Never Expose in Client Code**
- `ADMIN_SECRET`
- `DATABASE_URL`
- `SLACK_BOT_TOKEN`
- `RESEND_API_KEY`
- `TWILIO_AUTH_TOKEN`

### **Safe for Client Code (NEXT_PUBLIC_*)**
- `NEXT_PUBLIC_BASE_URL`
- `NEXT_PUBLIC_ADMIN_SECRET` (demo only)

### **Environment-Specific**
- `NODE_ENV` - Set to "production" in production
- `DATABASE_URL` - Use different databases for dev/prod
- `ADMIN_SECRET` - Use strong, unique secrets in production

---

## 📝 **Usage Examples**

### **Checking if Variable is Set**
```typescript
if (process.env.SLACK_BOT_TOKEN) {
  // Slack integration is configured
}
```

### **Using Default Values**
```typescript
const adminChannel = process.env.SLACK_ADMIN_CHANNEL || '#admin';
const port = process.env.PORT || 3000;
```

### **Required Variable Check**
```typescript
if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL is required');
}
```

---

## 🔍 **Code Locations**

### **Database & Core**
- `lib/db.ts` - Database connection and logging
- `next.config.js` - Server configuration and timeouts

### **API Routes**
- `api/health/route.ts` - Health check and status
- `api/applications/route.ts` - Application management
- `api/applications/approve/route.ts` - Admin approval
- `api/slack/events/route.ts` - Slack event handling
- `api/slack/interactive/route.ts` - Slack button interactions
- `api/slack/webhook/route.ts` - Slack webhook processing

### **Services**
- `services/notificationService.ts` - Notification logic

### **Components**
- `app/demo/page.tsx` - Demo page functionality

---

**🎯 This reference covers all environment variables actually used in the notification system code. No documentation examples or unused variables included!** 