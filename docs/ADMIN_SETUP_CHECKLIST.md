# ✅ **Admin Setup Checklist**

## 🎯 **Complete Setup Guide for New Admins**

Follow this checklist step-by-step to set up the notification system for the first time.

---

## 📋 **Pre-Setup Requirements**

- [ ] **PostgreSQL database** access
- [ ] **Slack workspace** admin permissions
- [ ] **Domain/URL** for your API
- [ ] **Email provider** (Resend, SendGrid, etc.)
- [ ] **SMS provider** (Twilio, etc.) - optional
- [ ] **Push notification provider** (Novu, etc.) - optional

---

## 🗄️ **Step 1: Database Setup**

### **1.1 Create Database**
```bash
# Connect to PostgreSQL
psql -U postgres

# Create database
CREATE DATABASE notifications;

# Create user (if needed)
CREATE USER notification_user WITH PASSWORD 'secure_password';

# Grant permissions
GRANT ALL PRIVILEGES ON DATABASE notifications TO notification_user;

# Exit
\q
```

### **1.2 Verify Database Connection**
```bash
# Test connection
psql -U notification_user -d notifications -h localhost
# Should connect successfully
\q
```

**✅ Database setup complete**

---

## 🔧 **Step 2: Environment Configuration**

### **2.1 Create Environment File**
```bash
# Navigate to API directory
cd apps/api

# Copy example environment file
cp env.example .env
```

### **2.2 Configure Database**
```bash
# Edit .env file
DATABASE_URL="postgresql://notification_user:secure_password@localhost:5432/notifications"
```

### **2.3 Configure Admin Settings**
```bash
# Generate secure admin secret
openssl rand -base64 32

# Add to .env
ADMIN_SECRET="generated-secret-here"
ADMIN_EMAIL="<EMAIL>"
```

**✅ Environment configuration complete**

---

## 🔗 **Step 3: Slack App Setup**

### **3.1 Create Slack App**
- [ ] Go to [https://api.slack.com/apps](https://api.slack.com/apps)
- [ ] Click **"Create New App"**
- [ ] Choose **"From scratch"**
- [ ] Name: `Notification System Admin`
- [ ] Choose your workspace

### **3.2 Configure Bot Token Scopes**
- [ ] Go to **"OAuth & Permissions"**
- [ ] Under **"Bot Token Scopes"**, add:
  - [ ] `chat:write`
  - [ ] `im:write`
  - [ ] `channels:read`
  - [ ] `channels:history`

### **3.3 Install App to Workspace**
- [ ] Go to **"Install App"**
- [ ] Click **"Install to Workspace"**
- [ ] Authorize the app
- [ ] **Copy the "Bot User OAuth Token"** (starts with `xoxb-`)

### **3.4 Enable Interactivity**
- [ ] Go to **"Interactivity & Shortcuts"**
- [ ] Toggle **"Interactivity"** to **On**
- [ ] Set **"Request URL"** to: `https://your-domain.com/api/slack/interactive`
- [ ] Click **"Save Changes"**

### **3.5 Enable Event Subscriptions**
- [ ] Go to **"Event Subscriptions"**
- [ ] Toggle **"Enable Events"** to **On**
- [ ] Set **"Request URL"** to: `https://your-domain.com/api/slack/events`
- [ ] Under **"Subscribe to bot events"**, add: `app_mention`
- [ ] Click **"Save Changes"**

### **3.6 Add Bot to Admin Channel**
- [ ] Go to your admin channel (e.g., `#admin`)
- [ ] Type: `/invite @Notification System Admin`
- [ ] Verify bot appears in channel

**✅ Slack app setup complete**

---

## 🔑 **Step 4: Provider Configuration**

### **4.1 Email Provider (Resend)**
- [ ] Sign up at [resend.com](https://resend.com)
- [ ] Get API key
- [ ] Add to `.env`:
  ```bash
  RESEND_API_KEY="re_123456789"
  RESEND_FROM_EMAIL="<EMAIL>"
  ```

### **4.2 SMS Provider (Twilio) - Optional**
- [ ] Sign up at [twilio.com](https://twilio.com)
- [ ] Get Account SID and Auth Token
- [ ] Add to `.env`:
  ```bash
  TWILIO_ACCOUNT_SID="AC123456789"
  TWILIO_AUTH_TOKEN="your-auth-token"
  ```

### **4.3 Push Provider (Novu) - Optional**
- [ ] Sign up at [novu.co](https://novu.co)
- [ ] Create app and get API key
- [ ] Add to `.env`:
  ```bash
  NOVU_API_KEY="your-novu-api-key"
  NOVU_APP_ID="your-novu-app-id"
  ```

**✅ Provider configuration complete**

---

## 🚀 **Step 5: System Deployment**

### **5.1 Install Dependencies**
```bash
# Navigate to API directory
cd apps/api

# Install dependencies
npm install
```

### **5.2 Run Database Migrations**
```bash
# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate dev --name initial_setup

# Verify database schema
npx prisma studio
```

### **5.3 Test Database Connection**
```bash
# Test connection
npx prisma db pull
# Should complete without errors
```

**✅ System deployment complete**

---

## 🧪 **Step 6: Testing & Verification**

### **6.1 Test Slack Integration**
```bash
# Test bot token
curl -H "Authorization: Bearer YOUR_BOT_TOKEN" "https://slack.com/api/auth.test"
# Should return success response
```

### **6.2 Test System Health**
```bash
# Start the API server
npm run dev

# Test health endpoint
curl "http://localhost:3000/api/health"
# Should return healthy status
```

### **6.3 Test App Registration**
```bash
# Submit test application
curl -X POST "http://localhost:3000/api/applications" \
  -H "Content-Type: application/json" \
  -d '{
    "appId": "test-app",
    "name": "Test Application",
    "contactEmail": "<EMAIL>",
    "contactName": "Test User"
  }'
# Should return success and send Slack notification
```

### **6.4 Test Slack Approval**
- [ ] Check admin channel for notification
- [ ] Click **✅ Approve** button
- [ ] Verify application is approved
- [ ] Copy generated API key

**✅ Testing and verification complete**

---

## 🔒 **Step 7: Security & Production**

### **7.1 Update Environment for Production**
```bash
# Update .env for production
NODE_ENV="production"
NEXT_PUBLIC_BASE_URL="https://your-domain.com"
```

### **7.2 Secure Admin Secret**
- [ ] Generate new production admin secret
- [ ] Update `.env` file
- [ ] Store securely (password manager)

### **7.3 SSL/HTTPS Setup**
- [ ] Configure SSL certificate
- [ ] Update Slack webhook URLs to HTTPS
- [ ] Test all endpoints with HTTPS

### **7.4 Monitoring Setup**
- [ ] Set up logging
- [ ] Configure error monitoring
- [ ] Set up health checks

**✅ Security and production setup complete**

---

## 📚 **Step 8: Documentation & Training**

### **8.1 Admin Training**
- [ ] Review [ADMIN_GUIDE.md](ADMIN_GUIDE.md)
- [ ] Review [ADMIN_QUICK_REFERENCE.md](ADMIN_QUICK_REFERENCE.md)
- [ ] Practice approval/rejection workflow
- [ ] Test all Slack commands

### **8.2 Team Setup**
- [ ] Add other admins to Slack channel
- [ ] Train team on approval workflow
- [ ] Document team procedures
- [ ] Set up escalation procedures

### **8.3 Maintenance Schedule**
- [ ] Schedule regular security reviews
- [ ] Plan admin secret rotation
- [ ] Set up backup procedures
- [ ] Document maintenance tasks

**✅ Documentation and training complete**

---

## 🎉 **Setup Complete!**

### **What You Now Have:**
✅ **Fully functional notification system**
✅ **Slack-powered admin workflow**
✅ **Secure API key management**
✅ **Multi-provider notification support**
✅ **Complete admin documentation**

### **Daily Operations:**
1. **Monitor admin channel** for new registrations
2. **Click approve/reject buttons** in Slack
3. **Share API keys** with approved developers
4. **Monitor system health** and performance

### **Next Steps:**
- [ ] **Start accepting real applications**
- [ ] **Monitor system performance**
- [ ] **Gather feedback from developers**
- [ ] **Plan future enhancements**

---

## 🆘 **Need Help?**

### **Documentation:**
- [ADMIN_GUIDE.md](ADMIN_GUIDE.md) - Complete admin guide
- [ADMIN_QUICK_REFERENCE.md](ADMIN_QUICK_REFERENCE.md) - Quick commands
- [SLACK_INTEGRATION_SETUP.md](../SLACK_INTEGRATION_SETUP.md) - Slack setup details

### **Common Issues:**
- Check troubleshooting section in [ADMIN_GUIDE.md](ADMIN_GUIDE.md)
- Verify all environment variables are set
- Test Slack integration step by step
- Check database connectivity

### **Support:**
- Review error logs for specific issues
- Test with simple examples to isolate problems
- Contact development team with error details

---

**🎯 Congratulations! You're now ready to manage the notification system effectively!** 