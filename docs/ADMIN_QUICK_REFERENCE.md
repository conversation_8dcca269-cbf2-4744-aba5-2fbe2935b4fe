# 🚀 **Admin Quick Reference**

## 📱 **Daily Operations (Slack)**

### **Check Pending Applications**
```
@Notification Admin pending
@Notification Admin registrations
```

### **Get Help**
```
@Notification Admin help
```

### **Approve Application**
1. Click **✅ Approve** button in Slack
2. Confirm approval
3. <PERSON><PERSON> generated API key for developer

### **Reject Application**
1. Click **❌ Reject** button in Slack
2. Confirm rejection
3. Provide reason if needed

### **View Application Details**
1. Click **👁️ View Details** button in Slack
2. Review full application information

---

## 🔧 **Quick Commands**

### **Check System Health**
```bash
curl "https://your-domain.com/api/health"
```

### **View Pending Registrations**
```bash
curl -X GET "https://your-domain.com/api/applications/approve?adminSecret=YOUR_SECRET"
```

### **Approve via API**
```bash
curl -X POST "https://your-domain.com/api/applications/approve" \
  -H "Content-Type: application/json" \
  -d '{
    "appId": "app-id-here",
    "action": "APPROVE",
    "adminSecret": "YOUR_SECRET"
  }'
```

### **Reject via API**
```bash
curl -X POST "https://your-domain.com/api/applications/approve" \
  -H "Content-Type: application/json" \
  -d '{
    "appId": "app-id-here",
    "action": "REJECT",
    "reason": "Reason for rejection",
    "adminSecret": "YOUR_SECRET"
  }'
```

---

## 🗄️ **Database Queries**

### **View Active Applications**
```sql
SELECT app_id, name, default_provider, is_active, created_at 
FROM applications 
WHERE is_active = true 
ORDER BY created_at DESC;
```

### **View Pending Registrations**
```sql
SELECT app_id, name, contact_email, contact_name, created_at 
FROM app_registrations 
WHERE status = 'PENDING_APPROVAL' 
ORDER BY created_at ASC;
```

### **View API Key Usage**
```sql
SELECT ak.name, ak.app_id, a.name as app_name, ak.last_used_at 
FROM app_api_keys ak 
JOIN applications a ON ak.app_id = a.app_id 
ORDER BY ak.last_used_at DESC NULLS LAST;
```

### **Count Pending Applications**
```sql
SELECT COUNT(*) as pending_count 
FROM app_registrations 
WHERE status = 'PENDING_APPROVAL';
```

---

## 🔑 **Environment Variables**

| Variable | Purpose | Example |
|----------|---------|---------|
| `ADMIN_SECRET` | Admin operations | `your-secure-secret` |
| `SLACK_BOT_TOKEN` | Slack bot access | `xoxb-your-token` |
| `SLACK_ADMIN_CHANNEL` | Admin notifications | `#admin` |
| `DATABASE_URL` | Database connection | `postgresql://user:pass@localhost:5432/db` |

---

## 🚨 **Troubleshooting**

### **Slack Bot Not Working**
- Check bot token in `.env`
- Verify bot is in admin channel
- Check Slack app settings

### **Database Issues**
- Verify `DATABASE_URL` in `.env`
- Check database server is running
- Run `npx prisma studio` to verify

### **API Key Issues**
- Check key is active in database
- Verify app is active
- Check key permissions

---

## 📞 **Support Commands**

### **Test Slack Integration**
```bash
curl -H "Authorization: Bearer $SLACK_BOT_TOKEN" "https://slack.com/api/auth.test"
```

### **Check Database Schema**
```bash
cd apps/api && npx prisma studio
```

### **Verify Environment**
```bash
cd apps/api && node -e "console.log('Slack:', !!process.env.SLACK_BOT_TOKEN, 'DB:', !!process.env.DATABASE_URL)"
```

---

**💡 Bookmark this for quick access during daily operations!** 