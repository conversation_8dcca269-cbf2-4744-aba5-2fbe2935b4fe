# Spark Strand Notifications System - Integration Guide

This guide provides comprehensive instructions for integrating the Spark Strand Notifications System into your applications.

## Table of Contents

1. [Overview](#overview)
2. [Quick Start](#quick-start)
3. [React/Next.js Integration](#reactnextjs-integration)
4. [React Native Integration](#react-native-integration)
5. [Electron Integration](#electron-integration)
6. [API Reference](#api-reference)
7. [Configuration](#configuration)
8. [Advanced Usage](#advanced-usage)
9. [Troubleshooting](#troubleshooting)

## Overview

The Spark Strand Notifications System is a vendor-agnostic notifications platform that wraps Novu while providing the flexibility to add custom providers and bypass Novu when needed. It consists of:

- **Core Package**: Shared types, interfaces, and utilities
- **Client Packages**: Platform-specific implementations (React, React Native, Electron)
- **API Server**: Next.js-based backend that manages notifications and integrates with providers

## Quick Start

### 1. Install Dependencies

Choose the appropriate package for your platform:

```bash
# For React/Next.js applications
npm install @sparkstrand/notifications-react

# For React Native applications
npm install @sparkstrand/notifications-react-native

# For Electron applications
npm install @sparkstrand/notifications-electron
```

### 2. Configure the System

```typescript
import { NotificationsProvider } from '@sparkstrand/notifications-react';

const config = {
  apiUrl: 'https://your-api-domain.com/api',
  appId: 'your-app-id',
  userId: 'user-123',
};

function App() {
  return (
    <NotificationsProvider config={config}>
      <YourApp />
    </NotificationsProvider>
  );
}
```

### 3. Send Your First Notification

```typescript
import { useNotifications } from '@sparkstrand/notifications-react';

function NotificationButton() {
  const { sendNotification } = useNotifications();

  const handleSendNotification = async () => {
    await sendNotification({
      title: 'Hello World!',
      message: 'This is your first notification',
      type: 'INFO',
      priority: 'NORMAL',
      channels: ['inApp', 'email'],
      recipientId: 'user-123',
    });
  };

  return <button onClick={handleSendNotification}>Send Notification</button>;
}
```

## React/Next.js Integration

### Basic Setup

```typescript
// _app.tsx or layout.tsx
import { NotificationsProvider } from '@sparkstrand/notifications-react';

const config = {
  apiUrl: process.env.NEXT_PUBLIC_NOTIFICATIONS_API_URL,
  appId: process.env.NEXT_PUBLIC_APP_ID,
  userId: getCurrentUserId(), // Your user ID logic
};

export default function RootLayout({ children }) {
  return (
    <NotificationsProvider config={config}>
      {children}
    </NotificationsProvider>
  );
}
```

### Using Hooks

```typescript
import { useNotifications, useNotificationPreferences } from '@sparkstrand/notifications-react';

function NotificationCenter() {
  const { 
    notifications, 
    sendNotification, 
    markAsRead, 
    deleteNotification,
    isLoading,
    error 
  } = useNotifications();

  const { preferences, updatePreferences } = useNotificationPreferences();

  // Send a notification
  const handleSend = async () => {
    try {
      await sendNotification({
        title: 'Task Completed',
        message: 'Your background task has finished processing',
        type: 'SUCCESS',
        priority: 'HIGH',
        channels: ['inApp', 'email'],
        recipientId: 'user-123',
        metadata: {
          taskId: 'task-456',
          completionTime: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error('Failed to send notification:', error);
    }
  };

  // Mark notification as read
  const handleMarkAsRead = async (notificationId: string) => {
    await markAsRead(notificationId);
  };

  return (
    <div>
      <button onClick={handleSend}>Send Test Notification</button>
      
      {isLoading && <div>Loading...</div>}
      {error && <div>Error: {error.message}</div>}
      
      <div>
        {notifications.map(notification => (
          <div key={notification.id}>
            <h3>{notification.title}</h3>
            <p>{notification.message}</p>
            <button onClick={() => handleMarkAsRead(notification.id)}>
              Mark as Read
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
```

### Using Components

```typescript
import { 
  NotificationCenter, 
  NotificationBadge, 
  NotificationPreferences 
} from '@sparkstrand/notifications-react';

function Header() {
  return (
    <header>
      <h1>My App</h1>
      <NotificationBadge />
      <NotificationCenter />
    </header>
  );
}

function Settings() {
  return (
    <div>
      <h2>Notification Settings</h2>
      <NotificationPreferences />
    </div>
  );
}
```

## React Native Integration

### Basic Setup

```typescript
// App.tsx
import { NotificationsProvider } from '@sparkstrand/notifications-react-native';

const config = {
  apiUrl: 'https://your-api-domain.com/api',
  appId: 'your-app-id',
  userId: 'user-123',
  enablePushNotifications: true,
};

export default function App() {
  return (
    <NotificationsProvider config={config}>
      <YourApp />
    </NotificationsProvider>
  );
}
```

### Push Notifications

```typescript
import { usePushNotifications } from '@sparkstrand/notifications-react-native';

function PushNotificationSetup() {
  const { 
    registerDevice, 
    unregisterDevice, 
    enablePushNotifications,
    deviceToken,
    isRegistered 
  } = usePushNotifications();

  useEffect(() => {
    // Register for push notifications when component mounts
    registerDevice();
  }, []);

  const handleEnablePush = async () => {
    try {
      await enablePushNotifications();
      console.log('Push notifications enabled');
    } catch (error) {
      console.error('Failed to enable push notifications:', error);
    }
  };

  return (
    <View>
      <Text>Device Token: {deviceToken || 'Not registered'}</Text>
      <Text>Status: {isRegistered ? 'Registered' : 'Not registered'}</Text>
      <Button title="Enable Push Notifications" onPress={handleEnablePush} />
    </View>
  );
}
```

### Using Mobile-Specific Features

```typescript
import { useNotifications } from '@sparkstrand/notifications-react-native';

function MobileNotificationCenter() {
  const { notifications, sendNotification } = useNotifications();

  const handleSendMobileNotification = async () => {
    await sendNotification({
      title: 'Mobile Notification',
      message: 'This notification is optimized for mobile',
      type: 'INFO',
      priority: 'NORMAL',
      channels: ['push', 'inApp'],
      recipientId: 'user-123',
      metadata: {
        platform: 'mobile',
        deviceType: 'phone',
      },
    });
  };

  return (
    <ScrollView>
      <TouchableOpacity onPress={handleSendMobileNotification}>
        <Text>Send Mobile Notification</Text>
      </TouchableOpacity>
      
      {notifications.map(notification => (
        <View key={notification.id} style={styles.notificationItem}>
          <Text style={styles.title}>{notification.title}</Text>
          <Text style={styles.message}>{notification.message}</Text>
          <Text style={styles.timestamp}>
            {new Date(notification.createdAt).toLocaleString()}
          </Text>
        </View>
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  notificationItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  timestamp: {
    fontSize: 12,
    color: '#999',
  },
});
```

## Electron Integration

### Main Process Setup

```typescript
// main.ts
import { app, BrowserWindow } from 'electron';
import { ElectronMainProcessNotificationManager } from '@sparkstrand/notifications-electron';

let notificationManager: ElectronMainProcessNotificationManager;

app.whenReady().then(async () => {
  // Initialize notification manager
  notificationManager = new ElectronMainProcessNotificationManager({
    apiUrl: 'https://your-api-domain.com/api',
    appId: 'your-app-id',
    enableSystemNotifications: true,
    enableTrayNotifications: true,
    enableSound: true,
    notificationTimeout: 5000,
    maxNotifications: 10,
  });

  await notificationManager.initialize();

  // Create main window
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
    },
  });

  mainWindow.loadFile('index.html');
});

// Handle app events
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    // Recreate window
  }
});
```

### Renderer Process Setup

```typescript
// renderer.ts
import { ElectronRendererProcessNotificationManager } from '@sparkstrand/notifications-electron';

const config = {
  apiUrl: 'https://your-api-domain.com/api',
  appId: 'your-app-id',
};

const notificationManager = new ElectronRendererProcessNotificationManager(config);

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
  try {
    await notificationManager.initialize();
    console.log('Notification manager initialized');
  } catch (error) {
    console.error('Failed to initialize notification manager:', error);
  }
});

// Set up event handlers
notificationManager.onNotificationClick((notification) => {
  console.log('Notification clicked:', notification);
  // Handle notification click (e.g., focus app, open specific window)
});

notificationManager.onPreferencesChange((preferences) => {
  console.log('Preferences changed:', preferences);
  // Update UI or save preferences
});

// Send notification
async function sendDesktopNotification() {
  try {
    await notificationManager.createNotification({
      title: 'Desktop Notification',
      message: 'This is a desktop notification',
      type: 'INFO',
      priority: 'NORMAL',
      channels: ['inApp'],
      recipientId: 'user-123',
    });
  } catch (error) {
    console.error('Failed to send notification:', error);
  }
}
```

## API Reference

### Base URL

```
https://your-api-domain.com/api
```

### Endpoints

#### Create Notification

```http
POST /notifications
Content-Type: application/json

{
  "title": "Notification Title",
  "message": "Notification message content",
  "type": "INFO",
  "priority": "NORMAL",
  "channels": ["inApp", "email"],
  "recipientId": "user-123",
  "senderId": "system",
  "appId": "your-app-id",
  "metadata": {
    "key": "value"
  }
}
```

#### Get Notifications

```http
GET /notifications?userId=user-123&limit=20&offset=0&status=PENDING
```

#### Update Notification

```http
PUT /notifications
Content-Type: application/json

{
  "id": "notification-id",
  "status": "READ",
  "metadata": {
    "readAt": "2024-01-01T00:00:00Z"
  }
}
```

#### Delete Notification

```http
DELETE /notifications?id=notification-id
```

#### Health Check

```http
GET /health
```

#### Get Application Configuration

```http
GET /applications?apiKey=your-app-api-key
```

#### Create Application

```http
POST /applications
Content-Type: application/json

{
  "name": "My App",
  "description": "My custom application",
  "novuAppId": "your-novu-app-id",
  "novuApiKey": "your-novu-api-key",
  "resendApiKey": "your-resend-api-key",
  "twilioAccountSid": "your-twilio-account-sid",
  "twilioAuthToken": "your-twilio-auth-token",
  "defaultProvider": "hybrid",
  "config": {
    "defaultChannels": ["inApp", "email", "sms"],
    "enableQuietHours": true
  }
}
```

#### Update Application Configuration

```http
PUT /applications
Content-Type: application/json

{
  "apiKey": "your-app-api-key",
  "resendApiKey": "new-resend-api-key",
  "defaultProvider": "direct"
}
```

### Response Format

All API responses follow this format:

```typescript
{
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}
```

## Configuration

### Multi-Tenant Configuration

The system supports multi-tenant configuration, allowing each application to have its own provider credentials and settings.

#### Application Registration

```typescript
// Create a new application with custom provider configurations
const response = await fetch('/api/applications', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'My App',
    description: 'My custom application',
    novuAppId: 'your-novu-app-id',
    novuApiKey: 'your-novu-api-key',
    resendApiKey: 'your-resend-api-key',
    twilioAccountSid: 'your-twilio-account-sid',
    twilioAuthToken: 'your-twilio-auth-token',
    defaultProvider: 'hybrid', // novu, direct, or hybrid
    config: {
      defaultChannels: ['inApp', 'email', 'sms'],
      enableQuietHours: true,
    }
  })
});

const app = await response.json();
// app.data.apiKey contains your unique API key
```

#### Using Application-Specific Configuration

```typescript
// When sending notifications, use your app's API key
const response = await fetch('/api/notifications', {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'X-API-Key': 'your-app-api-key' // Your application's unique API key
  },
  body: JSON.stringify({
    title: 'Hello from My App',
    message: 'This notification uses your app\'s provider configuration',
    recipientId: 'user-123',
    channels: ['email', 'sms'],
    appId: 'your-app-api-key'
  })
});
```

#### Provider Strategy

- **Novu**: Uses Novu for all delivery (default)
- **Direct**: Bypasses Novu, uses direct provider APIs
- **Hybrid**: Uses Novu for some channels, direct providers for others

#### Application Management

```typescript
// Get your application configuration
const response = await fetch('/api/applications?apiKey=your-app-api-key');
const app = await response.json();

// Update provider configurations
const updateResponse = await fetch('/api/applications', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    apiKey: 'your-app-api-key',
    resendApiKey: 'new-resend-api-key',
    defaultProvider: 'direct'
  })
});
```

### Environment Variables

```bash
# Required
NOVU_API_KEY=your_novu_api_key
NOVU_APP_ID=your_novu_app_id

# Optional
RESEND_API_KEY=your_resend_api_key
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
JWT_SECRET=your_jwt_secret
```

### Client Configuration

```typescript
interface NotificationsConfig {
  apiUrl: string;                    // Required: API server URL
  appId: string;                     // Required: Your application ID
  userId?: string;                   // Optional: Current user ID
  enablePushNotifications?: boolean;  // Optional: Enable push notifications (RN)
  enableSystemNotifications?: boolean; // Optional: Enable system notifications (Electron)
  enableTrayNotifications?: boolean;  // Optional: Enable tray notifications (Electron)
  enableSound?: boolean;             // Optional: Enable sound notifications (Electron)
  notificationTimeout?: number;      // Optional: Auto-hide timeout in ms
  maxNotifications?: number;         // Optional: Maximum concurrent notifications
  enableQuietHours?: boolean;        // Optional: Enable quiet hours
  quietHoursStart?: string;         // Optional: Quiet hours start (HH:mm)
  quietHoursEnd?: string;           // Optional: Quiet hours end (HH:mm)
}
```

## Advanced Usage

### Custom Notification Types

```typescript
import { NotificationType } from '@sparkstrand/notifications-core';

// Define custom notification types
const CUSTOM_TYPES: NotificationType[] = [
  'TASK_COMPLETED',
  'PAYMENT_RECEIVED',
  'SYSTEM_MAINTENANCE',
  'FEATURE_UPDATE',
];

// Use in your notifications
await sendNotification({
  title: 'Task Completed',
  message: 'Background processing finished',
  type: 'TASK_COMPLETED',
  priority: 'HIGH',
  channels: ['inApp', 'email'],
  recipientId: 'user-123',
  metadata: {
    taskId: 'task-456',
    completionTime: new Date().toISOString(),
    performanceMetrics: {
      duration: '2.5s',
      memoryUsed: '128MB',
    },
  },
});
```

### Channel-Specific Configuration

```typescript
import { DeliveryChannels } from '@sparkstrand/notifications-core';

// Configure different channels with different priorities
const channels: DeliveryChannels = {
  email: {
    enabled: true,
    priority: 'HIGH',
    template: 'email-template-1',
  },
  sms: {
    enabled: false,
    priority: 'URGENT',
  },
  push: {
    enabled: true,
    priority: 'NORMAL',
    sound: 'default',
  },
  inApp: {
    enabled: true,
    priority: 'LOW',
    position: 'top-right',
  },
  webhook: {
    enabled: true,
    priority: 'LOW',
    endpoint: 'https://your-webhook.com/notifications',
  },
};
```

### Real-time Updates

```typescript
import { useNotifications } from '@sparkstrand/notifications-react';

function RealTimeNotifications() {
  const { notifications, isConnected } = useNotifications();

  // The hook automatically handles real-time updates via Server-Sent Events
  // You can check connection status
  if (!isConnected) {
    return <div>Connecting to notifications...</div>;
  }

  return (
    <div>
      <h3>Real-time Notifications</h3>
      {notifications.map(notification => (
        <div key={notification.id}>
          {notification.title} - {notification.status}
        </div>
      ))}
    </div>
  );
}
```

### Error Handling

```typescript
import { useNotifications } from '@sparkstrand/notifications-react';

function NotificationWithErrorHandling() {
  const { sendNotification, error, clearError } = useNotifications();

  const handleSend = async () => {
    try {
      await sendNotification({
        title: 'Test',
        message: 'Test message',
        type: 'INFO',
        recipientId: 'user-123',
      });
    } catch (error) {
      console.error('Failed to send notification:', error);
      // Handle error in your UI
    }
  };

  return (
    <div>
      {error && (
        <div className="error">
          Error: {error.message}
          <button onClick={clearError}>Dismiss</button>
        </div>
      )}
      
      <button onClick={handleSend}>Send Test Notification</button>
    </div>
  );
}
```

## Troubleshooting

### Common Issues

#### 1. Notifications Not Appearing

- Check that the API server is running and accessible
- Verify your `apiUrl` configuration is correct
- Check browser console for error messages
- Ensure your `appId` and `userId` are properly set

#### 2. Push Notifications Not Working (React Native)

- Verify device registration was successful
- Check that push notification permissions are granted
- Ensure your app is properly configured for push notifications
- Check device token is being received

#### 3. Electron Notifications Not Showing

- Ensure the main process notification manager is initialized
- Check that system notifications are enabled
- Verify IPC communication between main and renderer processes
- Check tray icon is properly configured

#### 4. API Errors

- Verify your environment variables are set correctly
- Check that Novu API key is valid
- Ensure your API server is running
- Check network connectivity

### Debug Mode

Enable debug logging by setting the environment variable:

```bash
DEBUG=sparkstrand:notifications:*
```

### Getting Help

If you encounter issues:

1. Check the browser/console logs for error messages
2. Verify your configuration matches the examples
3. Test the API endpoints directly using a tool like Postman
4. Check the health endpoint: `GET /api/health`
5. Review the troubleshooting section above

For additional support, please refer to the main README or create an issue in the repository.

## Next Steps

Now that you have the basics, you can:

1. **Customize the UI**: Modify the notification components to match your app's design
2. **Add More Channels**: Implement additional delivery channels
3. **Set Up Monitoring**: Add logging and analytics to track notification performance
4. **Implement Advanced Features**: Add notification scheduling, grouping, and filtering
5. **Optimize Performance**: Implement caching and optimize API calls

The system is designed to be flexible and extensible, so feel free to customize it to meet your specific needs!
