# 🔧 Novu Inbox Implementation Guide

This guide ensures your Novu inbox integration works perfectly with our package.

## ✅ **Implementation Checklist**

### **1. Dependencies Installation**

```bash
# Install our package (That's it!)
npm install @sparkstrand/notifications-react

# Verify installation
npm list @sparkstrand/notifications-react
```

**✨ No need to install Novu packages separately!** Our package includes everything you need.

### **2. Environment Configuration**

Create `.env.local` in your project root:

```bash
# Novu Configuration (Required)
NEXT_PUBLIC_NOVU_BACKEND_URL=https://api.novu.co
NEXT_PUBLIC_NOVU_SOCKET_URL=https://ws.novu.co
NEXT_PUBLIC_NOVU_APP_ID=your-novu-app-identifier

# Your App Configuration (Required)
NEXT_PUBLIC_APP_ID=your-app-id
NEXT_PUBLIC_API_URL=https://yourdomain.com/api
```

### **3. Basic Component Implementation**

```tsx
import React from 'react';
import { NovuInbox } from '@sparkstrand/notifications-react';

export const MyNovuInbox: React.FC = () => {
    return (
        <NovuInbox
            appId="my-app"
            apiKey="your-api-key"
            userId="user-123"
            theme="light"
        />
    );
};
```

### **4. Advanced Implementation with Handlers**

```tsx
import React, { useState } from 'react';
import { NovuInbox } from '@sparkstrand/notifications-react';
import { IMessage } from '@novu/notification-center';

export const AdvancedNovuInbox: React.FC = () => {
    const [unreadCount, setUnreadCount] = useState(0);

    const handleNotificationClick = (notification: IMessage) => {
        console.log('Notification clicked:', notification);
        // Add your custom logic here
    };

    const handleUnreadCountChange = (count: number) => {
        setUnreadCount(count);
        // Update your app state
    };

    return (
        <NovuInbox
            appId="my-app"
            apiKey="your-api-key"
            userId="user-123"
            theme="dark"
            position="bottom"
            showUserPreferences={true}
            onNotificationClick={handleNotificationClick}
            onUnreadCountChange={handleUnreadCountChange}
        />
    );
};
```

## 🧪 **Testing Your Implementation**

### **1. Component Rendering Test**

```tsx
import React from 'react';
import { render, screen } from '@testing-library/react';
import { NovuInbox } from '@sparkstrand/notifications-react';

test('NovuInbox renders without crashing', () => {
    render(
        <NovuInbox
            appId="test-app"
            apiKey="test-key"
            userId="test-user"
        />
    );
    
    // The component should render without errors
    expect(document.querySelector('.novu-inbox-container')).toBeInTheDocument();
});
```

### **2. Functionality Test**

```tsx
import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import { NovuInbox } from '@sparkstrand/notifications-react';

test('NovuInbox handles notification clicks', () => {
    const mockClickHandler = jest.fn();
    
    render(
        <NovuInbox
            appId="test-app"
            apiKey="test-key"
            userId="test-user"
            onNotificationClick={mockClickHandler}
        />
    );
    
    // Click the notification bell
    const bell = screen.getByRole('button');
    fireEvent.click(bell);
    
    // Verify the click handler was called
    expect(mockClickHandler).toHaveBeenCalled();
});
```

## 🔍 **Troubleshooting Common Issues**

### **Issue 1: Component Not Rendering**

**Symptoms**: Blank page or component not visible

**Solutions**:
```tsx
// Check your imports
import { NovuInbox } from '@sparkstrand/notifications-react';

// Verify props are correct
<NovuInbox
    appId="valid-app-id"        // Must be string
    apiKey="valid-api-key"      // Must be string
    userId="valid-user-id"      // Must be string
/>
```

### **Issue 2: Novu Connection Errors**

**Symptoms**: Console errors about Novu API

**Solutions**:
```bash
# Verify environment variables
echo $NEXT_PUBLIC_NOVU_APP_ID
echo $NEXT_PUBLIC_NOVU_BACKEND_URL

# Check Novu dashboard for correct app ID
# Ensure API key has proper permissions
```

### **Issue 3: Styling Conflicts**

**Symptoms**: Component looks broken or unstyled

**Solutions**:
```css
/* Add CSS isolation */
.novu-inbox-container {
    position: relative;
    z-index: 1000;
    isolation: isolate;
}

/* Override conflicting styles */
.novu-notification-bell {
    all: unset;
    cursor: pointer;
    /* Add your custom styles */
}
```

### **Issue 4: WebSocket Connection Issues**

**Symptoms**: Real-time updates not working

**Solutions**:
```tsx
// Check WebSocket URLs
const wsUrl = process.env.NEXT_PUBLIC_NOVU_SOCKET_URL;
console.log('WebSocket URL:', wsUrl);

// Verify HTTPS in production
if (process.env.NODE_ENV === 'production') {
    console.assert(location.protocol === 'https:', 'HTTPS required for WebSocket');
}
```

## 🚀 **Production Deployment**

### **1. Build Verification**

```bash
# Build your application
npm run build

# Check for build errors
npm run build 2>&1 | grep -i error

# Verify bundle size
npm run build -- --analyze
```

### **2. Environment Verification**

```bash
# Production environment check
curl -s https://yourdomain.com/api/health

# Verify environment variables are set
echo "App ID: $NEXT_PUBLIC_APP_ID"
echo "Novu App ID: $NEXT_PUBLIC_NOVU_APP_ID"
```

### **3. Performance Monitoring**

```tsx
// Add performance monitoring
useEffect(() => {
    const startTime = performance.now();
    
    return () => {
        const loadTime = performance.now() - startTime;
        console.log(`Novu Inbox loaded in ${loadTime.toFixed(2)}ms`);
        
        // Send to analytics
        if (window.gtag) {
            window.gtag('event', 'novu_inbox_load', {
                load_time: loadTime
            });
        }
    };
}, []);
```

## 📱 **Mobile and Responsive Testing**

### **1. Responsive Design Test**

```tsx
// Test different screen sizes
const testResponsive = () => {
    const sizes = [
        { width: 375, height: 667 },   // iPhone
        { width: 768, height: 1024 },  // iPad
        { width: 1920, height: 1080 }  // Desktop
    ];
    
    sizes.forEach(size => {
        // Test component at each size
        console.log(`Testing at ${size.width}x${size.height}`);
    });
};
```

### **2. Touch Interaction Test**

```tsx
// Test touch events
const testTouch = () => {
    const bell = document.querySelector('.novu-notification-bell');
    
    if (bell) {
        // Simulate touch events
        const touchEvent = new TouchEvent('touchstart', {
            bubbles: true,
            cancelable: true
        });
        
        bell.dispatchEvent(touchEvent);
    }
};
```

## 🔒 **Security Considerations**

### **1. API Key Protection**

```tsx
// Never expose API keys in client-side code
// Use environment variables or secure backend endpoints

// ❌ Wrong - API key in component
const apiKey = 'sk-1234567890abcdef';

// ✅ Correct - API key from environment or secure source
const apiKey = process.env.NEXT_PUBLIC_API_KEY || await getSecureApiKey();
```

### **2. User Authentication**

```tsx
// Always verify user identity
const NovuInboxWithAuth: React.FC = () => {
    const { user, isAuthenticated } = useAuth();
    
    if (!isAuthenticated) {
        return <div>Please log in to view notifications</div>;
    }
    
    return (
        <NovuInbox
            appId="my-app"
            apiKey={user.apiKey}
            userId={user.id}
        />
    );
};
```

## 📊 **Monitoring and Analytics**

### **1. Error Tracking**

```tsx
// Add error boundary
class NovuInboxErrorBoundary extends React.Component {
    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        console.error('Novu Inbox Error:', error, errorInfo);
        
        // Send to error tracking service
        if (window.Sentry) {
            window.Sentry.captureException(error, { extra: errorInfo });
        }
    }
    
    render() {
        return this.props.children;
    }
}

// Usage
<NovuInboxErrorBoundary>
    <NovuInbox {...props} />
</NovuInboxErrorBoundary>
```

### **2. Usage Analytics**

```tsx
// Track user interactions
const trackNovuUsage = (action: string, data?: any) => {
    if (window.gtag) {
        window.gtag('event', 'novu_inbox_interaction', {
            action,
            data,
            timestamp: Date.now()
        });
    }
};

// Use in handlers
const handleNotificationClick = (notification: IMessage) => {
    trackNovuUsage('notification_click', {
        notification_id: notification._id,
        notification_type: notification.type
    });
    
    // Your custom logic
};
```

## 🎯 **Final Verification Checklist**

Before going live, verify:

- [ ] **Dependencies**: Our package installed correctly
- [ ] **Environment**: Variables set in production
- [ ] **Components**: Render without errors
- [ ] **Functionality**: Click handlers work
- [ ] **Styling**: Looks correct on all devices
- [ ] **Performance**: Loads within acceptable time
- [ ] **Security**: API keys protected
- [ ] **Monitoring**: Error tracking enabled
- [ ] **Testing**: Works on target devices
- [ ] **Documentation**: Team knows how to use

## 🆘 **Getting Help**

If you encounter issues:

1. **Check this guide** for common solutions
2. **Review the full documentation** in `NOVU_INBOX_INTEGRATION.md`
3. **Test with the demo component** in `NovuInboxDemo.tsx`
4. **Check browser console** for error messages
5. **Verify environment variables** are set correctly
6. **Contact our support team** with specific error details

---

**Your Novu inbox integration is now ready for production! 🎉**

Follow this guide step by step, and you'll have a beautiful, functional notification center that your users will love. 