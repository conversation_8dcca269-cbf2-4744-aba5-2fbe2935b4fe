# 🚀 **Developer Guide: Notification System**

## 📋 **Table of Contents**

1. [Getting Started](#getting-started)
2. [Registration Process](#registration-process)
3. [API Authentication](#api-authentication)
4. [Sending Notifications](#sending-notifications)
5. [API Reference](#api-reference)
6. [Integration Examples](#integration-examples)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

---

## 🎯 **Getting Started**

### **What You'll Get**
- **API Key**: Secure authentication for all requests
- **App ID**: Unique identifier for your application
- **Multi-provider support**: Email, SMS, push notifications
- **Automatic fallback**: If one provider fails, we try the next
- **Real-time delivery**: Instant notification delivery
- **Scalable infrastructure**: Handle millions of notifications

### **Quick Start**
1. **Register your application** at `/register`
2. **Wait for approval** (usually 24-48 hours)
3. **Get your API credentials** via email
4. **Start sending notifications** using our API

---

## 🔄 **Registration Process**

### **1. Fill Out Registration Form**
Visit `/register` and provide:
- **App ID**: Unique identifier (letters, numbers, hyphens only)
- **App Name**: Human-readable name
- **Description**: What your app does
- **Contact Information**: Your email and name
- **Business Details**: Type and expected volume
- **Provider Configuration**: Optional existing accounts

### **2. Wait for Approval**
- **Review time**: 24-48 hours
- **Admin review**: Our team reviews your application
- **Approval criteria**: Business legitimacy, use case clarity
- **Notification**: You'll receive email updates

### **3. Get Your Credentials**
Upon approval, you'll receive:
- **API Key**: `spark_abc123_def456`
- **App ID**: Your unique app identifier
- **Integration guide**: How to use the system

---

## 🔑 **API Authentication**

### **Authentication Header**
All API requests require your API key in the Authorization header:

```http
Authorization: Bearer YOUR_API_KEY
```

### **Example Request**
```bash
curl -H "Authorization: Bearer spark_abc123_def456" \
     -H "Content-Type: application/json" \
     https://your-domain.com/api/notifications
```

### **Security Best Practices**
✅ **Store API key securely** (environment variables, secure vaults)
✅ **Never expose in client-side code**
✅ **Rotate keys regularly** if needed
✅ **Use HTTPS** for all requests
❌ **Don't commit to version control**
❌ **Don't share in public repositories**

---

## 📨 **Sending Notifications**

### **Basic Notification**
```json
{
  "recipientId": "user123",
  "title": "Welcome!",
  "message": "Your account has been created successfully.",
  "type": "SUCCESS"
}
```

### **Advanced Notification**
```json
{
  "recipientId": "user123",
  "title": "Order Confirmed",
  "message": "Your order #12345 has been confirmed.",
  "type": "SUCCESS",
  "priority": "HIGH",
  "scheduledFor": "2024-01-15T14:00:00Z",
  "metadata": {
    "orderId": "12345",
    "amount": 99.99,
    "items": ["Product A", "Product B"]
  }
}
```

### **Notification Types**
- **INFO**: General information
- **SUCCESS**: Positive outcomes
- **WARNING**: Cautionary messages
- **ERROR**: Error notifications

### **Priority Levels**
- **LOW**: Non-urgent notifications
- **NORMAL**: Standard notifications (default)
- **HIGH**: Important notifications
- **URGENT**: Critical notifications

---

## 🔌 **API Reference**

### **Base URL**
```
https://your-domain.com/api
```

### **Endpoints**

#### **Send Notification**
```http
POST /notifications
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

**Request Body:**
```json
{
  "recipientId": "string",     // Required: Unique user identifier
  "title": "string",           // Required: Notification title
  "message": "string",         // Required: Notification message
  "type": "string",            // Optional: INFO, SUCCESS, WARNING, ERROR
  "priority": "string",        // Optional: LOW, NORMAL, HIGH, URGENT
  "scheduledFor": "string",    // Optional: ISO 8601 timestamp
  "metadata": {}               // Optional: Additional data
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "notif_123",
    "status": "PENDING",
    "message": "Notification queued successfully"
  }
}
```

#### **Get Application Configuration**
```http
GET /applications?apiKey=YOUR_API_KEY&appId=YOUR_APP_ID
```

**Response:**
```json
{
  "success": true,
  "data": {
    "appId": "my-awesome-app",
    "name": "My Awesome App",
    "defaultProvider": "novu",
    "fallbackStrategy": "failover",
    "maxRetries": 3,
    "isActive": true
  }
}
```

#### **Check Notification Status**
```http
GET /notifications/{notificationId}
Authorization: Bearer YOUR_API_KEY
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "notif_123",
    "status": "DELIVERED",
    "deliveredAt": "2024-01-15T14:00:00Z",
    "deliveryStatus": [
      {
        "channel": "email",
        "provider": "resend",
        "status": "DELIVERED",
        "deliveredAt": "2024-01-15T14:00:00Z"
      }
    ]
  }
}
```

---

## 💻 **Integration Examples**

### **JavaScript/Node.js**

#### **Basic Integration**
```javascript
class NotificationService {
  constructor(apiKey, baseUrl = 'https://your-domain.com/api') {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  async sendNotification(recipientId, title, message, options = {}) {
    try {
      const response = await fetch(`${this.baseUrl}/notifications`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          recipientId,
          title,
          message,
          type: options.type || 'INFO',
          priority: options.priority || 'NORMAL',
          scheduledFor: options.scheduledFor,
          metadata: options.metadata
        })
      });

      const result = await response.json();
      if (result.success) {
        console.log('Notification sent:', result.data);
        return result.data;
      } else {
        throw new Error(result.error || 'Failed to send notification');
      }
    } catch (error) {
      console.error('Error sending notification:', error);
      throw error;
    }
  }

  async getNotificationStatus(notificationId) {
    try {
      const response = await fetch(`${this.baseUrl}/notifications/${notificationId}`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      });

      const result = await response.json();
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error || 'Failed to get notification status');
      }
    } catch (error) {
      console.error('Error getting notification status:', error);
      throw error;
    }
  }
}

// Usage
const notificationService = new NotificationService('YOUR_API_KEY');

// Send immediate notification
await notificationService.sendNotification(
  'user123',
  'Welcome!',
  'Your account has been created successfully.',
  { type: 'SUCCESS' }
);

// Send scheduled notification
await notificationService.sendNotification(
  'user123',
  'Reminder',
  'Don\'t forget your meeting at 2 PM',
  { 
    scheduledFor: '2024-01-15T14:00:00Z',
    priority: 'HIGH'
  }
);
```

#### **Express.js Middleware**
```javascript
const express = require('express');
const app = express();

// Middleware to add notification service to requests
app.use((req, res, next) => {
  req.notifications = new NotificationService(process.env.NOTIFICATION_API_KEY);
  next();
});

// Route to send welcome notification
app.post('/users', async (req, res) => {
  try {
    // Create user logic here...
    const user = { id: 'user123', email: '<EMAIL>' };

    // Send welcome notification
    await req.notifications.sendNotification(
      user.id,
      'Welcome!',
      'Your account has been created successfully.',
      { type: 'SUCCESS' }
    );

    res.json({ success: true, user });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

### **Python**

#### **Basic Integration**
```python
import requests
import json
from datetime import datetime
from typing import Optional, Dict, Any

class NotificationService:
    def __init__(self, api_key: str, base_url: str = 'https://your-domain.com/api'):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def send_notification(
        self,
        recipient_id: str,
        title: str,
        message: str,
        notification_type: str = 'INFO',
        priority: str = 'NORMAL',
        scheduled_for: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Send a notification"""
        url = f'{self.base_url}/notifications'
        data = {
            'recipientId': recipient_id,
            'title': title,
            'message': message,
            'type': notification_type,
            'priority': priority
        }
        
        if scheduled_for:
            data['scheduledFor'] = scheduled_for
        
        if metadata:
            data['metadata'] = metadata
        
        try:
            response = requests.post(url, headers=self.headers, json=data)
            response.raise_for_status()
            result = response.json()
            
            if result['success']:
                print(f'Notification sent: {result["data"]}')
                return result['data']
            else:
                raise Exception(result.get('error', 'Failed to send notification'))
                
        except requests.exceptions.RequestException as e:
            print(f'Error sending notification: {e}')
            raise
    
    def get_notification_status(self, notification_id: str) -> Dict[str, Any]:
        """Get notification delivery status"""
        url = f'{self.base_url}/notifications/{notification_id}'
        
        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            result = response.json()
            
            if result['success']:
                return result['data']
            else:
                raise Exception(result.get('error', 'Failed to get notification status'))
                
        except requests.exceptions.RequestException as e:
            print(f'Error getting notification status: {e}')
            raise

# Usage
notification_service = NotificationService('YOUR_API_KEY')

# Send immediate notification
notification_service.send_notification(
    'user123',
    'Welcome!',
    'Your account has been created successfully.',
    notification_type='SUCCESS'
)

# Send scheduled notification
notification_service.send_notification(
    'user123',
    'Reminder',
    'Don\'t forget your meeting at 2 PM',
    scheduled_for='2024-01-15T14:00:00Z',
    priority='HIGH'
)
```

#### **Django Integration**
```python
# settings.py
NOTIFICATION_API_KEY = 'your-api-key-here'
NOTIFICATION_BASE_URL = 'https://your-domain.com/api'

# notifications.py
from django.conf import settings
from .notification_service import NotificationService

notification_service = NotificationService(
    settings.NOTIFICATION_API_KEY,
    settings.NOTIFICATION_BASE_URL
)

# views.py
from django.http import JsonResponse
from .notifications import notification_service

def create_user(request):
    try:
        # Create user logic here...
        user = User.objects.create_user(
            username='user123',
            email='<EMAIL>'
        )
        
        # Send welcome notification
        notification_service.send_notification(
            str(user.id),
            'Welcome!',
            'Your account has been created successfully.',
            notification_type='SUCCESS'
        )
        
        return JsonResponse({'success': True, 'user_id': user.id})
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
```

### **cURL Examples**

#### **Send Basic Notification**
```bash
curl -X POST https://your-domain.com/api/notifications \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "recipientId": "user123",
    "title": "Welcome!",
    "message": "Your account has been created successfully.",
    "type": "SUCCESS"
  }'
```

#### **Send Scheduled Notification**
```bash
curl -X POST https://your-domain.com/api/notifications \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "recipientId": "user123",
    "title": "Reminder",
    "message": "Don'\''t forget your meeting at 2 PM",
    "scheduledFor": "2024-01-15T14:00:00Z",
    "priority": "HIGH"
  }'
```

#### **Get Notification Status**
```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
  https://your-domain.com/api/notifications/notif_123
```

#### **Get Application Configuration**
```bash
curl "https://your-domain.com/api/applications?apiKey=YOUR_API_KEY&appId=YOUR_APP_ID"
```

---

## 🎯 **Best Practices**

### **Recipient ID Management**
✅ **Use meaningful IDs**: `user_123`, `order_456`, `team_admin`
✅ **Keep IDs consistent**: Same user should always have same ID
✅ **Use UUIDs for sensitive data**: `550e8400-e29b-41d4-a716-************`
❌ **Don't use sequential numbers**: `1`, `2`, `3` (security risk)
❌ **Don't use email addresses**: Use internal IDs instead

### **Notification Content**
✅ **Clear, actionable titles**: "Order Shipped" vs "Update"
✅ **Concise messages**: Get to the point quickly
✅ **Include relevant data**: Order numbers, amounts, dates
✅ **Use appropriate types**: SUCCESS, WARNING, ERROR
❌ **Don't send empty notifications**
❌ **Don't use generic messages**

### **Rate Limiting & Batching**
✅ **Respect rate limits**: Don't overwhelm the system
✅ **Batch notifications**: Send multiple in one request if possible
✅ **Use scheduling**: Spread notifications over time
✅ **Monitor delivery status**: Track success/failure rates
❌ **Don't send too many at once**
❌ **Don't ignore delivery failures**

### **Error Handling**
✅ **Handle API errors gracefully**: Check response status
✅ **Retry failed requests**: With exponential backoff
✅ **Log errors**: For debugging and monitoring
✅ **Fallback gracefully**: Don't break user experience
❌ **Don't ignore errors**
❌ **Don't retry indefinitely**

### **Security**
✅ **Store API key securely**: Environment variables, vaults
✅ **Use HTTPS**: Always for production
✅ **Validate input**: Sanitize user data
✅ **Monitor usage**: Watch for unusual patterns
❌ **Don't expose API key in client code**
❌ **Don't trust user input blindly**

---

## 🔧 **Troubleshooting**

### **Common Issues**

#### **Authentication Errors**
**Problem**: `401 Unauthorized` or `Invalid API key`
**Solutions**:
1. Check API key is correct
2. Verify API key is active
3. Ensure proper Authorization header format
4. Check if application is approved and active

#### **Rate Limiting**
**Problem**: `429 Too Many Requests`
**Solutions**:
1. Reduce request frequency
2. Implement exponential backoff
3. Batch multiple notifications
4. Use scheduled notifications

#### **Delivery Failures**
**Problem**: Notifications not reaching recipients
**Solutions**:
1. Check notification status endpoint
2. Verify recipient ID format
3. Check provider configurations
4. Review error messages in response

#### **Scheduling Issues**
**Problem**: Scheduled notifications not sending
**Solutions**:
1. Verify timestamp format (ISO 8601)
2. Check if time is in the future
3. Ensure timezone is correct
4. Verify scheduling is enabled

### **Debugging Steps**

#### **1. Check API Response**
```bash
curl -v -X POST https://your-domain.com/api/notifications \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"recipientId": "test", "title": "Test", "message": "Test"}'
```

#### **2. Verify API Key**
```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
  "https://your-domain.com/api/applications?apiKey=YOUR_API_KEY&appId=YOUR_APP_ID"
```

#### **3. Test with Simple Request**
```bash
curl -X POST https://your-domain.com/api/notifications \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "recipientId": "test_user",
    "title": "Test Notification",
    "message": "This is a test notification"
  }'
```

### **Getting Help**

#### **Support Channels**
- **Email**: <EMAIL>
- **Documentation**: `/docs`
- **Status Page**: Check system health
- **Community**: Developer forums

#### **Information to Provide**
When contacting support, include:
1. **Error message**: Exact error text
2. **Request details**: What you were trying to do
3. **Code snippet**: Relevant code section
4. **Environment**: Language, framework, version
5. **Timestamps**: When the error occurred

---

## 🚀 **Advanced Features**

### **Bulk Notifications**
Send multiple notifications in one request:

```json
{
  "notifications": [
    {
      "recipientId": "user1",
      "title": "Welcome User 1",
      "message": "Welcome to our platform!"
    },
    {
      "recipientId": "user2", 
      "title": "Welcome User 2",
      "message": "Welcome to our platform!"
    }
  ]
}
```

### **Template Notifications**
Use predefined templates:

```json
{
  "templateId": "welcome_email",
  "recipientId": "user123",
  "variables": {
    "userName": "John Doe",
    "companyName": "Acme Corp"
  }
}
```

### **Webhook Notifications**
Receive delivery status updates:

```json
{
  "webhookUrl": "https://your-app.com/webhooks/notifications",
  "events": ["delivered", "failed", "bounced"]
}
```

---

## 📊 **Monitoring & Analytics**

### **Key Metrics to Track**
- **Delivery rate**: Percentage of successful deliveries
- **Response time**: API response times
- **Error rates**: Failed requests and reasons
- **Usage patterns**: Peak times and volumes

### **Health Checks**
```bash
# Check system health
curl https://your-domain.com/api/health

# Check your application status
curl -H "Authorization: Bearer YOUR_API_KEY" \
  "https://your-domain.com/api/applications?apiKey=YOUR_API_KEY&appId=YOUR_APP_ID"
```

---

## 🎉 **You're Ready!**

With this guide, you have everything needed to:

✅ **Integrate notifications** into your application
✅ **Send various types** of notifications
✅ **Handle errors gracefully** and debug issues
✅ **Follow best practices** for security and performance
✅ **Scale your notifications** as your app grows

**Start building amazing notification experiences for your users!** 🚀✨

---

**📚 Need more help? Check our comprehensive documentation or contact support!** 