# 🚀 Novu Inbox Integration Guide

This guide shows you how to implement the Novu inbox notification center using our `@sparkstrand/notifications-react` package.

## 📋 Table of Contents

- [Overview](#overview)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Basic Implementation](#basic-implementation)
- [Advanced Features](#advanced-features)
- [Customization](#customization)
- [Real-time Updates](#real-time-updates)
- [Production Deployment](#production-deployment)
- [Troubleshooting](#troubleshooting)
- [Examples](#examples)

## 🎯 Overview

The Novu inbox integration provides a beautiful, feature-rich notification center that works seamlessly with our notification system. It includes:

- **Real-time notifications** via WebSocket connections
- **Beautiful UI components** with light/dark theme support
- **Customizable positioning** and behavior
- **User preference management** for notification settings
- **Multi-channel support** (email, SMS, push, in-app)
- **Unread count tracking** and management

## 🔧 Prerequisites

Before implementing the Novu inbox, ensure you have:

1. **Novu Account**: Sign up at [novu.co](https://novu.co)
2. **Novu Application**: Create an application in your Novu dashboard
3. **API Keys**: Get your Novu API key and application identifier
4. **React/Next.js App**: A React application (version 18+) or Next.js app

## 📦 Installation

### **Step 1: Install Our Package (That's It!)**

```bash
npm install @sparkstrand/notifications-react
```

**✨ No need to install Novu packages separately!** Our package includes everything you need.

### **Step 2: Set Environment Variables**

Create or update your `.env.local` file:

```bash
# Novu Configuration
NEXT_PUBLIC_NOVU_BACKEND_URL=https://api.novu.co
NEXT_PUBLIC_NOVU_SOCKET_URL=https://ws.novu.co
NEXT_PUBLIC_NOVU_APP_ID=your-novu-app-identifier

# Your Notification System
NEXT_PUBLIC_API_URL=https://yourdomain.com/api
NEXT_PUBLIC_APP_ID=your-app-id
```

## 🚀 Basic Implementation

### **Simple Novu Inbox Component**

```tsx
import React from 'react';
import { NovuInbox } from '@sparkstrand/notifications-react';

interface BasicNovuInboxProps {
    appId: string;
    apiKey: string;
    userId: string;
}

export const BasicNovuInbox: React.FC<BasicNovuInboxProps> = ({
    appId,
    apiKey,
    userId
}) => {
    return (
        <NovuInbox
            appId={appId}
            apiKey={apiKey}
            userId={userId}
            theme="light"
        />
    );
};
```

### **Usage in Your App**

```tsx
import React from 'react';
import { BasicNovuInbox } from './components/BasicNovuInbox';

function App() {
    const appConfig = {
        appId: 'my-awesome-app',
        apiKey: 'your-secure-api-key',
        userId: 'user-123'
    };

    return (
        <div className="app">
            <header className="app-header">
                <h1>My Application</h1>
                
                {/* Novu Inbox Integration */}
                <BasicNovuInbox
                    appId={appConfig.appId}
                    apiKey={appConfig.apiKey}
                    userId={appConfig.userId}
                />
            </header>

            <main className="app-content">
                <h2>Welcome to your dashboard!</h2>
                <p>Click the notification bell to see your notifications.</p>
            </main>
        </div>
    );
}

export default App;
```

## ⚡ Advanced Features

### **Enhanced Novu Inbox with Custom Handlers**

```tsx
import React, { useState } from 'react';
import { NovuInbox } from '@sparkstrand/notifications-react';
import { IMessage } from '@novu/notification-center';

interface EnhancedNovuInboxProps {
    appId: string;
    apiKey: string;
    userId: string;
    theme?: 'light' | 'dark';
}

export const EnhancedNovuInbox: React.FC<EnhancedNovuInboxProps> = ({
    appId,
    apiKey,
    userId,
    theme = 'light'
}) => {
    const [notificationCount, setNotificationCount] = useState(0);
    const [lastNotification, setLastNotification] = useState<IMessage | null>(null);

    // Handle notification clicks
    const handleNotificationClick = (notification: IMessage) => {
        console.log('Notification clicked:', notification);
        setLastNotification(notification);
        
        // You can add custom logic here:
        // - Navigate to specific pages
        // - Update application state
        // - Trigger actions
        // - Mark as read in your backend
    };

    // Handle unread count changes
    const handleUnreadCountChange = (count: number) => {
        setNotificationCount(count);
        console.log('Unread notifications:', count);
    };

    return (
        <div className="enhanced-notification-section">
            <NovuInbox
                appId={appId}
                apiKey={apiKey}
                userId={userId}
                theme={theme}
                position="top"
                showUserPreferences={true}
                onNotificationClick={handleNotificationClick}
                onUnreadCountChange={handleUnreadCountChange}
            />
            
            {/* Optional: Display notification info */}
            {lastNotification && (
                <div className="notification-info">
                    <small>Last clicked: {lastNotification.title}</small>
                </div>
            )}
        </div>
    );
};
```

### **Standalone Novu Inbox (No Dependencies)**

If you prefer not to use our notification hooks, use the standalone component:

```tsx
import React from 'react';
import { StandaloneNovuInbox } from '@sparkstrand/notifications-react';

export const StandaloneInbox: React.FC = () => {
    return (
        <StandaloneNovuInbox
            appId="my-app"
            apiKey="your-api-key"
            userId="user-123"
            theme="dark"
            position="bottom"
        />
    );
};
```

## 🎨 Customization

### **Theme and Styling**

```tsx
import React, { useState } from 'react';
import { NovuInbox } from '@sparkstrand/notifications-react';

export const ThemedNovuInbox: React.FC = () => {
    const [theme, setTheme] = useState<'light' | 'dark'>('light');

    return (
        <div className="themed-notification-section">
            {/* Theme Toggle */}
            <button 
                onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
                className="theme-toggle"
            >
                Switch to {theme === 'light' ? 'Dark' : 'Light'} Theme
            </button>

            {/* Novu Inbox with Current Theme */}
            <NovuInbox
                appId="my-app"
                apiKey="your-api-key"
                userId="user-123"
                theme={theme}
                position="top"
                showUserPreferences={true}
            />
        </div>
    );
};
```

### **Custom CSS Styling**

```css
/* Custom styles for Novu inbox */
.novu-inbox-container {
    position: relative;
    z-index: 1000;
}

.novu-notification-bell {
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.novu-notification-bell:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

/* Dark theme adjustments */
[data-theme="dark"] .novu-notification-bell {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Custom notification center positioning */
.novu-notification-center {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
    .novu-inbox-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
    }
}
```

## 🔄 Real-time Updates

### **WebSocket Integration for Live Updates**

```tsx
import React, { useEffect, useRef, useState } from 'react';
import { NovuInbox } from '@sparkstrand/notifications-react';

interface RealTimeNovuInboxProps {
    appId: string;
    apiKey: string;
    userId: string;
}

export const RealTimeNovuInbox: React.FC<RealTimeNovuInboxProps> = ({
    appId,
    apiKey,
    userId
}) => {
    const [isConnected, setIsConnected] = useState(false);
    const wsRef = useRef<WebSocket | null>(null);

    useEffect(() => {
        // Connect to your WebSocket endpoint for real-time updates
        const ws = new WebSocket(
            `wss://yourdomain.com/api/notifications/ws?appId=${appId}&apiKey=${apiKey}&userId=${userId}`
        );
        
        ws.onopen = () => {
            setIsConnected(true);
            console.log('WebSocket connected for real-time notifications');
        };

        ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            
            if (data.type === 'NEW_NOTIFICATION') {
                console.log('Real-time notification received:', data.notification);
                // The Novu inbox will automatically update via its own WebSocket
                // This is for additional custom handling if needed
            }
        };

        ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            setIsConnected(false);
        };

        ws.onclose = () => {
            setIsConnected(false);
            console.log('WebSocket disconnected');
        };

        wsRef.current = ws;

        return () => {
            if (wsRef.current) {
                wsRef.current.close();
            }
        };
    }, [appId, apiKey, userId]);

    return (
        <div className="realtime-notification-section">
            {/* Connection Status */}
            <div className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
                {isConnected ? '🟢 Live' : '🔴 Offline'}
            </div>

            {/* Novu Inbox */}
            <NovuInbox
                appId={appId}
                apiKey={apiKey}
                userId={userId}
                theme="light"
                position="top"
                showUserPreferences={true}
            />
        </div>
    );
};
```

## 🚀 Production Deployment

### **Environment Configuration**

```bash
# Production .env file
NEXT_PUBLIC_NOVU_BACKEND_URL=https://api.novu.co
NEXT_PUBLIC_NOVU_SOCKET_URL=https://ws.novu.co
NEXT_PUBLIC_NOVU_APP_ID=your-production-novu-app-id
NEXT_PUBLIC_API_URL=https://yourdomain.com/api
NEXT_PUBLIC_APP_ID=your-production-app-id
```

### **Build and Deploy Commands**

```bash
# Install dependencies
npm install

# Build the application
npm run build

# Start production server
npm start

# Or deploy to Vercel/Netlify
vercel --prod
```

### **Health Check Endpoint**

Ensure your notification API has a health check:

```bash
curl https://yourdomain.com/api/health
```

## 🔧 Troubleshooting

### **Common Issues and Solutions**

#### **1. Novu Inbox Not Loading**

**Problem**: The notification bell doesn't appear or shows errors.

**Solutions**:
- Check environment variables are correctly set
- Verify Novu API key and app ID are valid
- Ensure all dependencies are installed
- Check browser console for error messages

```bash
# Verify environment variables
echo $NEXT_PUBLIC_NOVU_APP_ID
echo $NEXT_PUBLIC_NOVU_BACKEND_URL
```

#### **2. WebSocket Connection Issues**

**Problem**: Real-time updates not working.

**Solutions**:
- Check firewall settings
- Verify WebSocket URL is correct
- Ensure HTTPS is used in production
- Check Novu service status

#### **3. Styling Conflicts**

**Problem**: Novu inbox styling conflicts with your app.

**Solutions**:
- Use CSS specificity to override styles
- Wrap Novu components in containers with unique classes
- Use CSS modules or styled-components for isolation

### **Debug Mode**

Enable debug logging:

```tsx
// Add to your component for debugging
useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
        console.log('Novu Inbox Debug Info:', {
            appId,
            userId,
            backendUrl: process.env.NEXT_PUBLIC_NOVU_BACKEND_URL,
            socketUrl: process.env.NEXT_PUBLIC_NOVU_SOCKET_URL
        });
    }
}, [appId, userId]);
```

## 📱 Examples

### **Complete Application Example**

```tsx
import React, { useState, useEffect } from 'react';
import { NovuInbox } from '@sparkstrand/notifications-react';

interface User {
    id: string;
    email: string;
    name: string;
}

interface AppConfig {
    appId: string;
    apiKey: string;
}

function App() {
    const [user, setUser] = useState<User | null>(null);
    const [appConfig, setAppConfig] = useState<AppConfig | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        // Simulate user authentication
        const authenticateUser = async () => {
            try {
                // Replace with your actual authentication logic
                const userData = await fetch('/api/auth/me');
                const user = await userData.json();
                
                setUser(user);
                setAppConfig({
                    appId: process.env.NEXT_PUBLIC_APP_ID!,
                    apiKey: user.apiKey
                });
            } catch (error) {
                console.error('Authentication failed:', error);
            } finally {
                setIsLoading(false);
            }
        };

        authenticateUser();
    }, []);

    if (isLoading) {
        return <div>Loading...</div>;
    }

    if (!user || !appConfig) {
        return <div>Please log in to continue.</div>;
    }

    return (
        <div className="app">
            <header className="app-header">
                <div className="header-content">
                    <h1>Welcome, {user.name}!</h1>
                    
                    {/* Novu Inbox Integration */}
                    <NovuInbox
                        appId={appConfig.appId}
                        apiKey={appConfig.apiKey}
                        userId={user.id}
                        theme="light"
                        position="top"
                        showUserPreferences={true}
                        onNotificationClick={(notification) => {
                            console.log('Notification clicked:', notification);
                        }}
                        onUnreadCountChange={(count) => {
                            console.log('Unread count:', count);
                        }}
                    />
                </div>
            </header>

            <main className="app-content">
                <h2>Your Dashboard</h2>
                <p>You have access to all your notifications through the bell icon above.</p>
                
                <div className="features">
                    <div className="feature">
                        <h3>📧 Email Notifications</h3>
                        <p>Receive important updates via email</p>
                    </div>
                    <div className="feature">
                        <h3>📱 Push Notifications</h3>
                        <p>Get instant alerts on your devices</p>
                    </div>
                    <div className="feature">
                        <h3>💬 In-App Notifications</h3>
                        <p>Stay updated within the application</p>
                    </div>
                </div>
            </main>
        </div>
    );
}

export default App;
```

### **Next.js Page Example**

```tsx
// pages/dashboard.tsx
import { NextPage } from 'next';
import { NovuInbox } from '@sparkstrand/notifications-react';
import { useSession } from 'next-auth/react';

const DashboardPage: NextPage = () => {
    const { data: session } = useSession();

    if (!session) {
        return <div>Please sign in to access the dashboard.</div>;
    }

    return (
        <div className="dashboard">
            <header className="dashboard-header">
                <h1>Dashboard</h1>
                
                <NovuInbox
                    appId={process.env.NEXT_PUBLIC_APP_ID!}
                    apiKey={session.user.apiKey}
                    userId={session.user.id}
                    theme="light"
                />
            </header>

            <main className="dashboard-content">
                <h2>Welcome back, {session.user.name}!</h2>
                <p>Your notifications are ready in the bell icon above.</p>
            </main>
        </div>
    );
};

export default DashboardPage;
```

## 🎯 Best Practices

### **Performance Optimization**

1. **Lazy Loading**: Load Novu components only when needed
2. **Memoization**: Use React.memo for components that don't change often
3. **Error Boundaries**: Wrap Novu components in error boundaries
4. **Loading States**: Show loading indicators while initializing

### **Security Considerations**

1. **API Key Management**: Never expose API keys in client-side code
2. **User Authentication**: Always verify user identity before showing notifications
3. **Rate Limiting**: Implement rate limiting on your notification endpoints
4. **HTTPS Only**: Use HTTPS in production for secure WebSocket connections

### **Accessibility**

1. **Screen Reader Support**: Ensure notification content is accessible
2. **Keyboard Navigation**: Support keyboard navigation for notification center
3. **High Contrast**: Provide high contrast themes for better visibility
4. **Focus Management**: Manage focus when notification center opens/closes

## 📚 Additional Resources

- [Novu Documentation](https://docs.novu.co/)
- [Novu Inbox Components](https://docs.novu.co/notification-center/react-components)
- [Our Notification System Guide](../README.md)
- [API Reference](../docs/API.md)

## 🆘 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review Novu's official documentation
3. Check our GitHub issues
4. Contact our support team

---

**Happy coding! 🎉**

Your Novu inbox integration is now ready to provide a beautiful, professional notification experience for your users. 