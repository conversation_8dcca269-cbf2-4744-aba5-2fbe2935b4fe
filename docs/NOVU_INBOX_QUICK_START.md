# 🚀 Novu Inbox Quick Start Guide

Get your Novu inbox up and running in under 5 minutes!

## ⚡ Quick Setup

### **1. Install Our Package (That's It!)**

```bash
npm install @sparkstrand/notifications-react
```

**✨ No need to install Novu packages separately!** Our package includes everything you need.

### **2. Add Environment Variables**

Create `.env.local`:
```bash
NEXT_PUBLIC_NOVU_BACKEND_URL=https://api.novu.co
NEXT_PUBLIC_NOVU_SOCKET_URL=https://ws.novu.co
NEXT_PUBLIC_NOVU_APP_ID=your-novu-app-identifier
```

### **3. Basic Implementation**

```tsx
import React from 'react';
import { NovuInbox } from '@sparkstrand/notifications-react';

function App() {
    return (
        <div className="app">
            <header>
                <h1>My Application</h1>
                
                {/* Add Novu Inbox Here */}
                <NovuInbox
                    appId="my-app"
                    apiKey="your-api-key"
                    userId="user-123"
                    theme="light"
                />
            </header>
            
            <main>
                <h2>Welcome to your dashboard!</h2>
                <p>Click the notification bell to see your notifications.</p>
            </main>
        </div>
    );
}

export default App;
```

### **4. That's It! 🎉**

Your Novu inbox is now working! Users can:
- Click the notification bell
- View all notifications
- Mark notifications as read
- Customize preferences

## 🔧 Advanced Features

### **Custom Handlers**

```tsx
<NovuInbox
    appId="my-app"
    apiKey="your-api-key"
    userId="user-123"
    theme="dark"
    position="bottom"
    showUserPreferences={true}
    onNotificationClick={(notification) => {
        console.log('Clicked:', notification);
        // Add your custom logic here
    }}
    onUnreadCountChange={(count) => {
        console.log('Unread:', count);
        // Update your app state
    }}
/>
```

### **Theme Switching**

```tsx
const [theme, setTheme] = useState<'light' | 'dark'>('light');

<NovuInbox
    appId="my-app"
    apiKey="your-api-key"
    userId="user-123"
    theme={theme}
/>
```

## 🎨 Custom Styling

```css
/* Custom styles for Novu inbox */
.novu-inbox-container {
    position: relative;
    z-index: 1000;
}

.novu-notification-bell {
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.novu-notification-bell:hover {
    transform: scale(1.1);
}
```

## 🚀 Production Ready

Your Novu inbox is production-ready with:
- ✅ Real-time updates
- ✅ Error handling
- ✅ Responsive design
- ✅ Accessibility support
- ✅ Customizable themes
- ✅ User preferences

## 📚 Next Steps

- Read the [Full Documentation](./NOVU_INBOX_INTEGRATION.md)
- Check out [Examples](./NOVU_INBOX_INTEGRATION.md#examples)
- Learn about [Advanced Features](./NOVU_INBOX_INTEGRATION.md#advanced-features)

---

**Need help?** Check the troubleshooting section in the full documentation or contact our support team. 