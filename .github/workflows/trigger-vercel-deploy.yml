name: Deploy to Vercel

on:
  push:
    branches: [ "main" ]

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
  NODE_OPTIONS: --max-old-space-size=2048

jobs:
  deploy:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'yarn'

      - name: Enable Corepack
        run: corepack enable

      - name: Install dependencies
        run: yarn install --frozen-lockfile --network-timeout 300000

      - name: Generate Prisma client
        run: cd apps/api && yarn db:generate
        env:
          NODE_OPTIONS: --max-old-space-size=2048

      - name: Build packages
        run: yarn build
        env:
          NODE_OPTIONS: --max-old-space-size=2048
        timeout-minutes: 10

      - name: Build API
        run: cd apps/api && yarn build
        env:
          NODE_OPTIONS: --max-old-space-size=2048
        timeout-minutes: 10

      - name: Install Vercel CLI
        run: npm install --global vercel@canary

      - name: Pull Vercel Environment Information
        run: |
          cd apps/api
          vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
        timeout-minutes: 5

      - name: Build Project Artifacts
        run: |
          cd apps/api
          vercel build --token=${{ secrets.VERCEL_TOKEN }}
        env:
          NODE_OPTIONS: --max-old-space-size=2048
        timeout-minutes: 10

      - name: Deploy Project Artifacts to Vercel
        run: |
          cd apps/api
          vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }}
        timeout-minutes: 5
