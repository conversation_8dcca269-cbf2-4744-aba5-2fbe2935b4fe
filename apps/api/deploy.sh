#!/bin/bash

# 🚀 Notification System Deployment Script
# This script automates the critical deployment steps

set -e  # Exit on any error

echo "🚀 Starting Notification System Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if .env file exists
if [ ! -f .env ]; then
    print_error ".env file not found!"
    echo "Please copy env.example to .env and configure your environment variables"
    exit 1
fi

print_status "Environment file found"

# Check if DATABASE_URL is set
if ! grep -q "DATABASE_URL=" .env; then
    print_error "DATABASE_URL not found in .env file"
    exit 1
fi

print_status "Database configuration found"

# Check if ADMIN_SECRET is set
if ! grep -q "ADMIN_SECRET=" .env; then
    print_warning "ADMIN_SECRET not found in .env file - admin operations will fail"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -eq 0 ]; then
    print_status "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npm run db:generate

if [ $? -eq 0 ]; then
    print_status "Prisma client generated successfully"
else
    print_error "Failed to generate Prisma client"
    exit 1
fi

# Build the application
echo "🏗️  Building application..."
npm run build

if [ $? -eq 0 ]; then
    print_status "Application built successfully"
else
    print_error "Failed to build application"
    exit 1
fi

# Check if database is accessible
echo "🔍 Testing database connection..."
npm run db:push -- --accept-data-loss

if [ $? -eq 0 ]; then
    print_status "Database connection successful"
else
    print_warning "Database connection failed - you may need to run migrations manually"
fi

# Seed the database
echo "🌱 Seeding database..."
npm run db:seed

if [ $? -eq 0 ]; then
    print_status "Database seeded successfully"
else
    print_warning "Database seeding failed - you may need to seed manually"
fi

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Start the server: npm start"
echo "2. Check health: curl http://localhost:3000/api/health"
echo "3. Test registration: curl -X POST http://localhost:3000/api/applications -H 'Content-Type: application/json' -d '{\"appId\":\"test\",\"name\":\"Test App\",\"contactEmail\":\"<EMAIL>\",\"contactName\":\"Test User\"}'"
echo ""
echo "📚 For more information, see DEPLOYMENT_CHECKLIST.md"
echo "🆘 If you encounter issues, check the troubleshooting section in the checklist" 