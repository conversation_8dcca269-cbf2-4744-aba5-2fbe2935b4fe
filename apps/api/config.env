# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/notifications_db"

# Novu Configuration
NOVU_API_KEY=your_novu_api_key_here
NOVU_APP_ID=your_novu_app_id_here

# Direct Provider Configuration (optional, for bypassing Novu)
RESEND_API_KEY=your_resend_api_key_here
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here

# Security Configuration
JWT_SECRET=your_jwt_secret_here
API_KEY_SECRET=your_api_key_secret_here

# Server Configuration
PORT=3001
NODE_ENV=development

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Database Connection Pool (optional)
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10
