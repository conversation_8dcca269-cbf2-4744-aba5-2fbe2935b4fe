/** @type {import('next').NextConfig} */
const nextConfig = {
    // External packages configuration
    serverExternalPackages: ['@novu/node'],
    // Optimize for Vercel
    turbo: {
        rules: {
            '*.svg': {
                loaders: ['@svgr/webpack'],
                as: '*.js',
            },
        },
    },

    // Memory and resource management
    experimental: {
        // Limit memory usage
        memoryBasedWorkers: true,
        // Reduce bundle size
        optimizePackageImports: ['@prisma/client'],
    },

    // Webpack configuration for better resource management
    webpack: (config, { isServer }) => {
        if (isServer) {
            // Limit server-side bundle size
            config.optimization.splitChunks = {
                chunks: 'all',
                maxSize: 200000, // 200KB limit (reduced from 244KB)
                cacheGroups: {
                    vendor: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendors',
                        chunks: 'all',
                    },
                },
            };
        }

        // Add memory limits
        config.performance = {
            maxEntrypointSize: 400000, // 400KB (reduced from 512KB)
            maxAssetSize: 400000, // 400KB (reduced from 512KB)
        };

        // Optimize for production
        if (!config.optimization) {
            config.optimization = {};
        }

        config.optimization.minimize = true;
        config.optimization.minimizer = config.optimization.minimizer || [];

        return config;
    },

    // API route configuration
    async headers() {
        return [
            {
                source: '/api/(.*)',
                headers: [
                    {
                        key: 'X-Frame-Options',
                        value: 'DENY',
                    },
                    {
                        key: 'X-Content-Type-Options',
                        value: 'nosniff',
                    },
                    {
                        key: 'Referrer-Policy',
                        value: 'origin-when-cross-origin',
                    },
                    // Add timeout headers
                    {
                        key: 'X-Request-Timeout',
                        value: '30000',
                    },
                ],
            },
        ];
    },

    // Rewrite configuration
    async rewrites() {
        return [
            {
                source: '/api/notifications/:path*',
                destination: '/api/notifications/:path*',
            },
            {
                source: '/api/health',
                destination: '/api/health',
            },
        ];
    },

    // Environment-specific configurations
    env: {
        // Set reasonable defaults for timeouts
        DATABASE_TIMEOUT_MS: process.env.DATABASE_TIMEOUT_MS || '10000',
        EXTERNAL_API_TIMEOUT_MS: process.env.EXTERNAL_API_TIMEOUT_MS || '15000',
        TRANSACTION_TIMEOUT_MS: process.env.TRANSACTION_TIMEOUT_MS || '60000',
    },

    // Server configuration
    serverRuntimeConfig: {
        // Memory limits (reduced for Vercel)
        maxMemoryUsage: process.env.MAX_MEMORY_USAGE || '256MB',
        // Timeout limits
        requestTimeout: process.env.REQUEST_TIMEOUT || '30000',
        // Connection limits
        maxConnections: process.env.MAX_CONNECTIONS || '50',
    },

    // Production optimizations
    swcMinify: true,
    compress: true,

    // Reduce bundle size
    compiler: {
        removeConsole: process.env.NODE_ENV === 'production',
    },
};

module.exports = nextConfig;
