# =============================================================================
# NOTIFICATION SYSTEM ENVIRONMENT VARIABLES
# =============================================================================
# Copy this file to .env and fill in your actual values

# =============================================================================
# CORE CONFIGURATION
# =============================================================================

# Node Environment
NODE_ENV="development"
PORT=3000

# Base URL (for webhooks and links)
NEXT_PUBLIC_BASE_URL="http://localhost:3000"

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Connection String
DATABASE_URL="postgresql://username:password@localhost:5432/notifications"

# Database Timeouts
DATABASE_TIMEOUT_MS=5000
EXTERNAL_API_TIMEOUT_MS=10000
TRANSACTION_TIMEOUT_MS=30000

# =============================================================================
# SECURITY & ADMINISTRATION
# =============================================================================

# Admin Authentication
ADMIN_SECRET="your-super-secure-admin-secret-here"
ADMIN_EMAIL="<EMAIL>"

# =============================================================================
# NOTIFICATION PROVIDERS (Global Defaults)
# =============================================================================

# Novu Configuration
NOVU_API_KEY="your-novu-api-key"
NOVU_APP_ID="your-novu-app-id"

# Resend Configuration
RESEND_API_KEY="your-resend-api-key"
RESEND_FROM_EMAIL="<EMAIL>"

# Twilio Configuration
TWILIO_ACCOUNT_SID="your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"
TWILIO_PHONE_NUMBER="+**********"

# =============================================================================
# SLACK INTEGRATION
# =============================================================================

# Slack Bot Configuration
SLACK_BOT_TOKEN="xoxb-your-slack-bot-token"
SLACK_ADMIN_CHANNEL="#admin"
SLACK_WEBHOOK_SECRET="your-webhook-secret-for-slack"
SLACK_SIGNING_SECRET="your-slack-signing-secret"

# =============================================================================
# SERVER PERFORMANCE
# =============================================================================

# Memory and Connection Limits
MAX_MEMORY_USAGE="512MB"
REQUEST_TIMEOUT=30000
MAX_CONNECTIONS=100

# =============================================================================
# DEVELOPMENT & DEMO (Optional)
# =============================================================================

# Demo Mode (for development/testing)
NEXT_PUBLIC_ADMIN_SECRET="demo-secret"

# =============================================================================
# NOTES
# =============================================================================

# Required Variables (must be set):
# - DATABASE_URL: PostgreSQL connection string
# - ADMIN_SECRET: Secret for admin operations
# - ADMIN_EMAIL: Admin email for notifications

# Highly Recommended:
# - SLACK_BOT_TOKEN: For Slack admin workflow
# - SLACK_ADMIN_CHANNEL: Admin channel for notifications
# - RESEND_API_KEY: For sending confirmation emails
# - RESEND_FROM_EMAIL: Sender email for notifications

# Optional:
# - NOVU_API_KEY: For push notifications
# - NOVU_APP_ID: For Novu integration
# - TWILIO_ACCOUNT_SID: For SMS notifications
# - TWILIO_AUTH_TOKEN: For Twilio authentication

# Development Only:
# - NODE_ENV: Set to "production" in production
# - NEXT_PUBLIC_ADMIN_SECRET