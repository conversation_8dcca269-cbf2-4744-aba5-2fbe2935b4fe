services:
  postgres:
    image: postgres:latest
    container_name: notifications-postgres-db
    environment:
      POSTGRES_USER: notificationsUser
      POSTGRES_PASSWORD: notificationsPassword
      POSTGRES_DB: notifications
    ports:
      - "${DATABASE_PORT:-5432}:5432"
    volumes:
      - notifications-postgres-data:/var/lib/postgresql/data
    env_file:
      - .env
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U notificationsUser -d notifications"]
      interval: 10s
      timeout: 5s
      retries: 5

  notifications:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: notifications
    ports:
      - "${APP_PORT:-3000}:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=*********************************************************************************/
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./apps/:/apps
      - /apps/api/node_modules
    command: >
      sh -c "until pg_isready -h notifications-postgres-db -p 5432 -U notificationsUser -d notifications; do sleep 1; done &&
           npm run prisma:push &&
           npm run prisma:seed &&
           npm run dev"

  adminer:
    image: adminer
    ports:
      - 8080:8080
    environment:
      ADMINER_DEFAULT_SERVER: notifications-postgres-db

volumes:
  notifications-postgres-data: