# 🚀 Deployment Checklist for Notification System

## ⚠️ CRITICAL ISSUES FIXED

### 1. Schema Alignment Issues ✅
- **Fixed**: Database schema now uses `appId` consistently instead of `apiKey`
- **Fixed**: Added missing `phone` field to User model for SMS notifications
- **Fixed**: Corrected field mappings between API routes and database schema
- **Fixed**: Added missing `novuNotificationId` field positioning

### 2. Dependencies ✅
- **Fixed**: Added missing `bcryptjs` and `@types/bcryptjs` packages
- **Fixed**: All required packages are now in package.json

### 3. Database Relations ✅
- **Fixed**: Corrected foreign key constraint naming conflicts
- **Fixed**: Proper relation mappings between models

## 🔧 PRE-DEPLOYMENT STEPS

### 1. Database Setup
```bash
# Generate Prisma client
npm run db:generate

# Run migrations (if using migrations)
npm run db:migrate

# Or push schema directly (for development)
npm run db:push

# Seed the database
npm run db:seed
```

### 2. Environment Variables
Copy `env.example` to `.env` and configure:

#### Required Variables:
```bash
# Database
DATABASE_URL="postgresql://username:password@host:port/database"

# Admin
ADMIN_SECRET="your-super-secure-admin-secret"
ADMIN_EMAIL="<EMAIL>"

# Base URL
NEXT_PUBLIC_BASE_URL="https://yourdomain.com"
```

#### Optional but Recommended:
```bash
# Email (Resend)
RESEND_API_KEY="your-resend-api-key"
RESEND_FROM_EMAIL="<EMAIL>"

# SMS (Twilio)
TWILIO_ACCOUNT_SID="your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"
TWILIO_PHONE_NUMBER="+**********"

# Push Notifications (Novu)
NOVU_API_KEY="your-novu-api-key"
NOVU_APP_ID="your-novu-app-id"

# Slack Integration
SLACK_BOT_TOKEN="xoxb-your-slack-bot-token"
SLACK_ADMIN_CHANNEL="#admin"
SLACK_WEBHOOK_SECRET="your-webhook-secret"
SLACK_SIGNING_SECRET="your-slack-signing-secret"
```

### 3. Database Verification
```bash
# Check database connection
curl https://yourdomain.com/api/health

# Expected response: {"status":"healthy","database":{"status":"connected"}}
```

## 🚀 DEPLOYMENT STEPS

### 1. Build and Deploy
```bash
# Install dependencies
npm install

# Build the application
npm run build

# Start the server
npm start
```

### 2. Health Check
```bash
# Verify system health
curl https://yourdomain.com/api/health
```

### 3. Test Core Functionality
```bash
# Test application registration
curl -X POST https://yourdomain.com/api/applications \
  -H "Content-Type: application/json" \
  -d '{
    "appId": "test-app",
    "name": "Test Application",
    "contactEmail": "<EMAIL>",
    "contactName": "Test User"
  }'

# Test notification creation (after approval)
curl -X POST https://yourdomain.com/api/notifications \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "appId": "test-app",
    "userId": "user-id",
    "title": "Test Notification",
    "message": "This is a test notification"
  }'
```

## 🔍 POST-DEPLOYMENT VERIFICATION

### 1. System Health
- [ ] Health endpoint returns `{"status":"healthy"}`
- [ ] Database connection successful
- [ ] All environment variables loaded correctly

### 2. Core Features
- [ ] Application registration endpoint works
- [ ] Admin approval workflow functions
- [ ] Notification creation and delivery works
- [ ] API key authentication functions

### 3. Provider Integration
- [ ] Email delivery (Resend) works (if configured)
- [ ] SMS delivery (Twilio) works (if configured)
- [ ] Push notifications (Novu) work (if configured)

## 🚨 TROUBLESHOOTING

### Common Issues:

#### 1. Database Connection Failed
```bash
# Check DATABASE_URL format
# Verify database is accessible
# Check firewall settings
```

#### 2. Prisma Client Not Generated
```bash
# Run: npm run db:generate
# Check for TypeScript compilation errors
```

#### 3. Environment Variables Not Loading
```bash
# Verify .env file exists
# Check variable names match exactly
# Restart the application after changes
```

#### 4. API Key Validation Fails
```bash
# Check bcryptjs is installed
# Verify API key format in database
# Check appId matches exactly
```

## 📊 MONITORING

### 1. Health Endpoints
- `/api/health` - Full system health check
- `/api/health` (HEAD) - Lightweight health check

### 2. Logs to Monitor
- Database connection errors
- API key validation failures
- Provider delivery errors
- Rate limit violations

### 3. Metrics to Track
- Response times
- Error rates
- Database connection status
- Memory usage

## 🔒 SECURITY CONSIDERATIONS

### 1. API Keys
- API keys are hashed using bcryptjs
- Keys can be revoked by admins
- Expiration dates supported

### 2. Rate Limiting
- Per-minute, per-hour, per-day limits
- Configurable per application
- Automatic blocking on violation

### 3. Admin Access
- Admin secret required for sensitive operations
- Application approval workflow
- Audit trail for all admin actions

## 📝 NOTES

- The system is designed to be self-contained
- All external provider integrations are optional
- Fallback strategies are implemented for reliability
- The system will work with just the database configured
- Additional providers can be added without code changes

## 🎯 SUCCESS CRITERIA

The system is successfully deployed when:
1. ✅ Health endpoint returns healthy status
2. ✅ Database operations work correctly
3. ✅ Application registration flow functions
4. ✅ Notification creation and delivery works
5. ✅ API key authentication functions properly
6. ✅ Admin approval workflow operates correctly

## 🆘 SUPPORT

If you encounter issues:
1. Check the health endpoint for system status
2. Review application logs for error details
3. Verify all environment variables are set correctly
4. Ensure database is accessible and schema is up to date
5. Check that all required packages are installed 