import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
    // Starting database seed...
    console.log('🌱 Starting database seed...');

    // Create default application
    const defaultApp = await prisma.application.upsert({
        where: { appId: 'default-app-key' },
        update: {},
        create: {
            appId: 'default-app-key',
            name: 'Default Application',
            description: 'Default application for development and testing',
            // Use environment variables for provider configurations, fallback to defaults
            novuAppId: process.env.NOVU_APP_ID || 'default-novu-app',
            novuApiKey: process.env.NOVU_API_KEY || 'default-novu-key',
            resendApiKey: process.env.RESEND_API_KEY || null,
            twilioAccountSid: process.env.TWILIO_ACCOUNT_SID || null,
            twilioAuthToken: process.env.TWILIO_AUTH_TOKEN || null,
            defaultProvider: 'NOVU',
            metadata: {
                defaultChannels: ['inApp', 'email'],
                defaultPriority: 'NORMAL',
                enableQuietHours: false,
            },
        },
    });

    console.log('✅ Created default application:', defaultApp.name);
    console.log(defaultApp);

    // Create test user
    const testUser = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            name: 'Test User',
            phone: '+**********', // Add test phone number
            preferences: {
                theme: 'light',
                language: 'en',
            },
        },
    });

    console.log('✅ Created test user:', testUser.email);

    // Create default notification preferences for test user
    const defaultPreferences = [
        { notificationType: 'INFO', enabled: true, priority: 'NORMAL' },
        { notificationType: 'SUCCESS', enabled: true, priority: 'LOW' },
        { notificationType: 'WARNING', enabled: true, priority: 'HIGH' },
        { notificationType: 'ERROR', enabled: true, priority: 'URGENT' },
    ];

    for (const pref of defaultPreferences) {
        await prisma.userNotificationPreferences.upsert({
            where: {
                userId_notificationType: {
                    userId: testUser.id,
                    notificationType: pref.notificationType,
                },
            },
            update: {},
            create: {
                userId: testUser.id,
                ...pref,
            },
        });
    }

    console.log('✅ Created default notification preferences');

    // Create application-specific user subscription
    await prisma.applicationUserSubscription.upsert({
        where: {
            userId_appId: {
                userId: testUser.id,
                appId: defaultApp.appId,
            },
        },
        update: {},
        create: {
            userId: testUser.id,
            appId: defaultApp.appId,
            novuSubscriberId: `user-${testUser.id}-app-${defaultApp.appId}`,
            preferences: {
                channels: ['inApp', 'email'],
                quietHours: false,
            },
        },
    });

    console.log('✅ Created application-specific user subscription');

    // Create sample notification templates
    const templates = [
        {
            name: 'welcome',
            description: 'Welcome notification for new users',
            title: 'Welcome to our platform!',
            message: 'We\'re excited to have you on board.',
            type: 'INFO',
            priority: 'NORMAL',
            channels: ['inApp', 'email'],
            emailSubject: 'Welcome to our platform!',
            emailBody: '<h1>Welcome!</h1><p>We\'re excited to have you on board.</p>',
        },
        {
            name: 'task_completed',
            description: 'Notification when a task is completed',
            title: 'Task Completed',
            message: 'Your task has been completed successfully.',
            type: 'SUCCESS',
            priority: 'LOW',
            channels: ['inApp', 'email'],
            emailSubject: 'Task Completed Successfully',
            emailBody: '<h1>Task Completed</h1><p>Your task has been completed successfully.</p>',
        },
        {
            name: 'system_maintenance',
            description: 'System maintenance notification',
            title: 'Scheduled Maintenance',
            message: 'We will be performing scheduled maintenance.',
            type: 'WARNING',
            priority: 'HIGH',
            channels: ['inApp', 'email', 'push'],
            emailSubject: 'Scheduled Maintenance Notice',
            emailBody: '<h1>Scheduled Maintenance</h1><p>We will be performing scheduled maintenance.</p>',
        },
    ];

    for (const template of templates) {
        await prisma.notificationTemplate.upsert({
            where: { name: template.name },
            update: {},
            create: template,
        });
    }

    console.log('✅ Created sample notification templates');

    // Create sample notifications
    const sampleNotifications = [
        {
            title: 'Welcome to Notifications System',
            message: 'This is your first notification from the system.',
            type: 'INFO',
            priority: 'NORMAL',
            recipientId: testUser.id,
            appId: defaultApp.appId,
            status: 'DELIVERED',
            metadata: {
                template: 'welcome',
                source: 'seed',
            },
        },
        {
            title: 'System Ready',
            message: 'The notification system is now ready for use.',
            type: 'SUCCESS',
            priority: 'LOW',
            recipientId: testUser.id,
            appId: defaultApp.appId,
            status: 'DELIVERED',
            metadata: {
                template: 'system_ready',
                source: 'seed',
            },
        },
    ];

    for (const notification of sampleNotifications) {
        const createdNotification = await prisma.notification.create({
            data: notification,
        });

        // Create delivery status records
        await prisma.deliveryStatus.createMany({
            data: [
                {
                    notificationId: createdNotification.id,
                    channel: 'inApp',
                    provider: 'system',
                    status: 'DELIVERED',
                    deliveredAt: new Date(),
                },
                {
                    notificationId: createdNotification.id,
                    channel: 'email',
                    provider: 'novu',
                    status: 'DELIVERED',
                    deliveredAt: new Date(),
                },
            ],
        });
    }

    console.log('✅ Created sample notifications with delivery status');

    console.log('🎉 Database seeding completed successfully!');
}

main()
    .catch((e) => {
        console.error('❌ Error during seeding:', e);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
    });
