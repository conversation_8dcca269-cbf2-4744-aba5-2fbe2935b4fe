// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Users table - stores user information and preferences
model User {
  id     String  @id @default(cuid())
  email  String  @unique
  name   String?
  avatar String?
  phone  String? // Add phone field for SMS notifications

  // Notification preferences
  preferences Json? @default("{}")

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  notifications            Notification[]                @relation("UserNotifications")
  notificationPreferences  UserNotificationPreferences[] @relation("UserNotificationPreferences")
  applicationSubscriptions ApplicationUserSubscription[] @relation("UserApplicationSubscriptions")

  // External provider IDs (global defaults)
  novuSubscriberId String? @map("novu_subscriber_id")

  @@map("users")
}

// User notification preferences - granular control over notification types and channels
model UserNotificationPreferences {
  id     String @id @default(cuid())
  userId String @map("user_id")

  // Notification type preferences
  notificationType String  @map("notification_type") // INFO, SUCCESS, WARNING, ERROR, etc.
  enabled          Boolean @default(true)
  priority         String  @default("NORMAL") // LOW, NORMAL, HIGH, URGENT

  // Channel preferences
  emailEnabled   Boolean @default(true) @map("email_enabled")
  smsEnabled     Boolean @default(false) @map("sms_enabled")
  pushEnabled    Boolean @default(true) @map("push_enabled")
  inAppEnabled   Boolean @default(true) @map("in_app_enabled")
  webhookEnabled Boolean @default(false) @map("webhook_enabled")

  // Quiet hours
  quietHoursEnabled Boolean @default(false) @map("quiet_hours_enabled")
  quietHoursStart   String? @map("quiet_hours_start") // HH:mm format
  quietHoursEnd     String? @map("quiet_hours_end") // HH:mm format

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user User @relation("UserNotificationPreferences", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, notificationType])
  @@map("user_notification_preferences")
}

// Application-specific user subscriptions and provider IDs
model ApplicationUserSubscription {
  id     String @id @default(cuid())
  userId String @map("user_id")
  appId  String @map("app_id")

  // Provider-specific subscriber IDs
  novuSubscriberId String? @unique @map("novu_subscriber_id")

  // User preferences for this specific application
  preferences Json? @default("{}")

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user User        @relation("UserApplicationSubscriptions", fields: [userId], references: [id], onDelete: Cascade)
  app  Application @relation("ApplicationUserSubscriptions", fields: [appId], references: [appId], onDelete: Cascade)

  @@unique([userId, appId])
  @@map("application_user_subscriptions")
}

enum AppRegistrationStatus {
  PENDING_APPROVAL
  APPROVED
  REJECTED
}

enum FallbackStrategy {
  FAILOVER
  PARALLEL
  HYBRID
  NONE
}

// App Registration Requests - tracks registration requests and approval status
model AppRegistration {
  id          String   @id @default(cuid())
  appId       String   @unique @map("app_id")
  name        String
  description String?
  
  // Contact information
  contactEmail String @map("contact_email")
  contactName  String @map("contact_name")
  website      String?
  businessType String? @map("business_type")
  expectedVolume Int?  @map("expected_volume")
  
  // Registration status
  status      AppRegistrationStatus @default(PENDING_APPROVAL)
  adminNotes  String?
  approvedAt  DateTime? @map("approved_at")
  approvedBy  String?  @map("approved_by")
  
  // Provider configurations (encrypted in production)
  novuAppId    String? @map("novu_app_id")
  novuApiKey   String? @map("novu_api_key")
  resendApiKey String? @map("resend_api_key")
  twilioAccountSid String? @map("twilio_account_sid")
  twilioAuthToken  String? @map("twilio_auth_token")
  
  // Configuration
  fallbackStrategy FallbackStrategy? @default(NONE) @map("fallback_strategy") // failover, parallel, hybrid
  maxRetries       Int?    @default(3) @map("max_retries")
  retryDelay       Int?    @default(1000) @map("retry_delay")
  rateLimits       Json?   @default("{}") @map("rate_limits")
  
  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  apiKeys AppApiKey[] @relation("AppRegistrationApiKeys")

  @@map("app_registrations")
}

// Secure API Keys - stores hashed API keys for authentication
model AppApiKey {
  id        String   @id @default(cuid())
  appId     String   @map("app_id")
  apiKeyHash String  @map("api_key_hash") // Hashed version of the API key
  name      String  // Required human-readable name for the key (e.g., "Production Key", "Development Key")
  
  // Key status
  isActive   Boolean @default(true) @map("is_active")
  expiresAt  DateTime? @map("expires_at")
  lastUsedAt DateTime? @map("last_used_at")
  
  // Security
  permissions String[] @default([]) // send_notifications, read_notifications, manage_preferences.
  
  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  application Application @relation("ApplicationApiKeys", fields: [appId], references: [appId], onDelete: Cascade, map: "fk_app_api_keys_application")
  appRegistration AppRegistration? @relation("AppRegistrationApiKeys", fields: [appId], references: [appId], onDelete: Cascade, map: "fk_app_api_keys_registration")

  @@map("app_api_keys")
}

enum DefaultProvider {
  NOVU
  DIRECT
  HYBRID
}
// Applications - MAIN TABLE for all active applications using the notification system
model Application {
  appId       String   @id @map("app_id") // This is the primary identifier
  name        String
  description String?
  webhookUrl  String? @map("webhook_url")

  // Provider-specific configurations
  novuAppId  String? @map("novu_app_id")
  novuApiKey String? @map("novu_api_key")
  resendApiKey     String? @map("resend_api_key")
  twilioAccountSid String? @map("twilio_account_sid")
  twilioAuthToken  String? @map("twilio_auth_token")

  // Default provider selection
  defaultProvider DefaultProvider @default(NOVU) @map("default_provider")

  // Configuration
  fallbackStrategy FallbackStrategy? @default(NONE) @map("fallback_strategy")
  maxRetries       Int?    @default(3) @map("max_retries")
  retryDelay       Int?    @default(1000) @map("retry_delay")
  rateLimits       Json?   @default("{}") @map("rate_limits")
  
  // Status and metadata
  isActive   Boolean @default(true) @map("is_active")
  metadata   Json?   @default("{}")

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  notifications     Notification[]                @relation("ApplicationNotifications")
  userSubscriptions ApplicationUserSubscription[] @relation("ApplicationUserSubscriptions")
  apiKeys          AppApiKey[]                   @relation("ApplicationApiKeys")

  @@map("applications")
}

// Core notifications table
model Notification {
  id String @id @default(cuid())

  // Basic notification data
  title    String
  message  String
  type     String // INFO, SUCCESS, WARNING, ERROR, etc.
  priority String @default("NORMAL") // LOW, NORMAL, HIGH, URGENT

  // Recipients and senders
  recipientId String  @map("recipient_id")
  senderId    String? @map("sender_id")
  appId       String  @map("app_id")

  // Status tracking
  status String @default("PENDING") // PENDING, SENT, DELIVERED, READ, FAILED

  // Scheduling
  scheduledFor DateTime? @map("scheduled_for")
  expiresAt    DateTime? @map("expires_at")

  // Metadata and custom data
  metadata Json? @default("{}")

  // External provider tracking
  novuNotificationId String? @unique @map("novu_notification_id")

  // Timestamps
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  readAt    DateTime? @map("read_at")

  // Relations
  recipient         User              @relation("UserNotifications", fields: [recipientId], references: [id], onDelete: Cascade)
  app               Application       @relation("ApplicationNotifications", fields: [appId], references: [appId], onDelete: Cascade)
  deliveries        DeliveryStatus[]  @relation("NotificationDeliveries")
  webhookDeliveries WebhookDelivery[] @relation("NotificationWebhookDeliveries")

  @@map("notifications")
}

// Delivery status tracking - tracks delivery across different channels and providers
model DeliveryStatus {
  id             String @id @default(cuid())
  notificationId String @map("notification_id")

  // Channel and provider information
  channel  String // email, sms, push, in_app, webhook
  provider String // novu, resend, twilio, etc.

  // Status tracking
  status   String // PENDING, SENT, DELIVERED, FAILED, BOUNCED
  attempts Int    @default(0)

  // Delivery details
  deliveredAt  DateTime? @map("delivered_at")
  errorMessage String?   @map("error_message")
  errorCode    String?   @map("error_code")

  // Provider-specific data
  providerMessageId String? @map("provider_message_id")
  providerResponse  Json?   @default("{}")

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  notification Notification @relation("NotificationDeliveries", fields: [notificationId], references: [id], onDelete: Cascade)

  @@unique([notificationId, channel, provider])
  @@map("delivery_status")
}

// Notification templates - reusable notification structures
model NotificationTemplate {
  id          String @id @default(cuid())
  name        String @unique
  description String?

  // Template content
  title    String
  message  String
  type     String
  priority String @default("NORMAL")

  // Channel-specific content
  emailSubject String? @map("email_subject")
  emailBody    String? @map("email_body")
  smsMessage   String? @map("sms_message")
  pushTitle    String? @map("push_title")
  pushBody     String? @map("push_body")

  // Configuration
  channels String[] @default([])
  metadata Json?    @default("{}")

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("notification_templates")
}

// Webhook endpoints - for external integrations
model WebhookEndpoint {
  id     String @id @default(cuid())
  name   String
  url    String
  secret String

  // Configuration
  events  String[] @default([]) // notification.created, notification.delivered, etc.
  enabled Boolean  @default(true)

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  deliveries WebhookDelivery[] @relation("WebhookDeliveries")

  @@map("webhook_endpoints")
}

// Webhook delivery logs - tracks webhook delivery attempts
model WebhookDelivery {
  id             String @id @default(cuid())
  webhookId      String @map("webhook_id")
  notificationId String @map("notification_id")

  // Delivery details
  status       String // SUCCESS, FAILED, TIMEOUT
  responseCode Int?
  responseBody String?
  errorMessage String? @map("error_message")

  // Attempt tracking
  attempts    Int       @default(0)
  nextRetryAt DateTime? @map("next_retry_at")

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  webhook      WebhookEndpoint @relation("WebhookDeliveries", fields: [webhookId], references: [id], onDelete: Cascade)
  notification Notification    @relation("NotificationWebhookDeliveries", fields: [notificationId], references: [id], onDelete: Cascade)

  @@map("webhook_deliveries")
}
