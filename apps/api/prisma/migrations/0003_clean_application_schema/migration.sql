-- Clean up the application schema to make applications the main table
-- and app_registrations just for the approval workflow

-- First, drop the old api<PERSON>ey column from applications table
ALTER TABLE "applications" DROP COLUMN IF EXISTS "api_key";

-- Update the applications table to use appId as primary key
-- (This will be done by recreating the table with the new schema)

-- Create new applications table with proper structure
CREATE TABLE "applications_new" (
    "app_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "webhook_url" TEXT,
    "novu_app_id" TEXT,
    "novu_api_key" TEXT,
    "resend_api_key" TEXT,
    "twilio_account_sid" TEXT,
    "twilio_auth_token" TEXT,
    "default_provider" TEXT NOT NULL DEFAULT 'novu',
    "fallback_strategy" TEXT,
    "max_retries" INTEGER DEFAULT 3,
    "retry_delay" INTEGER DEFAULT 1000,
    "rate_limits" JSONB DEFAULT '{}',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "metadata" JSONB DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "applications_new_pkey" PRIMARY KEY ("app_id")
);

-- Copy existing data from old applications table (if any exists)
-- Note: This will be empty since we're starting fresh
INSERT INTO "applications_new" (
    "app_id", "name", "description", "webhook_url",
    "novu_app_id", "novu_api_key", "resend_api_key",
    "twilio_account_sid", "twilio_auth_token", "default_provider",
    "fallback_strategy", "max_retries", "retry_delay", "rate_limits",
    "is_active", "metadata", "created_at", "updated_at"
)
SELECT 
    COALESCE("app_id", "id") as "app_id",
    "name",
    "description",
    "webhook_url",
    "novu_app_id",
    "novu_api_key",
    "resend_api_key",
    "twilio_account_sid",
    "twilio_auth_token",
    COALESCE("default_provider", 'novu') as "default_provider",
    NULL as "fallback_strategy",
    3 as "max_retries",
    1000 as "retry_delay",
    '{}' as "rate_limits",
    true as "is_active",
    COALESCE("config", '{}') as "metadata",
    "created_at",
    "updated_at"
FROM "applications"
WHERE "applications"."app_id" IS NOT NULL OR "applications"."id" IS NOT NULL;

-- Drop the old applications table
DROP TABLE "applications";

-- Rename the new table to applications
ALTER TABLE "applications_new" RENAME TO "applications";

-- Update the app_api_keys table to reference applications instead of app_registrations
ALTER TABLE "app_api_keys" DROP CONSTRAINT IF EXISTS "app_api_keys_app_id_fkey";

-- Add the new foreign key constraint
ALTER TABLE "app_api_keys" ADD CONSTRAINT "app_api_keys_app_id_fkey" 
FOREIGN KEY ("app_id") REFERENCES "applications"("app_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Update the application_user_subscriptions table to reference applications.app_id
ALTER TABLE "application_user_subscriptions" DROP CONSTRAINT IF EXISTS "application_user_subscriptions_app_id_fkey";

-- Add the new foreign key constraint
ALTER TABLE "application_user_subscriptions" ADD CONSTRAINT "application_user_subscriptions_app_id_fkey" 
FOREIGN KEY ("app_id") REFERENCES "applications"("app_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Update the notifications table to reference applications.app_id
ALTER TABLE "notifications" DROP CONSTRAINT IF EXISTS "notifications_app_id_fkey";

-- Add the new foreign key constraint
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_app_id_fkey" 
FOREIGN KEY ("app_id") REFERENCES "applications"("app_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "applications_app_id_idx" ON "applications"("app_id");
CREATE INDEX IF NOT EXISTS "applications_is_active_idx" ON "applications"("is_active");
CREATE INDEX IF NOT EXISTS "app_api_keys_app_id_idx" ON "app_api_keys"("app_id");
CREATE INDEX IF NOT EXISTS "app_api_keys_is_active_idx" ON "app_api_keys"("is_active"); 