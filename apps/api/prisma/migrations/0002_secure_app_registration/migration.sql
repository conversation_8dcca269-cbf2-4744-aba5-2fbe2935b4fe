-- CreateTable
CREATE TABLE "app_registrations" (
    "id" TEXT NOT NULL,
    "app_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "contact_email" TEXT NOT NULL,
    "contact_name" TEXT NOT NULL,
    "website" TEXT,
    "business_type" TEXT,
    "expected_volume" INTEGER,
    "status" TEXT NOT NULL DEFAULT 'PENDING_APPROVAL',
    "admin_notes" TEXT,
    "approved_at" TIMESTAMP(3),
    "approved_by" TEXT,
    "novu_app_id" TEXT,
    "novu_api_key" TEXT,
    "resend_api_key" TEXT,
    "twilio_account_sid" TEXT,
    "twilio_auth_token" TEXT,
    "fallback_strategy" TEXT,
    "max_retries" INTEGER DEFAULT 3,
    "retry_delay" INTEGER DEFAULT 1000,
    "rate_limits" JSONB DEFAULT '{}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "app_registrations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "app_api_keys" (
    "id" TEXT NOT NULL,
    "app_id" TEXT NOT NULL,
    "api_key_hash" TEXT NOT NULL,
    "name" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "expires_at" TIMESTAMP(3),
    "last_used_at" TIMESTAMP(3),
    "permissions" TEXT[] DEFAULT '[]',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "app_api_keys_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "app_registrations_app_id_key" ON "app_registrations"("app_id");

-- CreateIndex
CREATE UNIQUE INDEX "app_api_keys_api_key_hash_key" ON "app_api_keys"("api_key_hash");

-- AddForeignKey
ALTER TABLE "app_api_keys" ADD CONSTRAINT "app_api_keys_app_id_fkey" FOREIGN KEY ("app_id") REFERENCES "app_registrations"("app_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add app_id column to applications table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'applications' AND column_name = 'app_id') THEN
        ALTER TABLE "applications" ADD COLUMN "app_id" TEXT;
    END IF;
END $$;

-- Create index on app_id if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE tablename = 'applications' AND indexname = 'applications_app_id_key') THEN
        CREATE UNIQUE INDEX "applications_app_id_key" ON "applications"("app_id");
    END IF;
END $$; 