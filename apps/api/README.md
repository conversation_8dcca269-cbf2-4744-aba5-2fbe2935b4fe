# Notifications API Server

This is the Next.js API server for the Spark Strand Notifications System, built with PostgreSQL and Prisma, featuring a unified multi-tenant notification service.

## Features

- **Multi-Tenant Architecture**: Support for multiple applications with isolated configurations
- **Unified Notification Service**: Single API for all notification operations
- **Automatic Provider Routing**: Smart selection of notification providers based on channel requirements
- **Fallback Strategies**: Configurable fallback mechanisms for reliability
- **PostgreSQL Database**: Robust, ACID-compliant database for notification storage
- **Prisma ORM**: Type-safe database operations with auto-generated client
- **Provider Integrations**: Novu, Resend, and Twilio support with vendor independence
- **User Preferences**: Granular control over notification types and channels
- **Rate Limiting**: Per-application rate limits and quotas
- **Monitoring & Analytics**: Real-time metrics and performance tracking

## Architecture Overview

The system uses a **unified notification service** that automatically:
- Routes notifications through the best available providers
- Handles fallbacks when providers fail
- Manages multiple applications with complete isolation
- Optimizes costs by using cheaper providers when available

### Key Components

- **Notification Service**: Unified API for all notification operations
- **Provider Factory**: Manages provider instances and routing logic
- **Multi-Tenant Service**: Handles application registration and isolation
- **Database Layer**: Stores notifications, user preferences, and delivery status

## Database Schema

The database includes the following main tables:

- **`users`**: User accounts and basic preferences
- **`user_notification_preferences`**: Granular notification settings per user
- **`applications`**: Different apps that can send notifications
- **`notifications`**: Core notification records
- **`delivery_status`**: Tracking delivery across channels and providers
- **`notification_templates`**: Reusable notification structures
- **`webhook_endpoints`**: External integration endpoints
- **`webhook_deliveries`**: Webhook delivery tracking

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Database Setup

#### Option A: Supabase (Recommended for development)

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > Database
3. Copy the connection string
4. Update your `.env` file:

```bash
DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres"
```

#### Option B: Local PostgreSQL

1. Install PostgreSQL locally
2. Create a database:
```sql
CREATE DATABASE notifications_db;
```
3. Update your `.env` file:
```bash
DATABASE_URL="postgresql://username:password@localhost:5432/notifications_db"
```

### 3. Environment Configuration

Copy `env.example` to `.env` and configure:

```bash
cp env.example .env
```

#### Required Variables
- `DATABASE_URL`: PostgreSQL connection string
- `NOVU_API_KEY`: Your Novu API key
- `NOVU_APP_ID`: Your Novu app ID

#### Optional Variables
- `RESEND_API_KEY`: For direct email delivery via Resend
- `TWILIO_ACCOUNT_SID` & `TWILIO_AUTH_TOKEN`: For SMS delivery via Twilio
- `JWT_SECRET`: For authentication
- `API_KEY_SECRET`: For API key validation

#### Multi-Tenant Configuration
For multiple applications, use app-specific environment variables:
```bash
# SportyExpats
SPORTY_EXPATS_NOVU_API_KEY=sk_123...
SPORTY_EXPATS_RESEND_API_KEY=re_456...

# TaxDone
TAX_DONE_NOVU_API_KEY=sk_abc...

# VentureDirection
VENTURE_DIRECTION_NOVU_API_KEY=sk_xyz...
VENTURE_DIRECTION_RESEND_API_KEY=re_uvw...
```

### 4. Database Migration

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database (for development)
npm run db:push

# Or run migrations (for production)
npm run db:migrate
```

### 5. Seed Database (Optional)

```bash
npm run db:seed
```

This creates:
- Default application with API key `default-app-key`
- Test user `<EMAIL>`
- Sample notification templates
- Sample notifications

### 6. Start Development Server

```bash
npm run dev
```

## Usage Examples

### Register an Application

```typescript
import { notificationService } from '@sparkstrand/notifications-core';

await notificationService.registerApp({
    appId: 'sporty-expats',
    name: 'SportyExpats',
    providers: {
        novu: {
            apiKey: process.env.SPORTY_EXPATS_NOVU_API_KEY,
            appId: process.env.SPORTY_EXPATS_NOVU_APP_ID,
            priority: 1,
            supportedChannels: ['email', 'sms', 'push', 'inApp']
        },
        resend: {
            apiKey: process.env.SPORTY_EXPATS_RESEND_API_KEY,
            fromEmail: '<EMAIL>',
            priority: 2,
            supportedChannels: ['email']
        }
    },
    fallbackStrategy: 'hybrid'
});
```

### Send Notifications

```typescript
// Single notification
await notificationService.sendNotification(
    'user-123',
    NotificationType.EVENT_REMINDER,
    'Football Match Tomorrow!',
    'Don\'t forget your match at 3 PM',
    { email: '<EMAIL>' }
);

// Bulk notifications
await notificationService.sendBulkNotifications({
    appId: 'sporty-expats',
    notifications: [
        { userId: 'user1', type: NotificationType.EVENT_REMINDER, title: '...', content: '...' },
        { userId: 'user2', type: NotificationType.EVENT_REMINDER, title: '...', content: '...' }
    ]
});
```

## API Endpoints

- `GET /api/health` - Health check with database connectivity and system metrics
- `POST /api/notifications` - Create notification
- `GET /api/notifications` - List notifications with filters
- `PUT /api/notifications` - Update notification
- `DELETE /api/notifications` - Delete notification

## Database Operations

### Generate Prisma Client

```bash
npm run db:generate
```

### View Database in Prisma Studio

```bash
npm run db:studio
```

### Run Migrations

```bash
npm run db:migrate
```

### Reset Database (Development)

```bash
npm run db:push --force-reset
npm run db:seed
```

## Development Workflow

1. **Schema Changes**: Update `prisma/schema.prisma`
2. **Generate Client**: `npm run db:generate`
3. **Test Locally**: `npm run db:push`
4. **Create Migration**: `npm run db:migrate`
5. **Deploy**: Push migration to production

## Production Considerations

- **Connection Pooling**: Configure `DATABASE_POOL_MIN` and `DATABASE_POOL_MAX`
- **Migrations**: Always use migrations in production, never `db:push`
- **Environment Variables**: Secure all sensitive configuration
- **Monitoring**: Use health check endpoint for monitoring
- **Backup**: Regular database backups
- **Scaling**: Consider read replicas for high read loads
- **Rate Limiting**: Configure appropriate rate limits per application
- **Provider Health**: Monitor provider status and implement alerts

## Troubleshooting

### Common Issues

1. **Connection Errors**: Check `DATABASE_URL` and network connectivity
2. **Migration Failures**: Ensure database user has proper permissions
3. **Prisma Client Errors**: Run `npm run db:generate` after schema changes
4. **Environment Variables**: Verify all required variables are set
5. **Provider Failures**: Check provider API keys and quotas
6. **Rate Limiting**: Verify rate limit configuration for your application

### Debug Mode

Enable Prisma query logging by setting `DEBUG=prisma:*` in your environment.

## Next Steps

1. **Authentication**: Implement JWT-based authentication
2. **Rate Limiting**: Add API rate limiting per application
3. **Caching**: Implement Redis caching for frequently accessed data
4. **Monitoring**: Add comprehensive metrics and logging
5. **Testing**: Add comprehensive test suite
6. **Provider Expansion**: Add support for additional notification providers
7. **Admin Dashboard**: Create management interface for applications and providers
