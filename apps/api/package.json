{"name": "@sparkstrand/notifications-api", "version": "0.0.0", "private": true, "description": "Next.js API server for the Spark Strand notifications system", "scripts": {"dev": "next dev", "build": "next build", "build:with-prisma": "prisma generate && next build", "build:vercel": "prisma generate && next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next dist", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "postinstall": "prisma generate", "db:start": "docker-compose up -d postgres adminer", "db:stop": "docker-compose stop postgres adminer", "db:logs": "echo '\n \t \t \t \t \t **************PRESS Ctrl+C TO EXIT************\n' && docker-compose logs -f postgres", "db:restart": "docker-compose restart postgres adminer", "db:status": "docker-compose ps", "db:connect": "docker-compose exec postgres psql -U ventureDirectionUser -d ventureDirection", "db:reset": "docker-compose down -v && docker-compose up -d postgres adminer"}, "dependencies": {"@novu/node": "^0.19.0", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "6.14.0", "@sparkstrand/notifications-core": "*", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "helmet": "^8.0.0", "next": "^15.0.0", "pg": "^8.16.3", "rate-limiter-flexible": "^4.0.1", "react": "^19.0.0", "react-dom": "^19.0.0", "zod": "^3.23.8"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/node": "^22.10.7", "@types/pg": "^8.15.5", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "^15.0.0", "postcss": "^8.5.6", "prisma": "6.14.0", "tailwindcss": "^4.1.11", "tsx": "^4.7.0", "typescript": "^5.7.3"}}