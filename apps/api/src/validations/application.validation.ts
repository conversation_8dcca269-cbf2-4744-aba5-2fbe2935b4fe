import { z } from 'zod';
import { AppRegistrationStatus, FallbackStrategy, DefaultProvider } from "@prisma/client";


const registrationStatusSchema = z.nativeEnum(AppRegistrationStatus);
const fallbackStrategySchema = z.nativeEnum(FallbackStrategy);
const defaultProviderSchema = z.nativeEnum(DefaultProvider);

/**
 * Zod schema for creating a new AppRegistration
 */
export const CreateAppRegistrationSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().nullable().optional(),
  contactEmail: z.string().email('Invalid email format'),
  contactName: z.string().min(1, 'Contact name is required'),
  website: z.string().url('Invalid URL format').nullable().optional(),
  businessType: z.string().nullable().optional(),
  expectedVolume: z.number().int().positive().nullable().optional(),
  fallbackStrategy: fallbackStrategySchema.nullable().optional(),
  maxRetries: z.number().int().nonnegative().nullable().optional(),
  retryDelay: z.number().int().nonnegative().nullable().optional(),
  rateLimits: z.any().nullable().optional(),
  novuAppId: z.string().nullable().optional(),
  novuApiKey: z.string().nullable().optional(),
  resendApiKey: z.string().nullable().optional(),
  twilioAccountSid: z.string().nullable().optional(),
  twilioAuthToken: z.string().nullable().optional(),
});

/**
 * Zod schema for creating a new Application
 */
export const CreateApplicationSchema = z.object({
  appId: z.string().cuid(),
  name: z.string().min(1, 'Name is required'),
  description: z.string().nullable().optional(),
  webhookUrl: z.string().url('Invalid URL format').nullable().optional(),
  novuAppId: z.string().nullable().optional(),
  novuApiKey: z.string().nullable().optional(),
  resendApiKey: z.string().nullable().optional(),
  twilioAccountSid: z.string().nullable().optional(),
  twilioAuthToken: z.string().nullable().optional(),
  defaultProvider: defaultProviderSchema.default(DefaultProvider.NOVU),
  fallbackStrategy: fallbackStrategySchema.nullable().optional(),
  maxRetries: z.number().int().nonnegative().nullable().optional(),
  retryDelay: z.number().int().nonnegative().nullable().optional(),
  rateLimits: z.any().nullable().optional(),
  metadata: z.any().nullable().optional(),
});

/**
 * Zod schema for creating a new AppApiKey
 */
export const CreateAppApiKeySchema = z.object({
  appId: z.string().cuid('Invalid App ID format'),
  name: z.string().min(1, 'Name is required'),
  expiresAt: z.date().nullable().optional(),
  permissions: z.array(z.string()).min(1, 'At least one permission is required').nullable().optional(),
});