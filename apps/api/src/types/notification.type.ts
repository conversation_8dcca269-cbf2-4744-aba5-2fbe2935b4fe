export interface Notification {
  id: string;
  title: string;
  message: string;
  type: string;
  priority: string;
  recipientId: string;
  senderId: string | null;
  appId: string;
  status: string;
  scheduledFor: Date | null;
  expiresAt: Date | null;
  metadata: any;
  novuNotificationId: string | null;
  createdAt: Date;
  updatedAt: Date;
  readAt: Date | null;
  
  // Relations (when included)
  recipient: {
    id: string;
    email: string;
    name: string | null;
    phone: string | null;
  } | null;
  app: {
    appId: string;
    name: string;
  } | null;
  deliveries: DeliveryStatus[] | null;
  webhookDeliveries: WebhookDelivery[] | null;
}

export interface CreateNotification {
  title: string;
  message: string;
  type: string;
  priority?: string;
  recipientId: string;
  senderId?: string;
  appId: string;
  scheduledFor?: Date;
  expiresAt?: Date;
  metadata?: any;
  novuNotificationId?: string;
}

export interface UpdateNotification {
  title?: string;
  message?: string;
  type?: string;
  priority?: string;
  status?: string;
  scheduledFor?: Date;
  expiresAt?: Date;
  metadata?: any;
  readAt?: Date;
}

export interface NotificationFilter {
  page?: number;
  limit?: number;
  appId?: string;
  recipientId?: string;
  status?: string;
  type?: string;
  priority?: string;
  startDate?: string | Date;
  endDate?: string | Date;
  includeRead?: boolean;
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface NotificationStats {
  total: number;
  byStatus: {
    pending: number;
    sent: number;
    delivered: number;
    failed: number;
    read: number;
  };
  byType: Record<string, number>;
  byPriority: Record<string, number>;
}

export interface BulkNotificationResult {
  successful: Notification[];
  failed: Array<{
    notification: CreateNotification;
    error: string;
  }>;
  totalCount: number;
}

// ============ DELIVERY STATUS TYPES ============

export interface DeliveryStatus {
  id: string;
  notificationId: string;
  channel: string; // email, sms, push, in_app, webhook
  provider: string; // novu, resend, twilio, etc.
  status: string; // PENDING, SENT, DELIVERED, FAILED, BOUNCED
  attempts: number;
  deliveredAt?: Date;
  errorMessage?: string;
  errorCode?: string;
  providerMessageId?: string;
  providerResponse: string | any;
  createdAt: Date;
  updatedAt: Date;
}

// ============ USER PREFERENCES TYPES ============

export interface UserNotificationPreferences {
  id: string;
  userId: string;
  notificationType: string;
  enabled: boolean;
  priority: string;
  emailEnabled: boolean;
  smsEnabled: boolean;
  pushEnabled: boolean;
  inAppEnabled: boolean;
  webhookEnabled: boolean;
  quietHoursEnabled: boolean;
  quietHoursStart?: string;
  quietHoursEnd?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserNotificationPreferences {
  enabled?: boolean;
  priority?: string;
  emailEnabled?: boolean;
  smsEnabled?: boolean;
  pushEnabled?: boolean;
  inAppEnabled?: boolean;
  webhookEnabled?: boolean;
  quietHoursEnabled?: boolean;
  quietHoursStart?: string;
  quietHoursEnd?: string;
}

// ============ TEMPLATE TYPES ============

export interface NotificationTemplate {
  id: string;
  name: string;
  description?: string;
  title: string;
  message: string;
  type: string;
  priority: string;
  emailSubject?: string;
  emailBody?: string;
  smsMessage?: string;
  pushTitle?: string;
  pushBody?: string;
  channels: string[];
  metadata: string | any;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateNotificationTemplate {
  name: string;
  description?: string;
  title: string;
  message: string;
  type: string;
  priority?: string;
  emailSubject?: string;
  emailBody?: string;
  smsMessage?: string;
  pushTitle?: string;
  pushBody?: string;
  channels?: string[];
  metadata?: any;
}

export interface UpdateNotificationTemplate {
  description?: string;
  title?: string;
  message?: string;
  type?: string;
  priority?: string;
  emailSubject?: string;
  emailBody?: string;
  smsMessage?: string;
  pushTitle?: string;
  pushBody?: string;
  channels?: string[];
  metadata?: any;
}

// ============ WEBHOOK TYPES ============

export interface WebhookEndpoint {
  id: string;
  name: string;
  url: string;
  secret: string;
  events: string[];
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateWebhookEndpoint {
  name: string;
  url: string;
  secret?: string;
  events?: string[];
  enabled?: boolean;
}

export interface WebhookDelivery {
  id: string;
  webhookId: string;
  notificationId: string;
  status: string; // SUCCESS, FAILED, TIMEOUT
  responseCode?: number;
  responseBody?: string;
  errorMessage?: string;
  attempts: number;
  nextRetryAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations (when included)
  webhook?: {
    id: string;
    name: string;
    url: string;
  };
}

// ============ NOTIFICATION CHANNELS ============

export type NotificationChannel = 
  | 'email' 
  | 'sms' 
  | 'push' 
  | 'in_app' 
  | 'webhook'
  | 'slack'
  | 'discord'
  | 'teams';

export type NotificationStatus = 
  | 'PENDING' 
  | 'SENT' 
  | 'DELIVERED' 
  | 'READ' 
  | 'FAILED'
  | 'EXPIRED'
  | 'CANCELLED';

export type NotificationType = 
  | 'INFO' 
  | 'SUCCESS' 
  | 'WARNING' 
  | 'ERROR'
  | 'REMINDER'
  | 'ALERT'
  | 'MARKETING'
  | 'SYSTEM';

export type NotificationPriority = 
  | 'LOW' 
  | 'NORMAL' 
  | 'HIGH' 
  | 'URGENT'
  | 'CRITICAL';

export type DeliveryStatusType = 
  | 'PENDING' 
  | 'SENT' 
  | 'DELIVERED' 
  | 'FAILED'