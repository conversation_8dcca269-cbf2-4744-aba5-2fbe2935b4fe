import { AppRegistrationStatus, FallbackStrategy, Default<PERSON>rovider } from "@prisma/client";

export const ApplicationEnum = {
  AppRegistrationStatus: AppRegistrationStatus,
  FallbackStrategy: FallbackStrategy,
  DefaultProvider: DefaultProvider,
};

/**
 * Interface for AppRegistration
 * 
 */
export interface AppRegistration {
  /**
   * The unique primary key for the registration record, automatically generated by the database.
   */
  id: string;

  /**
   * The unique identifier for the application itself. This ID is assigned during registration and
   * is used to link to the active `Application` model.
   */
  appId: string;

  /**
   * The name of the application requesting to be registered.
   */
  name: string;

  /**
   * An optional, brief description of the application.
   */
  description: string | null;

  /**
   * The email address of the primary contact person for the application.
   */
  contactEmail: string;

  /**
   * The name of the main contact for the application.
   */
  contactName: string;

  /**
   * The optional URL for the application's official website.
   */
  website: string | null;

  /**
   * An optional field to categorize the type of business or application (e.g., "SaaS", "e-commerce").
   */
  businessType: string | null;

  /**
   * The estimated number of notifications the application expects to send per month.
   */
  expectedVolume: number | null;

  /**
   * The current state of the registration request.
   * Possible values: 'PENDING_APPROVAL', 'APPROVED', 'REJECTED'.
   */
  status: keyof typeof ApplicationEnum.AppRegistrationStatus ;

  /**
   * Optional internal notes from an administrator regarding the approval or rejection of the request.
   */
  adminNotes: string | null;

  /**
   * The timestamp indicating when the application was approved.
   */
  approvedAt: Date | null;

  /**
   * The identifier of the administrator who approved the registration.
   */
  approvedBy: string | null;

  /**
   * The application ID provided by the Novu notification service.
   */
  novuAppId: string | null;

  /**
   * The API key for the Novu service.
   */
  novuApiKey: string | null;

  /**
   * The API key for the Resend email service.
   */
  resendApiKey: string | null;

  /**
   * The Account SID for the Twilio SMS service.
   */
  twilioAccountSid: string | null;

  /**
   * The Auth Token for the Twilio SMS service.
   */
  twilioAuthToken: string | null;

  /**
   * The strategy to use if a primary provider fails.
   * Possible values: 'failover', 'parallel', 'hybrid'.
   */
  fallbackStrategy: keyof typeof ApplicationEnum.FallbackStrategy | null;

  /**
   * The maximum number of delivery attempts for a failed notification. Defaults to 3.
   */
  maxRetries: number | null;

  /**
   * The delay in milliseconds between retry attempts. Defaults to 1000.
   */
  retryDelay: number | null;

  /**
   * A JSON object to configure rate limits for the application, such as messages per minute.
   */
  rateLimits: any,

  /**
   * The timestamp when the registration record was created.
   */
  createdAt: Date;

  /**
   * The timestamp of the last update to the registration record.
   */
  updatedAt: Date;

  /**
   * A relation to the `AppApiKey` model, representing the API keys associated with this application.
   */
  apiKeys?: AppApiKey[];
}


/**
 * Interface for creating a new AppRegistration
 * 
 */
export interface CreateAppRegistration {
  /**
   * The name of the application. This is a required field.
   */
  name: string;

  /**
   * An optional, brief description of the application.
   */
  description: string | null;

  /**
   * The email address of the primary contact for the application. This is a required field.
   */
  contactEmail: string;

  /**
   * The name of the main contact for the application. This is a required field.
   */
  contactName: string;

  /**
   * The optional URL for the application's official website.
   */
  website: string | null;

  /**
   * The type of business or application (e.g., "SaaS", "e-commerce"). This field is optional.
   */
  businessType: string | null;

  /**
   * The estimated number of notifications the application expects to send per month.
   * This is an optional field.
   */
  expectedVolume: number | null;

  /**
   * The strategy to use if a primary provider fails. This field is optional and has a default value.
   * Possible values: 'failover', 'parallel', 'hybrid'.
   */
  fallbackStrategy: keyof typeof ApplicationEnum.FallbackStrategy | null;

    /**
   * The maximum number of delivery attempts for a failed notification. Defaults to 3.
   */
  maxRetries: number | null;

  /**
   * The delay in milliseconds between retry attempts. Defaults to 1000.
   */
  retryDelay: number | null;

  /**
   * A JSON object to configure rate limits for the application, such as messages per minute.
   */
  rateLimits: any

  /**
   * The application ID provided by the Novu notification service. This is an optional field.
   */
  novuAppId: string | null;

  /**
   * The API key for the Novu service. This is an optional field.
   */
  novuApiKey: string | null;

  /**
   * The API key for the Resend email service. This is an optional field.
   */
  resendApiKey: string | null;

  /**
   * The Account SID for the Twilio SMS service. This is an optional field.
   */
  twilioAccountSid: string | null;

  /**
   * The Auth Token for the Twilio SMS service. This is an optional field.
   */
  twilioAuthToken: string | null;
}


/**
 * Interface for Application
 * Represents an application instance in the system
 */
export interface Application {
  /**
   * The primary identifier for the application. This ID is established during registration.
   */
  appId: string;

  /**
   * The name of the application.
   */
  name: string;

  /**
   * An optional, brief description of the application.
   */
  description: string | null;

  /**
   * An optional URL for the application's webhook endpoint.
   */
  webhookUrl: string | null;

  /**
   * The application ID from the Novu notification service.
   */
  novuAppId: string | null;

  /**
   * The API key for the Novu service.
   */
  novuApiKey: string | null;

  /**
   * The API key for the Resend email service.
   */
  resendApiKey: string | null;

  /**
   * The Account SID for the Twilio SMS service.
   */
  twilioAccountSid: string | null;

  /**
   * The Auth Token for the Twilio SMS service.
   */
  twilioAuthToken: string | null;

  /**
   * The default provider to use for notifications.
   * Possible values: 'novu', 'direct', 'hybrid'.
   */
  defaultProvider: keyof typeof ApplicationEnum.DefaultProvider;

  /**
   * The strategy to use if a primary provider fails.
   * Possible values: 'failover', 'parallel', 'hybrid'.
   */
  fallbackStrategy: keyof typeof ApplicationEnum.FallbackStrategy | null;

  /**
   * The maximum number of delivery attempts for a failed notification.
   */
  maxRetries: number | null;

  /**
   * The delay in milliseconds between retry attempts.
   */
  retryDelay: number | null;

  /**
   * A JSON object to configure rate limits for the application.
   */
  rateLimits: any;

  /**
   * Indicates whether the application is active in the system.
   */
  isActive: boolean;

  /**
   * A JSON object for storing additional application metadata.
   */
  metadata: any;

  /**
   * The timestamp when the application was created.
   */
  createdAt: Date;

  /**
   * The timestamp of the last update to the application record.
   */
  updatedAt: Date;

  /**
   * A relation to the `Notification` model, representing all notifications sent by this application.
   */
  notifications?: any[];

  /**
   * A relation to the `ApplicationUserSubscription` model, representing user subscriptions for this application.
   */
  userSubscriptions?: any[];

  /**
   * A relation to the `AppApiKey` model, representing the API keys associated with this application.
   */
  apiKeys?: AppApiKey[];
}


/**
 * Interface for CreateApplication
 * Represents the minimum data needed to create a new active application record
 */
export interface CreateApplication {
  /**
   * The unique identifier for the application. This is a required field.
   */
  appId: string;

  /**
   * The name of the application.
   */
  name: string;

  /**
   * An optional, brief description of the application.
   */
  description: string | null;

  /**
   * An optional URL for the application's webhook endpoint.
   */
  webhookUrl: string | null;

  /**
   * The application ID from the Novu notification service. This is an optional field.
   */
  novuAppId: string | null;

  /**
   * The API key for the Novu service. This is an optional field.
   */
  novuApiKey: string | null;

  /**
   * The API key for the Resend email service. This is an optional field.
   */
  resendApiKey: string | null;

  /**
   * The Account SID for the Twilio SMS service. This is an optional field.
   */
  twilioAccountSid: string | null;

  /**
   * The Auth Token for the Twilio SMS service. This is an optional field.
   */
  twilioAuthToken: string | null;

  /**
   * The default provider to use for notifications. Defaults to 'novu'.
   * Possible values: 'novu', 'direct', 'hybrid'.
   */
  defaultProvider: keyof typeof ApplicationEnum.DefaultProvider;

  /**
   * The strategy to use if a primary provider fails.
   * Possible values: 'failover', 'parallel', 'hybrid'.
   */
  fallbackStrategy: keyof typeof ApplicationEnum.FallbackStrategy | null;

  /**
   * The maximum number of delivery attempts for a failed notification. Defaults to 3.
   */
  maxRetries: number | null;

  /**
   * The delay in milliseconds between retry attempts. Defaults to 1000.
   */
  retryDelay: number | null;

  /**
   * A JSON object for configuring rate limits.
   */
  rateLimits: any;

  /**
   * A JSON object for storing additional application metadata.
   */
  metadata: any;
}


/**
 * Interface for ApplicationNotificationProviders
 */
export interface ApplicationNotificationProviders {
  /**
   * The application ID from the Novu notification service. This is an optional field.
   */
  novuAppId: string | null;

  /**
   * The API key for the Novu service. This is an optional field.
   */
  novuApiKey: string | null;

  /**
   * The API key for the Resend email service. This is an optional field.
   */
  resendApiKey: string | null;

  /**
   * The Account SID for the Twilio SMS service. This is an optional field.
   */
  twilioAccountSid: string | null;

  /**
   * The Auth Token for the Twilio SMS service. This is an optional field.
   */
  twilioAuthToken: string | null;

  /**
   * The default provider to use for notifications. Defaults to 'novu'.
   * Possible values: 'novu', 'direct', 'hybrid'.
   */
  defaultProvider: keyof typeof ApplicationEnum.DefaultProvider | 'NOVU';

}

/**
 * Interface for ApplicationConfig
 * Represents the configuration settings for an application
 */
export interface ApplicationConfig {
 /**
   * The strategy to use if a primary provider fails.
   * Possible values: 'failover', 'parallel', 'hybrid'.
   */
  fallbackStrategy: keyof typeof ApplicationEnum.FallbackStrategy | null;

  /**
   * The maximum number of delivery attempts for a failed notification. Defaults to 3.
   */
  maxRetries: number | null;

  /**
   * The delay in milliseconds between retry attempts. Defaults to 1000.
   */
  retryDelay: number | null;

  /**
   * A JSON object for configuring rate limits.
   */
  rateLimits: any;

  /**
   * A JSON object for storing additional application metadata.
   */
  metadata: any;
}

/**
 * Interface for AppApiKey
 * Represents an API key associated with an application
 */
export interface AppApiKey {
  /**
   * The unique primary key for the API key record, automatically generated by the database.
   */
  id: string;

  /**
   * The unique identifier of the application this API key belongs to.
   */
  appId: string;

  /**
   * The hashed version of the API key, used for secure authentication.
   */
  apiKeyHash: string;

  /**
   * An optional, human-readable name for the key (e.g., "Production Key", "Development Key").
   */
  name: string | null;

  /**
   * A boolean indicating if the API key is currently active. Defaults to true during key creation.
   */
  isActive: boolean;

  /**
   * The timestamp when the API key will expire. Optional.
   */
  expiresAt: Date | null;

  /**
   * The timestamp of the last time the API key was used. Optional.
   */
  lastUsedAt: Date | null;

  /**
   * A list of permissions granted to this key (e.g., 'send_notifications', 'read_notifications', 'manage_preferences').
   */
  permissions: string[];

  /**
   * The timestamp when the API key record was created.
   */
  createdAt: Date;

  /**
   * The timestamp of the last update to the API key record.
   */
  updatedAt: Date;

  /**
   * The related Application model, representing the parent application.
   */
  application?: Application;

  /**
   * The related AppRegistration model, representing the registration request this key is linked to.
   */
  appRegistration?: AppRegistration | null;
}


/**
 * Interface for creating a new AppApiKey
 */
export interface CreateAppApiKey {
  /**
   * The unique identifier of the application this API key belongs to.
   */
  appId: string;

  // /**
  //  * The raw, unhashed API key string that your backend will process and hash.
  //  */
  // apiKey: string;

  /**
   * Required human-readable name for the key - (e.g., "Production", "Development", "Staging",)
   */
  name: string;

  // /**
  //  * A boolean to set the initial activity status of the key. Defaults to true.
  //  */
  // isActive?: boolean;

  /**
   * The timestamp when the API key will expire. Optional.
   */
  expiresAt?: Date;

  /**
   * A list of permissions granted to this key.
   */
  permissions: string[] | null;
}


export interface AppApiKeyStatus {
  isActive:   boolean,
  expiresAt:  Date | null,
  lastUsedAt: Date | null
}