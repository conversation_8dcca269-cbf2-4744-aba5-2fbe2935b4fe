
/**
 * Standardized response interface for consistent responses across all services and api
 */
export interface Result<TData = any> {
  /** Indicates if the operation was successful */
  success: boolean;
  /** Human-readable message describing the result */
  message: string;
  /** The response data payload */
  data: TData;
  /** HTTP status code */
  statusCode: number;
  /** Optional redirect URL for navigation flows */
  redirectTo?: string;
  /** Optional validation errors for detailed error reporting */
  errors?: Record<string, string[]>;
  /** Optional metadata for additional context */
  metadata?: Record<string, any>;
}

/**
 * HTTP Status Codes enum for better code readability and consistency
 */
export enum HttpStatusCode {
  // Success
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  
  // Client Errors
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  
  // Server Errors
  INTERNAL_SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503
}