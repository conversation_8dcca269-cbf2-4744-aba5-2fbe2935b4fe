import { prisma } from '@/lib/db';
import { 
    Result, AppRegistration, 
    CreateAppRegistration, CreateApplication, 
    ApplicationEnum, Application,
    ApplicationConfig, AppApiKey, CreateAppApiKey,
    AppApiKeyStatus,
    ApplicationNotificationProviders
 } from '@/types';
import { BaseService } from './base.service';
import { createId } from '@paralleldrive/cuid2';


/**
 * Service for managing applications
 */
export class ApplicationService extends BaseService {
    constructor() {
        super('Application');
    }

    /**
     * Create a new app registration
     */
    async createAppRegistration(data: CreateAppRegistration): Promise<Result<AppRegistration>> {
        try {
            const rateLimits = data.rateLimits || {};
            const rateJSON = JSON.stringify(rateLimits);
            const result = await prisma.appRegistration.create({
                data: {...data, appId: createId(), rateLimits: rateJSON }
            });

            return this.created(result);
        } catch (error) {
            console.log('Error creating app registration:', error);
            return this.serverError();
        }
    }

    async getAppRegistration(appId: string): Promise<Result<AppRegistration>> {
        try {
            const app = await prisma.appRegistration.findUnique({
                where: { appId }
            });

            if (!app) {
                return this.notFound();
            }

            return this.success(app);
        } catch (error) {
            console.log('Error fetching app registration:', error);
            return this.serverError();
        }
    }

    /**
     * Change the approval status of an app registration and create an application record if approved.
    */
    async changeApprovalStatus(appId: string, status: keyof typeof ApplicationEnum.AppRegistrationStatus, adminNotes?: string): Promise<Result<null>> {
        try {
            if(!this.vallidateAppEnum(status, ApplicationEnum.AppRegistrationStatus)) {
                return this.badRequest('Invalid status');
            }
            await prisma.$transaction(async (tx) => {
                // 1. Find the app registration record within the transaction.
                const appRegistration = await tx.appRegistration.findUnique({
                    where: { appId },
                });

                if (!appRegistration) {
                    throw new Error("App registration not found.");
                }

                const isApprove = status === 'APPROVED'; 

                // 2. Update the approval status within the transaction.
                const updatedAppRegistration = await tx.appRegistration.update({
                    where: { appId },
                    data: {
                        status,
                        adminNotes,
                        approvedAt: isApprove ? new Date() : null,
                        approvedBy: isApprove ? 'admin' : null
                    }
                });

                // 3. If approved, create a new application record within the same transaction.
                if (isApprove) {
                    const newApp: CreateApplication = {
                        appId,
                        name: updatedAppRegistration.name,
                        description: updatedAppRegistration.description,
                        webhookUrl: null,
                        novuAppId: updatedAppRegistration.novuAppId,
                        novuApiKey: updatedAppRegistration.novuApiKey,
                        resendApiKey: updatedAppRegistration.resendApiKey,
                        twilioAccountSid: updatedAppRegistration.twilioAccountSid,
                        twilioAuthToken: updatedAppRegistration.twilioAuthToken,
                        defaultProvider: 'NOVU',
                        fallbackStrategy: updatedAppRegistration.fallbackStrategy,
                        maxRetries: updatedAppRegistration.maxRetries,
                        retryDelay: updatedAppRegistration.retryDelay,
                        rateLimits: updatedAppRegistration.rateLimits,
                        metadata: {
                            defaultChannels: ['inApp', 'email'],
                            defaultPriority: 'NORMAL',
                            enableQuietHours: false,
                        }
                    };
                    await tx.application.upsert({ where: { appId }, update: {}, create: { ...newApp, isActive: true } });
                }
            });

            return this.success(null, 'Application successfully approved');

        } catch (error) {
            console.error('Error updating approval status in transaction:', error);
            return this.serverError();
        }
    }

    async createApplication(data: CreateApplication): Promise<Result<Application>> {
        try {
            const rateLimits = data.rateLimits || {};
            const rateJSON = JSON.stringify(rateLimits);
            const appId = createId();
            data = {...data, appId, rateLimits: rateJSON, isActive: true } as any;
            const result = await prisma.application.create({data});

            return this.created(this.safeApplication(result)!);
        } catch (error) {
            console.log('Error creating application:', error);
            return this.serverError();
        }
    }

    /**
     * Change the default provider for an application
     * @param appId 
     * @param defaultProvider 
     * @returns 
     */
    async updateApplicationDefaultProvider(appId: string, defaultProvider: (keyof typeof ApplicationEnum.DefaultProvider)): Promise<Result<Application>> {
        try {
            if(!this.vallidateAppEnum(defaultProvider, ApplicationEnum.DefaultProvider)) {
                return this.badRequest('Invalid default provider');
            }
            const result = await prisma.application.update({
                where: { appId },
                data: { defaultProvider }
            });

            return this.success(this.safeApplication(result)!);
        } catch (error) {
            console.log('Error changing default provider:', error);
            return this.serverError();
        }
    }

    /**
     * change the webhook URL for an application
     * @param appId 
     * @param webhookUrl 
     * @returns 
     */
    async updateApplicationWebhook(appId: string, webhookUrl: string): Promise<Result<Application>> {
        try {
            const result = await prisma.application.update({
                where: { appId },
                data: { webhookUrl }
            });

            return this.success(this.safeApplication(result)!);
        } catch (error) {
            console.log('Error updating webhook URL:', error);
            return this.serverError();
        }
    }

    async updateApplicationNotificationProviders(appId: string, providers: ApplicationNotificationProviders): Promise<Result<Application>> {
        try {
            const result = await prisma.application.update({
                where: { appId },
                data: { ...providers }
            });

            return this.success(this.safeApplication(result)!);
        } catch (error) {
            console.log('Error updating notification providers:', error);
            return this.serverError();
        }
    }

    async updateApplicationConfig(appId: string, configData: ApplicationConfig): Promise<Result<Application>> {
        try {
            if(Object.keys(configData).length === 0) {
                return this.badRequest('No data provided');
            }
            const updateData: Partial<ApplicationConfig> = {};
            const app = await this.getApplication(appId);
            if(!app.success) return app;
            
            let { metadata, rateLimits } = app.data;
            rateLimits = JSON.parse(rateLimits);
            metadata = JSON.parse(metadata);
            if (configData.fallbackStrategy) updateData.fallbackStrategy = configData.fallbackStrategy;
            if (configData.maxRetries) updateData.maxRetries = configData.maxRetries;
            if (configData.retryDelay) updateData.retryDelay = configData.retryDelay;
            if (configData.rateLimits) updateData.rateLimits = JSON.stringify({...rateLimits, ...configData.rateLimits});
            if (configData.metadata) updateData.metadata = JSON.stringify({...metadata, ...configData.metadata});

            const result = await prisma.application.update({
                where: { appId },
                data: { ...updateData}
            });

            return this.success(this.safeApplication(result)!);
        } catch (error) {
            console.log('Error updating application config:', error);
            return this.serverError();
        }
    }

    async getAppConfig(appId: string): Promise<Result<ApplicationConfig>> {
        try {
            const app = await prisma.application.findUnique({
                where: { appId },
                select: {
                    fallbackStrategy: true,
                    maxRetries: true,
                    retryDelay: true,
                    rateLimits: true,
                    metadata: true
                }
            });

            if (!app) {
                return this.notFound();
            }

            return this.success(app);
        } catch (error) {
            console.log('Error fetching application config:', error);
            return this.serverError();
        }
    }

    async getApplication(appId: string): Promise<Result<Application>> {
        try {
            const app = await prisma.application.findUnique({
                where: { appId }
            });

            if (!app) {
                return this.notFound();
            }

            return this.success(app);
        } catch (error) {
            console.log('Error fetching application:', error);
            return this.serverError();
        }
    }

    safeApplication(app: Application): Application | null {
        if(!app) return null;
        return {
            ...app,
            novuApiKey: app.novuApiKey ? '***configured***' : null,
            resendApiKey: app.resendApiKey ? '***configured***' : null,
            twilioAuthToken: app.twilioAuthToken ? '***configured***' : null,
            novuAppId: app.novuAppId ? '***configured***' : null,
            twilioAccountSid: app.twilioAccountSid ? '***configured***' : null,

        };
    }

    vallidateAppEnum(value: any, enumType: any): boolean {
        return Object.keys(enumType).indexOf(value) !== -1;
    }

    async deleteApplication(appId: string): Promise<Result<null>> {
        try {
            await prisma.application.delete({
                where: { appId }
            });

            return this.success(null);
        } catch (error) {
            console.log('Error deleting application:', error);
            return this.serverError();
        }
    }

    async createApplicationApiKey(apiKeyData: CreateAppApiKey): Promise<Result<AppApiKey>> {
        try {
            const app = await this.getApplication(apiKeyData.appId);
            if(!app.success) return app as any;
            const key = createId();
            const hashedKey = await this.hashApiKey(key);
            const permissions: string[] = apiKeyData.permissions ||  ['send_notifications', 'read_notifications', 'manage_preferences', 'manage_users'];
            
            const apiKey = await prisma.appApiKey.create({
                data: {
                    ...apiKeyData,
                    permissions,
                    apiKeyHash: hashedKey
                }
            });

            return this.created(apiKey);
        } catch (error) {
            console.log('Error creating API key:', error);
            return this.serverError();
        }
    }

    async updateApplicationApiKeyName(apiKeyId: string, name: string): Promise<Result<AppApiKey>> {
        try {
            const apiKey = await prisma.appApiKey.update({
                where: { id: apiKeyId },
                data: { name }
            });

            return this.success(apiKey);
        } catch (error) {
            console.log('Error updating API key name:', error);
            return this.serverError();
        }
    }

    async updateApplicationApiKeyStatus(apiKeyId: string, status: Partial<AppApiKeyStatus>): Promise<Result<AppApiKey>> {
        try {
            const apiKey = await prisma.appApiKey.update({
                where: { id: apiKeyId },
                data: { ...status }
            });

            return this.success(apiKey);
        } catch (error) {
            console.log('Error updating API key status:', error);
            return this.serverError();
        }
    }
}

export const applicationService = new ApplicationService();
