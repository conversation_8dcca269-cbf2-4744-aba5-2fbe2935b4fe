import { prisma } from '@/lib/db';
import { 
    Result, Notification, 
    CreateNotification, UpdateNotification,
    DeliveryStatus, NotificationTemplate,
    CreateNotificationTemplate, UpdateNotificationTemplate,
    NotificationFilter, PaginatedResult,
    NotificationStats, BulkNotificationResult,
    UserNotificationPreferences, CreateUserNotificationPreferences,
    WebhookEndpoint, CreateWebhookEndpoint, WebhookDelivery
} from '@/types';
import { BaseService } from './base.service';
import { createId } from '@paralleldrive/cuid2';

/**
 * Service for managing notifications, templates, and delivery tracking
 */
export class NotificationService extends BaseService {
    constructor() {
        super('Notification');
    }

    /**
     * Create a new notification
     */
    async createNotification(data: CreateNotification): Promise<Result<Notification>> {
        try {
            // Validate application exists
            const app = await prisma.application.findUnique({
                where: { appId: data.appId, isActive: true }
            });

            if (!app) {
                return this.badRequest('Application not found or inactive');
            }

            // Validate recipient exists
            const recipient = await prisma.user.findUnique({
                where: { id: data.recipientId }
            });

            if (!recipient) {
                return this.badRequest('Recipient not found');
            }

            const metadata = data.metadata ? JSON.stringify(data.metadata) : '{}';
            
            const notification = await prisma.notification.create({
                data: {
                    ...data,
                    metadata,
                    status: 'PENDING'
                },
                include: {
                    recipient: {
                        select: { id: true, email: true, name: true, phone: true }
                    },
                    app: {
                        select: { appId: true, name: true }
                    }
                }
            });

            return this.created(notification);
        } catch (error) {
            console.error('Error creating notification:', error);
            return this.serverError();
        }
    }

    /**
     * Create multiple notifications in bulk
     */
    async createBulkNotifications(notifications: CreateNotification[]): Promise<Result<BulkNotificationResult>> {
        try {
            const results: BulkNotificationResult = {
                successful: [],
                failed: [],
                totalCount: notifications.length
            };

            await prisma.$transaction(async (tx) => {
                for (const notificationData of notifications) {
                    try {
                        const metadata = notificationData.metadata ? JSON.stringify(notificationData.metadata) : '{}';
                        
                        const notification = await tx.notification.create({
                            data: {
                                ...notificationData,
                                id: createId(),
                                metadata,
                                status: 'PENDING'
                            },
                            include: {
                                recipient: {
                                    select: { id: true, email: true, name: true, phone: true }
                                },
                                app: {
                                    select: { appId: true, name: true }
                                }
                            }
                        });

                        results.successful.push(notification);
                    } catch (error) {
                        results.failed.push({
                            notification: notificationData,
                            error: error instanceof Error ? error.message : 'Unknown error'
                        });
                    }
                }
            });

            return this.success(results);
        } catch (error) {
            console.error('Error creating bulk notifications:', error);
            return this.serverError();
        }
    }

    /**
     * Get notification by ID
     */
    async getNotification(notificationId: string): Promise<Result<Notification>> {
        try {
            const notification = await prisma.notification.findUnique({
                where: { id: notificationId },
                include: {
                    recipient: {
                        select: { id: true, email: true, name: true, phone: true }
                    },
                    app: {
                        select: { appId: true, name: true }
                    },
                    deliveries: true,
                    webhookDeliveries: {
                        include: {
                            webhook: {
                                select: { id: true, name: true, url: true }
                            }
                        }
                    }
                }
            });

            if (!notification) {
                return this.notFound();
            }

            return this.success(notification);
        } catch (error) {
            console.error('Error fetching notification:', error);
            return this.serverError();
        }
    }

    /**
     * Update notification
     */
    async updateNotification(notificationId: string, data: UpdateNotification): Promise<Result<Notification>> {
        try {
            const updateData: any = { ...data };
            if (data.metadata) {
                updateData.metadata = JSON.stringify(data.metadata);
            }

            const notification = await prisma.notification.update({
                where: { id: notificationId },
                data: updateData,
                include: {
                    recipient: {
                        select: { id: true, email: true, name: true, phone: true }
                    },
                    app: {
                        select: { appId: true, name: true }
                    },
                    deliveries: true
                }
            });

            return this.success(notification);
        } catch (error) {
            console.error('Error updating notification:', error);
            return this.serverError();
        }
    }

    /**
     * Mark notification as read
     */
    async markAsRead(notificationId: string, userId?: string): Promise<Result<Notification>> {
        try {
            const whereClause: any = { id: notificationId };
            if (userId) {
                whereClause.recipientId = userId;
            }

            const notification = await prisma.notification.update({
                where: whereClause,
                data: { 
                    status: 'READ',
                    readAt: new Date()
                },
                include: {
                    recipient: {
                        select: { id: true, email: true, name: true, phone: true }
                    },
                    app: {
                        select: { appId: true, name: true }
                    }
                }
            });

            return this.success(notification);
        } catch (error) {
            console.error('Error marking notification as read:', error);
            return this.serverError();
        }
    }

    /**
     * Get notifications with filtering and pagination
     */
    async getNotifications(filter: NotificationFilter = {}): Promise<Result<PaginatedResult<Notification>>> {
        try {
            const {
                page = 1,
                limit = 20,
                appId,
                recipientId,
                status,
                type,
                priority,
                startDate,
                endDate,
                includeRead = true
            } = filter;

            const skip = (page - 1) * limit;
            const where: any = {};

            if (appId) where.appId = appId;
            if (recipientId) where.recipientId = recipientId;
            if (status) where.status = status;
            if (type) where.type = type;
            if (priority) where.priority = priority;
            if (!includeRead) where.readAt = null;

            if (startDate || endDate) {
                where.createdAt = {};
                if (startDate) where.createdAt.gte = new Date(startDate);
                if (endDate) where.createdAt.lte = new Date(endDate);
            }

            const [notifications, total] = await Promise.all([
                prisma.notification.findMany({
                    where,
                    skip,
                    take: limit,
                    orderBy: { createdAt: 'desc' },
                    include: {
                        recipient: {
                            select: { id: true, email: true, name: true }
                        },
                        app: {
                            select: { appId: true, name: true }
                        },
                        deliveries: true
                    }
                }),
                prisma.notification.count({ where })
            ]);

            const result: PaginatedResult<Notification> = {
                data: notifications,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            };

            return this.success(result);
        } catch (error) {
            console.error('Error fetching notifications:', error);
            return this.serverError();
        }
    }

    /**
     * Delete notification
     */
    async deleteNotification(notificationId: string): Promise<Result<null>> {
        try {
            await prisma.notification.delete({
                where: { id: notificationId }
            });

            return this.success(null);
        } catch (error) {
            console.error('Error deleting notification:', error);
            return this.serverError();
        }
    }

    /**
     * Get notification statistics
     */
    async getNotificationStats(appId?: string, startDate?: Date, endDate?: Date): Promise<Result<NotificationStats>> {
        try {
            const where: any = {};
            if (appId) where.appId = appId;
            if (startDate || endDate) {
                where.createdAt = {};
                if (startDate) where.createdAt.gte = startDate;
                if (endDate) where.createdAt.lte = endDate;
            }

            const [
                total,
                pending,
                sent,
                delivered,
                failed,
                read,
                byType,
                byPriority
            ] = await Promise.all([
                prisma.notification.count({ where }),
                prisma.notification.count({ where: { ...where, status: 'PENDING' } }),
                prisma.notification.count({ where: { ...where, status: 'SENT' } }),
                prisma.notification.count({ where: { ...where, status: 'DELIVERED' } }),
                prisma.notification.count({ where: { ...where, status: 'FAILED' } }),
                prisma.notification.count({ where: { ...where, status: 'READ' } }),
                prisma.notification.groupBy({
                    by: ['type'],
                    where,
                    _count: { type: true }
                }),
                prisma.notification.groupBy({
                    by: ['priority'],
                    where,
                    _count: { priority: true }
                })
            ]);

            const stats: NotificationStats = {
                total,
                byStatus: { pending, sent, delivered, failed, read },
                byType: byType.reduce((acc, item) => {
                    acc[item.type] = item._count.type;
                    return acc;
                }, {} as Record<string, number>),
                byPriority: byPriority.reduce((acc, item) => {
                    acc[item.priority] = item._count.priority;
                    return acc;
                }, {} as Record<string, number>)
            };

            return this.success(stats);
        } catch (error) {
            console.error('Error fetching notification stats:', error);
            return this.serverError();
        }
    }

    // ============ DELIVERY STATUS MANAGEMENT ============

    /**
     * Create or update delivery status
     */
    async updateDeliveryStatus(
        notificationId: string,
        channel: string,
        provider: string,
        status: string,
        details?: {
            deliveredAt?: Date;
            errorMessage?: string;
            errorCode?: string;
            providerMessageId?: string;
            providerResponse?: any;
        }
    ): Promise<Result<DeliveryStatus>> {
        try {
            const deliveryStatus = await prisma.deliveryStatus.upsert({
                where: {
                    notificationId_channel_provider: {
                        notificationId,
                        channel,
                        provider
                    }
                },
                update: {
                    status,
                    attempts: { increment: 1 },
                    ...details,
                    providerResponse: details?.providerResponse ? JSON.stringify(details.providerResponse) : undefined
                },
                create: {
                    id: createId(),
                    notificationId,
                    channel,
                    provider,
                    status,
                    attempts: 1,
                    ...details,
                    providerResponse: details?.providerResponse ? JSON.stringify(details.providerResponse) : '{}'
                }
            });

            // Update main notification status if needed
            if (status === 'DELIVERED') {
                await prisma.notification.update({
                    where: { id: notificationId },
                    data: { status: 'DELIVERED' }
                });
            } else if (status === 'FAILED') {
                // Check if all delivery attempts have failed
                const allDeliveries = await prisma.deliveryStatus.findMany({
                    where: { notificationId }
                });

                const allFailed = allDeliveries.every(d => d.status === 'FAILED');
                if (allFailed) {
                    await prisma.notification.update({
                        where: { id: notificationId },
                        data: { status: 'FAILED' }
                    });
                }
            }

            return this.success(deliveryStatus);
        } catch (error) {
            console.error('Error updating delivery status:', error);
            return this.serverError();
        }
    }

    /**
     * Get delivery statuses for a notification
     */
    async getDeliveryStatuses(notificationId: string): Promise<Result<DeliveryStatus[]>> {
        try {
            const deliveries = await prisma.deliveryStatus.findMany({
                where: { notificationId },
                orderBy: { createdAt: 'desc' }
            });

            return this.success(deliveries);
        } catch (error) {
            console.error('Error fetching delivery statuses:', error);
            return this.serverError();
        }
    }

    // ============ USER NOTIFICATION PREFERENCES ============

    /**
     * Create or update user notification preferences
     */
    async setUserNotificationPreferences(
        userId: string,
        notificationType: string,
        preferences: CreateUserNotificationPreferences
    ): Promise<Result<UserNotificationPreferences>> {
        try {
            const userPrefs = await prisma.userNotificationPreferences.upsert({
                where: {
                    userId_notificationType: {
                        userId,
                        notificationType
                    }
                },
                update: preferences,
                create: {
                    id: createId(),
                    userId,
                    notificationType,
                    ...preferences
                }
            });

            return this.success(userPrefs);
        } catch (error) {
            console.error('Error setting user notification preferences:', error);
            return this.serverError();
        }
    }

    /**
     * Get user notification preferences
     */
    async getUserNotificationPreferences(userId: string): Promise<Result<UserNotificationPreferences[]>> {
        try {
            const preferences = await prisma.userNotificationPreferences.findMany({
                where: { userId }
            });

            return this.success(preferences);
        } catch (error) {
            console.error('Error fetching user notification preferences:', error);
            return this.serverError();
        }
    }

    // ============ NOTIFICATION TEMPLATES ============

    /**
     * Create notification template
     */
    async createNotificationTemplate(data: CreateNotificationTemplate): Promise<Result<NotificationTemplate>> {
        try {
            const metadata = data.metadata ? JSON.stringify(data.metadata) : '{}';
            
            const template = await prisma.notificationTemplate.create({
                data: {
                    ...data,
                    id: createId(),
                    metadata
                }
            });

            return this.created(template);
        } catch (error) {
            console.error('Error creating notification template:', error);
            return this.serverError();
        }
    }

    /**
     * Get notification template by name
     */
    async getNotificationTemplate(name: string): Promise<Result<NotificationTemplate>> {
        try {
            const template = await prisma.notificationTemplate.findUnique({
                where: { name }
            });

            if (!template) {
                return this.notFound();
            }

            return this.success(template);
        } catch (error) {
            console.error('Error fetching notification template:', error);
            return this.serverError();
        }
    }

    /**
     * Update notification template
     */
    async updateNotificationTemplate(name: string, data: UpdateNotificationTemplate): Promise<Result<NotificationTemplate>> {
        try {
            const updateData: any = { ...data };
            if (data.metadata) {
                updateData.metadata = JSON.stringify(data.metadata);
            }

            const template = await prisma.notificationTemplate.update({
                where: { name },
                data: updateData
            });

            return this.success(template);
        } catch (error) {
            console.error('Error updating notification template:', error);
            return this.serverError();
        }
    }

    /**
     * Get all notification templates
     */
    async getNotificationTemplates(): Promise<Result<NotificationTemplate[]>> {
        try {
            const templates = await prisma.notificationTemplate.findMany({
                orderBy: { createdAt: 'desc' }
            });

            return this.success(templates);
        } catch (error) {
            console.error('Error fetching notification templates:', error);
            return this.serverError();
        }
    }

    /**
     * Delete notification template
     */
    async deleteNotificationTemplate(name: string): Promise<Result<null>> {
        try {
            await prisma.notificationTemplate.delete({
                where: { name }
            });

            return this.success(null);
        } catch (error) {
            console.error('Error deleting notification template:', error);
            return this.serverError();
        }
    }

    // ============ WEBHOOK MANAGEMENT ============

    /**
     * Create webhook endpoint
     */
    async createWebhookEndpoint(data: CreateWebhookEndpoint): Promise<Result<WebhookEndpoint>> {
        try {
            const webhook = await prisma.webhookEndpoint.create({
                data: {
                    ...data,
                    id: createId(),
                    secret: data.secret || this.generateWebhookSecret()
                }
            });

            return this.created(webhook);
        } catch (error) {
            console.error('Error creating webhook endpoint:', error);
            return this.serverError();
        }
    }

    /**
     * Get webhook endpoints
     */
    async getWebhookEndpoints(): Promise<Result<WebhookEndpoint[]>> {
        try {
            const webhooks = await prisma.webhookEndpoint.findMany({
                orderBy: { createdAt: 'desc' }
            });

            return this.success(webhooks);
        } catch (error) {
            console.error('Error fetching webhook endpoints:', error);
            return this.serverError();
        }
    }

    /**
     * Update webhook endpoint
     */
    async updateWebhookEndpoint(webhookId: string, data: Partial<CreateWebhookEndpoint>): Promise<Result<WebhookEndpoint>> {
        try {
            const webhook = await prisma.webhookEndpoint.update({
                where: { id: webhookId },
                data
            });

            return this.success(webhook);
        } catch (error) {
            console.error('Error updating webhook endpoint:', error);
            return this.serverError();
        }
    }

    /**
     * Delete webhook endpoint
     */
    async deleteWebhookEndpoint(webhookId: string): Promise<Result<null>> {
        try {
            await prisma.webhookEndpoint.delete({
                where: { id: webhookId }
            });

            return this.success(null);
        } catch (error) {
            console.error('Error deleting webhook endpoint:', error);
            return this.serverError();
        }
    }

    /**
     * Record webhook delivery attempt
     */
    async recordWebhookDelivery(
        webhookId: string,
        notificationId: string,
        status: string,
        responseCode?: number,
        responseBody?: string,
        errorMessage?: string
    ): Promise<Result<WebhookDelivery>> {
        try {
            const delivery = await prisma.webhookDelivery.create({
                data: {
                    id: createId(),
                    webhookId,
                    notificationId,
                    status,
                    responseCode,
                    responseBody,
                    errorMessage,
                    attempts: 1
                }
            });

            return this.success(delivery);
        } catch (error) {
            console.error('Error recording webhook delivery:', error);
            return this.serverError();
        }
    }

    // ============ UTILITY METHODS ============

    /**
     * Generate a secure webhook secret
     */
    private generateWebhookSecret(): string {
        return createId() + '_' + Date.now().toString(36);
    }

    /**
     * Check if user should receive notification based on preferences
     */
    async shouldSendNotification(
        userId: string,
        notificationType: string,
        channel: string,
        currentTime: Date = new Date()
    ): Promise<boolean> {
        try {
            const preferences = await prisma.userNotificationPreferences.findUnique({
                where: {
                    userId_notificationType: {
                        userId,
                        notificationType
                    }
                }
            });

            if (!preferences || !preferences.enabled) {
                return false;
            }

            // Check channel-specific preferences
            const channelEnabled = {
                'email': preferences.emailEnabled,
                'sms': preferences.smsEnabled,
                'push': preferences.pushEnabled,
                'in_app': preferences.inAppEnabled,
                'webhook': preferences.webhookEnabled
            }[channel];

            if (!channelEnabled) {
                return false;
            }

            // Check quiet hours
            if (preferences.quietHoursEnabled && preferences.quietHoursStart && preferences.quietHoursEnd) {
                const currentHour = currentTime.getHours();
                const currentMinute = currentTime.getMinutes();
                const currentTimeMinutes = currentHour * 60 + currentMinute;

                const [startHour, startMinute] = preferences.quietHoursStart.split(':').map(Number);
                const [endHour, endMinute] = preferences.quietHoursEnd.split(':').map(Number);
                
                const startTimeMinutes = startHour * 60 + startMinute;
                const endTimeMinutes = endHour * 60 + endMinute;

                // Handle quiet hours spanning midnight
                if (startTimeMinutes > endTimeMinutes) {
                    if (currentTimeMinutes >= startTimeMinutes || currentTimeMinutes <= endTimeMinutes) {
                        return false;
                    }
                } else {
                    if (currentTimeMinutes >= startTimeMinutes && currentTimeMinutes <= endTimeMinutes) {
                        return false;
                    }
                }
            }

            return true;
        } catch (error) {
            console.error('Error checking notification preferences:', error);
            // Default to allowing the notification if we can't check preferences
            return true;
        }
    }
}

export const notificationService = new NotificationService();