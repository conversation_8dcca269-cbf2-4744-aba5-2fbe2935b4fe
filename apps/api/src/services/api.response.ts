
import { NextResponse } from "next/server";
// import { auth } from "@clerk/nextjs/server";
import { Result as ApiResult, HttpStatusCode } from '@/types';

// import { getMessages } from "next-intl/server";

/**
 * ApiResponse class for handling API route responses with chainable methods
 * Provides a fluent interface for creating and sending responses
 * 
 * @example
 * ```typescript
 * // In an API route handler
 * export async function GET(request: Request) {
 *   return ApiResponse
 *     .success({ users: [] })
 *     .message('Users retrieved successfully')
 *     .send();
 * }
 * 
 * // With service integration
 * export async function POST(request: Request) {
 *   const userService = new UserService();
 *   const result = await userService.createUser(userData);
 *   return ApiResponse.fromResult(result).send();
 * }
 * 
 * // Chained error handling
 * export async function DELETE(request: Request) {
 *   try {
 *     const result = await someOperation();
 *     return ApiResponse.success(result).message('Deleted successfully').send();
 *   } catch (error) {
 *     return ApiResponse.serverError().message('Failed to delete').send();
 *   }
 * }
 * ```
 */
export class ApiResponse<TData = any> {
  private result: ApiResult<TData>;

  private constructor(result: ApiResult<TData>) {
    this.result = { ...result };
  }

  // ========== STATIC FACTORY METHODS ==========

  /**
   * Create ApiResponse from existing ApiResult (from BaseService)
   */
  static fromResult<TData>(result: ApiResult<TData>): ApiResponse<TData> {
    return new ApiResponse(result);
  }

  /**
   * Create a successful response
   */
  static success<TData>(data: TData, statusCode: HttpStatusCode = HttpStatusCode.OK): ApiResponse<TData> {
    return new ApiResponse({
      success: true,
      message: 'Operation completed successfully',
      data,
      statusCode
    });
  }

  /**
   * Create a created response (201)
   */
  static created<TData>(data: TData): ApiResponse<TData> {
    return new ApiResponse({
      success: true,
      message: 'Resource created successfully',
      data,
      statusCode: HttpStatusCode.CREATED
    });
  }

  /**
   * Create a no content response (204)
   */
  static noContent(): ApiResponse<null> {
    return new ApiResponse({
      success: true,
      message: 'Operation completed successfully',
      data: null,
      statusCode: HttpStatusCode.NO_CONTENT
    });
  }

  /**
   * Create a bad request error response (400)
   */
  static badRequest<TData = null>(message?: string, data: TData = null as TData): ApiResponse<TData> {
    return new ApiResponse({
      success: false,
      message: message || 'Bad request',
      data,
      statusCode: HttpStatusCode.BAD_REQUEST
    });
  }

  /**
   * Create an unauthorized error response (401)
   */
  static unauthorized<TData = null>(message?: string, data: TData = null as TData): ApiResponse<TData> {
    return new ApiResponse({
      success: false,
      message: message || 'Unauthorized access',
      data,
      statusCode: HttpStatusCode.UNAUTHORIZED
    });
  }

  /**
   * Create a forbidden error response (403)
   */
  static forbidden<TData = null>(message?: string, data: TData = null as TData): ApiResponse<TData> {
    return new ApiResponse({
      success: false,
      message: message || 'Access forbidden',
      data,
      statusCode: HttpStatusCode.FORBIDDEN
    });
  }

  /**
   * Create a not found error response (404)
   */
  static notFound<TData = null>(message?: string, data: TData = null as TData): ApiResponse<TData> {
    return new ApiResponse({
      success: false,
      message: message || 'Resource not found',
      data,
      statusCode: HttpStatusCode.NOT_FOUND
    });
  }

  /**
   * Create a conflict error response (409)
   */
  static conflict<TData = null>(message?: string, data: TData = null as TData): ApiResponse<TData> {
    return new ApiResponse({
      success: false,
      message: message || 'Resource conflict',
      data,
      statusCode: HttpStatusCode.CONFLICT
    });
  }

  /**
   * Create a validation error response (422)
   */
  static validationError<TData = null>(
    message?: string, 
    errors?: Record<string, string[]>, 
    data: TData = null as TData
  ): ApiResponse<TData> {
    return new ApiResponse({
      success: false,
      message: message || 'Validation failed',
      data,
      statusCode: HttpStatusCode.UNPROCESSABLE_ENTITY,
      errors
    });
  }

  /**
   * Create a server error response (500)
   */
  static serverError<TData = null>(message?: string, data: TData = null as TData): ApiResponse<TData> {
    return new ApiResponse({
      success: false,
      message: message || 'Internal server error',
      data,
      statusCode: HttpStatusCode.INTERNAL_SERVER_ERROR
    });
  }

  // ========== CHAINABLE METHODS ==========

  /**
   * Set custom message
   */
  message(message: string): ApiResponse<TData> {
    this.result.message = message;
    return this;
  }

  /**
   * Set redirect URL
   */
  redirectTo(url: string): ApiResponse<TData> {
    this.result.redirectTo = url;
    return this;
  }

  /**
   * Set validation errors
   */
  errors(errors: Record<string, string[]>): ApiResponse<TData> {
    this.result.errors = errors;
    return this;
  }

  /**
   * Set metadata
   */
  metadata(metadata: Record<string, any>): ApiResponse<TData> {
    this.result.metadata = metadata;
    return this;
  }

  /**
   * Set status code
   */
  status(statusCode: HttpStatusCode): ApiResponse<TData> {
    this.result.statusCode = statusCode;
    return this;
  }

  /**
   * Add single metadata field
   */
  meta(key: string, value: any): ApiResponse<TData> {
    if (!this.result.metadata) {
      this.result.metadata = {};
    }
    this.result.metadata[key] = value;
    return this;
  }

  // ========== UTILITY METHODS ==========

  /**
   * Get the raw ApiResult
   */
  getResult(): ApiResult<TData> {
    return { ...this.result };
  }

  static log(data?: any) {
    console.log('API Response:', data);
  }

  /**
   * Convert to Response using NextResponse.json() and send
   */
  send(): Response {
    return NextResponse.json(
      {
        success: this.result.success,
        message: this.result.message,
        data: this.result?.data,
        errors: this.result?.errors,
        metadata: this.result?.metadata,
        statusCode: this.result.statusCode,
        ...(this.result.redirectTo && { redirectTo: this.result.redirectTo })
      },
      { status: this.result.statusCode }
    );
  }

  /**
   * Convert to JSON (useful for testing)
   */
  toJSON(): ApiResult<TData> {
    return this.getResult();
  }

  // ========== STATIC UTILITY METHODS ==========

  /**
   * Handle async operations with automatic error responses
   */
  static async handle<TData>(
    operation: () => Promise<TData>,
    errorMessage?: string,
    successMessage?: string
  ): Promise<ApiResponse<TData | null>> {
    try {
      const data = await operation();
      return ApiResponse.success(data).message(successMessage || 'Operation completed successfully');
    } catch (error) {
      console.error('API operation failed:', error);
      const message = error instanceof Error ? error.message : errorMessage || 'An unexpected error occurred';
      return ApiResponse.serverError(message);
    }
  }

          /**
       * method to validate required fields
       */
      static validateRequiredFields(data: Record<string, any>, requiredFields: string[]): { isValid: boolean; errors: Record<string, string[]> } {
        
        const errors: Record<string, string[]> = {};
        
        requiredFields.forEach(field => {
          if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
            errors[field] = [`${field} is required`];
          }
        });

        return {
          isValid: Object.keys(errors).length === 0,
          errors
        };
      }

  /**
   * Validate request data and return appropriate responses
   */
  static validateRequest(
    data: Record<string, any>,
    requiredFields: string[]
  ): ApiResponse<null> | null {
    const validation = ApiResponse.validateRequiredFields(data, requiredFields);
    
    if (!validation.isValid) {
      return ApiResponse.validationError('Please provide all required fields', validation.errors);
    }
    
    return null; // No validation error
  }

  /**
   * Check authentication and return appropriate response
   */
//   static async requireAuth(): Promise<{ userId?: string; clerkUserId?: string }> {
//     try {
//       const {userId: clerkUserId, sessionClaims } = await auth() || {}; 
//       return { userId: sessionClaims?.metadata?.requestId, clerkUserId: clerkUserId as string | undefined };
//     } catch (error) {
//       return { userId: undefined, clerkUserId: undefined };
//     }
//   }

  static zodRunner<TData = any>(schema: any, data: TData, resource: string = 'resource'): ApiResponse<TData | null> {
    const validationResult = schema.safeParse(data);
    if (!validationResult.success) {
      const { fieldErrors, formErrors } = validationResult.error.flatten()
      const result = {
        success: false,
        message: `Invalid ${resource.toLowerCase()} data provided`,
        data: null,
        statusCode: HttpStatusCode.UNPROCESSABLE_ENTITY,
        errors: { ...fieldErrors, ...formErrors }
      };
      return ApiResponse.fromResult(result);
    }
    return ApiResponse.success(validationResult.data);
  }

  /**
   * Check if response is successful
   */
  isSuccess(): boolean {
    return this.result.success;
  }

  /**
   * Get status code
   */
  getStatusCode(): number {
    return this.result.statusCode;
  }
  /**
   * Get data
   */
  getData(): TData {
    return this.result.data;
  }

  /**
   * Get Errors
   */
  getErrors(): Record<string, string[]> | undefined {
    return this.result.errors;
  }

  /**
   * Get messages
   */
  getMessages(): string {
    return this.result.message;
  }
}

// ========== USAGE EXAMPLES ==========

/**
 * Example API route handlers showing different usage patterns
 */

// Example 1: Direct ApiResponse usage
export async function exampleGet() {
  return ApiResponse
    .success({ users: [], total: 0 })
    .message('Users retrieved successfully')
    .meta('page', 1)
    .meta('limit', 10)
    .send();
}

// Example 2: With service integration
export async function examplePost() {
  // Simulate service result
  const serviceResult: ApiResult<any> = {
    success: true,
    message: 'User created',
    data: { id: '123', name: 'John' },
    statusCode: 201
  };
  
  return ApiResponse
    .fromResult(serviceResult)
    .redirectTo('/dashboard')
    .send();
}

// Example 3: Error handling with async operations
export async function exampleDelete() {
  return ApiResponse.handle(
    async () => {
      // Simulate some async operation
      await new Promise(resolve => setTimeout(resolve, 100));
      return { deleted: true };
    },
    'Failed to delete resource',
    'Resource deleted successfully'
  ).then(response => response.send());
}

// Example 4: Authentication and validation
// export async function exampleProtectedRoute(request: Request) {
//   // Check authentication
//   const auth = await ApiResponse.requireAuth();
//   if (!auth.clerkUserId) {
//     return ApiResponse.unauthorized('Authentication required').send(); // Return unauthorized response
//   }
  
//   // Parse request data
//   const data = await request.json();
  
//   // Validate required fields
//   const validation = ApiResponse.validateRequest(data, ['name', 'email']);
//   if (validation) {
//     return validation.send(); // Return validation error
//   }
  
//   // Process request
//   return ApiResponse
//     .created({ id: '123', ...data })
//     .message('Resource created successfully')
//     .send();
// }