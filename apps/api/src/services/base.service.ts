import { Result, HttpStatusCode } from '@/types';
import { hash, compare } from 'bcryptjs';

/**
 * Base service class providing standardized result creation methods for consistent API response format.
 * It should be extended by classes inside .service.ts files for consistent API response format.
 * It can also be used in /api/* files to handle API route responses.
 * 
 * @example
 * ```typescript
 * class UserService extends BaseService {
 *   constructor() {
 *     super('User');
 *   }
 *   
 *   async getUser(id: string): Promise<Result<User>> {
 *     try {
 *       const user = await prisma.users.findUnique({ where: { id } });
 *       if (!user) {
 *         return this.notFound();
 *       }
 *       return this.success(user, 'User retrieved successfully');
 *     } catch (error) {
 *       return this.serverError('Failed to retrieve user');
 *     }
 *   }
 * }
 * ```
 */
export abstract class BaseService {
      protected readonly resourceName: string;
      
      // Pre-defined message templates
      private readonly messages = {
        validation: (resource: string) => `Invalid ${resource.toLowerCase()} data provided`,
        notFound: (resource: string) => `${resource} not found`,
        unauthorized: () => 'Authentication required to access this resource',
        forbidden: () => 'Insufficient permissions to perform this action',
        conflict: (resource: string) => `${resource} already exists or conflicts with existing data`,
        badRequest: () => 'The request could not be processed due to invalid parameters',
        serverError: () => 'An unexpected error occurred while processing your request',
        created: (resource: string) => `${resource} created successfully`,
        updated: (resource: string) => `${resource} updated successfully`,
        deleted: (resource: string) => `${resource} deleted successfully`,
        retrieved: (resource: string) => `${resource} retrieved successfully`,
        operationComplete: (resource: string) => `${resource} operation completed successfully`
      };

      constructor(resourceName?: string) {
        this.resourceName = resourceName || 'Resource';
      }

      // ========== SUCCESS RESPONSES ==========

      /**
       * Create a successful response with data
       */
      protected success<TData>(
        data: TData,
        message?: string,
        statusCode: HttpStatusCode = HttpStatusCode.OK,
        redirectTo?: string,
        metadata?: Record<string, any>
      ): Result<TData> {
        return {
          success: true,
          message: message || this.messages.operationComplete(this.resourceName),
          data,
          statusCode,
          redirectTo,
          metadata
        };
      }

      /**
       * Create a successful creation response (201)
       */
      protected created<TData>(
        data: TData,
        message?: string,
        redirectTo?: string
      ): Result<TData> {
        return this.success(
          data,
          message || this.messages.created(this.resourceName),
          HttpStatusCode.CREATED,
          redirectTo
        );
      }

      /**
       * Create a successful update response
       */
      protected updated<TData>(
        data: TData,
        message?: string
      ): Result<TData> {
        return this.success(
          data,
          message || this.messages.updated(this.resourceName)
        );
      }

      /**
       * Create a successful deletion response
       */
      protected deleted<TData = null>(
        message?: string,
        data: TData = null as TData
      ): Result<TData> {
        return this.success(
          data,
          message || this.messages.deleted(this.resourceName),
          HttpStatusCode.NO_CONTENT
        );
      }

      // ========== ERROR RESPONSES ==========

      /**
       * Create a validation error response (400)
       */
      protected validationError<TData = null>(
        message?: string,
        errors?: Record<string, string[]>,
        data: TData = null as TData
      ): Result<TData> {
        return {
          success: false,
          message: message || this.messages.validation(this.resourceName),
          data,
          statusCode: HttpStatusCode.BAD_REQUEST,
          errors
        };
      }

      /**
       * Create a not found error response (404)
       */
      protected notFound<TData = null>(
        message?: string,
        data: TData = null as TData
      ): Result<TData> {
        return {
          success: false,
          message: message || this.messages.notFound(this.resourceName),
          data,
          statusCode: HttpStatusCode.NOT_FOUND
        };
      }

      /**
       * Create an unauthorized error response (401)
       */
      protected unauthorized<TData = null>(
        message?: string,
        data: TData = null as TData
      ): Result<TData> {
        return {
          success: false,
          message: message || this.messages.unauthorized(),
          data,
          statusCode: HttpStatusCode.UNAUTHORIZED
        };
      }

      /**
       * Create a forbidden error response (403)
       */
      protected forbidden<TData = null>(
        message?: string,
        data: TData = null as TData
      ): Result<TData> {
        return {
          success: false,
          message: message || this.messages.forbidden(),
          data,
          statusCode: HttpStatusCode.FORBIDDEN
        };
      }

      /**
       * Create a conflict error response (409)
       */
      protected conflict<TData = null>(
        message?: string,
        data: TData = null as TData
      ): Result<TData> {
        return {
          success: false,
          message: message || this.messages.conflict(this.resourceName),
          data,
          statusCode: HttpStatusCode.CONFLICT
        };
      }

      /**
       * Create a bad request error response (400)
       */
      protected badRequest<TData = null>(
        message?: string,
        data: TData = null as TData
      ): Result<TData> {
        return {
          success: false,
          message: message || this.messages.badRequest(),
          data,
          statusCode: HttpStatusCode.BAD_REQUEST
        };
      }

      /**
       * Create a server error response (500)
       */
      protected serverError<TData = null>(
        message?: string,
        data: TData = null as TData
      ): Result<TData> {
        return {
          success: false,
          message: message || this.messages.serverError(),
          data,
          statusCode: HttpStatusCode.INTERNAL_SERVER_ERROR
        };
      }

        /**
         * method to handle common async operations with error catching
         */
        protected async handleAsync<TData>(
          operation: () => Promise<TData>,
          errorMessage?: string
        ): Promise<Result<TData | null>> {
          try {
            const data = await operation();
            return {
              success: true,
              message: 'Operation completed successfully',
              data,
              statusCode: HttpStatusCode.OK
            };
          } catch (error) {
            console.error('Service operation failed:', error);
            return {
              success: false,
              message: errorMessage || 'An unexpected error occurred',
              data: null,
              statusCode: HttpStatusCode.INTERNAL_SERVER_ERROR
            };
          }
        }

        /**
       * method to validate required fields
       */
      protected validateRequiredFields(data: Record<string, any>, requiredFields: string[]): { isValid: boolean; errors: Record<string, string[]> } {
        
        const errors: Record<string, string[]> = {};
        
        requiredFields.forEach(field => {
          if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
            errors[field] = [`${field} is required`];
          }
        });

        return {
          isValid: Object.keys(errors).length === 0,
          errors
        };
      }

      /* Helper function to hash API keys */
      protected async hashApiKey(apiKey: string): Promise<string> {
          return await hash(apiKey, process.env.BCRYPT_HASH_ROUNDS || 10);
      }
}