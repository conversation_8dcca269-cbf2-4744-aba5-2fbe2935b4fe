import { prisma, withTransaction } from '@/lib/db';
import { Novu } from '@novu/node';
import {
    CreateNotificationRequest,
    UpdateNotificationRequest,
    BaseNotification,
    NotificationType,
    NotificationPriority,
    DeliveryChannels,
    NotificationStatus,
} from '@sparkstrand/notifications-core';

// Initialize Novu client (will be overridden per application) with timeout and error handling
let defaultNovu: Novu | null = null;
if (process.env.NOVU_API_KEY) {
    try {
        defaultNovu = new Novu(process.env.NOVU_API_KEY);
    } catch (error) {
        // Silent fail in production, only log in development
        if (process.env.NODE_ENV === 'development') {
            console.error('Failed to initialize Novu client:', error);
        }
    }
}

// Add timeout wrapper for external API calls
const withTimeout = <T>(promise: Promise<T>, timeoutMs: number = 10000): Promise<T> => {
    const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs);
    });
    return Promise.race([promise, timeoutPromise]);
};

export class NotificationService {
    /**
     * Create a new notification
     */
    async createNotification(data: CreateNotificationRequest): Promise<BaseNotification> {
        try {
            return await withTransaction(async (tx) => {
                // Validate application exists with timeout
                const app = await withTimeout(
                    tx.application.findUnique({
                        where: { appId: data.appId || 'default-app-key' },
                    }),
                    5000
                );

                if (!app) {
                    throw new Error(`Application not found: ${data.appId}`);
                }

                // Validate recipient exists
                const recipient = await withTimeout(
                    tx.user.findUnique({
                        where: { id: data.userId },
                    }),
                    5000
                );

                if (!recipient) {
                    throw new Error(`Recipient not found: ${data.userId}`);
                }

                // Create notification record
                const notification = await withTimeout(
                    tx.notification.create({
                        data: {
                            title: data.title,
                            message: data.content,
                            type: data.type || 'INFO',
                            priority: data.priority || 'NORMAL',
                            recipientId: data.userId,
                            appId: app.appId,
                            scheduledFor: data.scheduledFor,
                            expiresAt: data.expiresAt,
                            metadata: data.metadata || {},
                            status: 'PENDING',
                        },
                        include: {
                            recipient: true,
                            app: true,
                        },
                    }),
                    10000
                );

                // Create delivery status records for each channel
                const channels = data.channels || { inApp: true };
                const enabledChannels = Object.entries(channels)
                    .filter(([_, enabled]) => enabled)
                    .map(([channel, _]) => channel);

                const deliveryRecords = enabledChannels.map(channel => ({
                    notificationId: notification.id,
                    channel,
                    provider: this.getProviderForChannel(channel),
                    status: 'PENDING',
                    attempts: 0,
                }));

                await withTimeout(
                    tx.deliveryStatus.createMany({
                        data: deliveryRecords,
                    }),
                    5000
                );

                // Send via Novu if configured for this application
                if (app.novuApiKey) {
                    await withTimeout(
                        this.sendViaNovu(notification, recipient, enabledChannels),
                        15000
                    );
                }

                // Send via direct providers if configured for this application (with timeout)
                await withTimeout(
                    this.sendViaDirectProviders(notification, enabledChannels),
                    10000
                );

                // Convert to BaseNotification format
                return this.mapToBaseNotification(notification);
            }, 60000); // 60 second transaction timeout
        } catch (error) {
            console.error('Error creating notification:', error);
            throw error;
        }
    }

    /**
     * Get notifications with filters and proper pagination limits
     */
    async getNotifications(filters: {
        userId?: string;
        appId?: string;
        status?: NotificationStatus;
        type?: NotificationType;
        limit?: number;
        offset?: number;
    }): Promise<{
        notifications: BaseNotification[];
        pagination: {
            total: number;
            limit: number;
            offset: number;
            hasMore: boolean;
        };
    }> {
        try {
            const { userId, appId, status, type, limit = 50, offset = 0 } = filters;

            // Enforce reasonable limits to prevent memory issues
            const safeLimit = Math.min(Math.max(limit, 1), 100);
            const safeOffset = Math.max(offset, 0);

            // Build where clause
            const where: any = {};

            if (userId) {
                where.recipientId = userId;
            }

            if (appId) {
                where.app = { appId: appId };
            }

            if (status) {
                where.status = status;
            }

            if (type) {
                where.type = type;
            }

            // Get total count
            const total = await withTimeout(
                prisma.notification.count({ where }),
                10000
            );

            // Get notifications with pagination
            const notifications = await withTimeout(
                prisma.notification.findMany({
                    where,
                    include: {
                        recipient: true,
                        app: true,
                        deliveries: true,
                    },
                    orderBy: { createdAt: 'desc' },
                    take: safeLimit,
                    skip: safeOffset,
                }),
                15000
            );

            return {
                notifications: notifications.map(this.mapToBaseNotification),
                pagination: {
                    total,
                    limit: safeLimit,
                    offset: safeOffset,
                    hasMore: safeOffset + safeLimit < total,
                },
            };
        } catch (error) {
            console.error('Error fetching notifications:', error);
            throw error;
        }
    }

    /**
     * Update notification
     */
    async updateNotification(id: string, data: UpdateNotificationRequest): Promise<BaseNotification> {
        if (!id) {
            throw new Error('Notification ID is required');
        }

        const updates = data;

        const existingNotification = await prisma.notification.findUnique({
            where: { id },
            include: { deliveries: true },
        });

        if (!existingNotification) {
            throw new Error('Notification not found');
        }

        // Update notification
        const updatedNotification = await prisma.notification.update({
            where: { id },
            data: {
                ...updates,
                updatedAt: new Date(),
                ...(updates.status === 'READ' && !existingNotification.readAt && { readAt: new Date() }),
            },
            include: {
                recipient: true,
                app: true,
                deliveries: true,
            },
        });

        // Update delivery status if status changed
        if (updates.status && updates.status !== existingNotification.status) {
            await prisma.deliveryStatus.updateMany({
                where: { notificationId: id },
                data: { status: updates.status },
            });
        }

        // Update via Novu if needed
        if (process.env.NOVU_API_KEY && updates.status) {
            await this.updateViaNovu(updatedNotification);
        }

        return this.mapToBaseNotification(updatedNotification);
    }

    /**
     * Delete notification
     */
    async deleteNotification(id: string): Promise<void> {
        const existingNotification = await prisma.notification.findUnique({
            where: { id },
        });

        if (!existingNotification) {
            throw new Error('Notification not found');
        }

        // Delete from Novu if needed
        if (process.env.NOVU_API_KEY && existingNotification.novuNotificationId) {
            await this.deleteViaNovu(existingNotification.novuNotificationId);
        }

        // Delete notification (cascades to delivery status)
        await prisma.notification.delete({
            where: { id },
        });
    }

    /**
     * Mark notification as read
     */
    async markAsRead(id: string): Promise<BaseNotification> {
        return this.updateNotification(id, {
            status: NotificationStatus.READ,
        });
    }

    /**
     * Get notification by ID
     */
    async getNotificationById(id: string): Promise<BaseNotification | null> {
        const notification = await prisma.notification.findUnique({
            where: { id },
            include: {
                recipient: true,
                app: true,
                deliveries: true,
            },
        });

        return notification ? this.mapToBaseNotification(notification) : null;
    }

    /**
     * Get user notification preferences
     */
    async getUserPreferences(userId: string) {
        return await prisma.userNotificationPreferences.findMany({
            where: { userId },
        });
    }

    /**
     * Update user notification preferences
     */
    async updateUserPreferences(userId: string, preferences: any) {
        try {
            // Validate user exists
            const user = await prisma.user.findUnique({
                where: { id: userId },
            });

            if (!user) {
                throw new Error(`User not found: ${userId}`);
            }

            // Update user preferences
            const updatedUser = await prisma.user.update({
                where: { id: userId },
                data: {
                    preferences: preferences,
                    updatedAt: new Date(),
                },
            });

            // Update specific notification type preferences if provided
            if (preferences.notificationTypes) {
                for (const [type, config] of Object.entries(preferences.notificationTypes)) {
                    await prisma.userNotificationPreferences.upsert({
                        where: {
                            userId_notificationType: {
                                userId: userId,
                                notificationType: type,
                            },
                        },
                        update: {
                            enabled: config.enabled !== undefined ? config.enabled : true,
                            priority: config.priority || 'NORMAL',
                            emailEnabled: config.emailEnabled !== undefined ? config.emailEnabled : true,
                            smsEnabled: config.smsEnabled !== undefined ? config.smsEnabled : false,
                            pushEnabled: config.pushEnabled !== undefined ? config.pushEnabled : true,
                            inAppEnabled: config.inAppEnabled !== undefined ? config.inAppEnabled : true,
                            quietHoursEnabled: config.quietHoursEnabled !== undefined ? config.quietHoursEnabled : false,
                            quietHoursStart: config.quietHoursStart || '22:00',
                            quietHoursEnd: config.quietHoursEnd || '08:00',
                        },
                        create: {
                            userId: userId,
                            notificationType: type,
                            enabled: config.enabled !== undefined ? config.enabled : true,
                            priority: config.priority || 'NORMAL',
                            emailEnabled: config.emailEnabled !== undefined ? config.emailEnabled : true,
                            smsEnabled: config.smsEnabled !== undefined ? config.smsEnabled : false,
                            pushEnabled: config.pushEnabled !== undefined ? config.pushEnabled : true,
                            inAppEnabled: config.inAppEnabled !== undefined ? config.inAppEnabled : true,
                            quietHoursEnabled: config.quietHoursEnabled !== undefined ? config.quietHoursEnabled : false,
                            quietHoursStart: config.quietHoursStart || '22:00',
                            quietHoursEnd: config.quietHoursEnd || '08:00',
                        },
                    });
                }
            }

            return updatedUser;
        } catch (error) {
            console.error('Error updating user preferences:', error);
            throw error;
        }
    }

    // Private helper methods

    private getProviderForChannel(channel: string): string {
        switch (channel) {
            case 'email':
                return process.env.RESEND_API_KEY ? 'resend' : 'novu';
            case 'sms':
                return process.env.TWILIO_ACCOUNT_SID ? 'twilio' : 'novu';
            case 'push':
                return 'novu';
            case 'inApp':
                return 'system';
            default:
                return 'novu';
        }
    }

    private async sendViaNovu(
        notification: any,
        recipient: any,
        channels: string[]
    ): Promise<void> {
        try {
            // Get application-specific configuration
            const app = await prisma.application.findUnique({
                where: { appId: notification.appId },
                select: { novuAppId: true, novuApiKey: true, defaultProvider: true }
            });

            if (!app?.novuApiKey) {
                // Only log in development
                if (process.env.NODE_ENV === 'development') {
                    console.log('No Novu API key configured for application, skipping Novu delivery');
                }
                return;
            }

            // Use application-specific Novu client
            const novuClient = new Novu(app.novuApiKey);

            // Get or create application-specific subscriber
            let appSubscriber = await prisma.applicationUserSubscription.findUnique({
                where: {
                    userId_appId: {
                        userId: recipient.id,
                        appId: notification.appId,
                    },
                },
            });

            if (!appSubscriber?.novuSubscriberId) {
                // Create Novu subscriber for this specific application
                const subscriberId = `${recipient.id}-${notification.appId}`;
                await novuClient.subscribers.identify(subscriberId, {
                    email: recipient.email,
                    firstName: recipient.name?.split(' ')[0],
                    lastName: recipient.name?.split(' ').slice(1).join(' '),
                });

                // Create or update application-specific subscription
                appSubscriber = await prisma.applicationUserSubscription.upsert({
                    where: {
                        userId_appId: {
                            userId: recipient.id,
                            appId: notification.appId,
                        },
                    },
                    update: {
                        novuSubscriberId: subscriberId,
                    },
                    create: {
                        userId: recipient.id,
                        appId: notification.appId,
                        novuSubscriberId: subscriberId,
                        preferences: {},
                    },
                });
            }

            // Send via Novu
            const result = await novuClient.trigger(notification.type.toLowerCase(), {
                to: {
                    subscriberId: appSubscriber.novuSubscriberId,
                    email: recipient.email,
                },
                payload: {
                    title: notification.title,
                    message: notification.message,
                    type: notification.type,
                    priority: notification.priority,
                    metadata: notification.metadata,
                },
            });

            // Update notification with Novu ID
            await prisma.notification.update({
                where: { id: notification.id },
                data: { novuNotificationId: result.data?._id },
            });

            // Update delivery status
            await prisma.deliveryStatus.updateMany({
                where: {
                    notificationId: notification.id,
                    provider: 'novu',
                },
                data: {
                    status: 'SENT',
                    providerMessageId: result.data?._id,
                    providerResponse: result,
                },
            });

        } catch (error) {
            console.error('Error sending via Novu:', error);

            // Update delivery status to failed
            await prisma.deliveryStatus.updateMany({
                where: {
                    notificationId: notification.id,
                    provider: 'novu',
                },
                data: {
                    status: 'FAILED',
                    errorMessage: error instanceof Error ? error.message : 'Unknown error',
                    errorCode: 'NOVU_ERROR',
                },
            });
        }
    }

    /**
     * Send notification via direct providers (bypassing Novu)
     */
    private async sendViaDirectProviders(
        notification: any,
        channels: string[]
    ): Promise<void> {
        try {
            // Get application-specific configuration
            const app = await prisma.application.findUnique({
                where: { appId: notification.appId },
                select: {
                    resendApiKey: true,
                    twilioAccountSid: true,
                    twilioAuthToken: true,
                    defaultProvider: true
                }
            });

            // Check if we should send directly (e.g., for email via Resend)
            if (channels.includes('email') && app?.resendApiKey) {
                await this.sendEmailDirectly(notification, app.resendApiKey);
            }

            // Add other direct provider integrations here
            // SMS via Twilio, Push, etc.
            if (channels.includes('sms') && app?.twilioAccountSid && app?.twilioAuthToken) {
                await this.sendSMSDirectly(notification, app.twilioAccountSid, app.twilioAuthToken);
            }

        } catch (error) {
            console.error('Error sending via direct providers:', error);
        }
    }

    private async sendEmailDirectly(notification: any, resendApiKey: string): Promise<void> {
        try {
            const response = await fetch('https://api.resend.com/emails', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${resendApiKey}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    from: '<EMAIL>',
                    to: notification.recipient.email,
                    subject: notification.title,
                    html: `
                        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                            <h2>${notification.title}</h2>
                            <p>${notification.message}</p>
                            <div style="margin-top: 20px; padding: 10px; background-color: #f5f5f5;">
                                <small>Type: ${notification.type} | Priority: ${notification.priority}</small>
                            </div>
                        </div>
                    `,
                }),
            });

            if (!response.ok) {
                throw new Error(`Resend API error: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();

            await prisma.deliveryStatus.updateMany({
                where: {
                    notificationId: notification.id,
                    channel: 'email',
                    provider: 'resend',
                },
                data: {
                    status: 'SENT',
                    deliveredAt: new Date(),
                    providerMessageId: result.id,
                    providerResponse: { success: true, provider: 'resend', result },
                },
            });
        } catch (error) {
            console.error('Error sending email directly:', error);

            await prisma.deliveryStatus.updateMany({
                where: {
                    notificationId: notification.id,
                    channel: 'email',
                    provider: 'resend',
                },
                data: {
                    status: 'FAILED',
                    errorMessage: error instanceof Error ? error.message : 'Unknown error',
                    errorCode: 'RESEND_ERROR',
                },
            });

            throw error;
        }
    }

    private async sendSMSDirectly(notification: any, twilioAccountSid: string, twilioAuthToken: string): Promise<void> {
        try {
            const auth = Buffer.from(`${twilioAccountSid}:${twilioAuthToken}`).toString('base64');
            const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${twilioAccountSid}/Messages.json`, {
                method: 'POST',
                headers: {
                    'Authorization': `Basic ${auth}`,
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    To: notification.recipient.phone || '+**********',
                    From: '+**********',
                    Body: `${notification.title}: ${notification.message}`,
                }),
            });

            if (!response.ok) {
                throw new Error(`Twilio API error: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();

            await prisma.deliveryStatus.updateMany({
                where: {
                    notificationId: notification.id,
                    channel: 'sms',
                    provider: 'twilio',
                },
                data: {
                    status: 'SENT',
                    deliveredAt: new Date(),
                    providerMessageId: result.sid,
                    providerResponse: { success: true, provider: 'twilio', result },
                },
            });
        } catch (error) {
            console.error('Error sending SMS directly:', error);

            await prisma.deliveryStatus.updateMany({
                where: {
                    notificationId: notification.id,
                    channel: 'sms',
                    provider: 'twilio',
                },
                data: {
                    status: 'FAILED',
                    errorMessage: error instanceof Error ? error.message : 'Unknown error',
                    errorCode: 'TWILIO_ERROR',
                },
            });

            throw error;
        }
    }

    private async updateViaNovu(notification: any): Promise<void> {
        try {
            // Update notification status in Novu if needed
            if (!notification.novuNotificationId) {
                // Only log in development
                if (process.env.NODE_ENV === 'development') {
                    console.log('No Novu notification ID found, skipping update');
                }
                return;
            }

            // Get application configuration
            const app = await prisma.application.findUnique({
                where: { appId: notification.appId },
                select: { novuApiKey: true }
            });

            if (!app?.novuApiKey) {
                // Only log in development
                if (process.env.NODE_ENV === 'development') {
                    console.log('No Novu API key configured, skipping update');
                }
                return;
            }

            const novuClient = new Novu(app.novuApiKey);

            // Update notification in Novu
            await novuClient.notifications.update(notification.novuNotificationId, {
                status: notification.status,
                metadata: notification.metadata,
            });

            // Only log in development
            if (process.env.NODE_ENV === 'development') {
                console.log('Successfully updated notification in Novu:', notification.id);
            }
        } catch (error) {
            console.error('Error updating in Novu:', error);
            throw error;
        }
    }

    private async deleteViaNovu(notificationId: string): Promise<void> {
        try {
            // Delete notification from Novu if needed
            if (!notificationId) {
                // Only log in development
                if (process.env.NODE_ENV === 'development') {
                    console.log('No Novu notification ID provided, skipping deletion');
                }
                return;
            }

            // Get application configuration
            const app = await prisma.application.findUnique({
                where: { appId: notificationId },
                select: { novuApiKey: true }
            });

            if (!app?.novuApiKey) {
                // Only log in development
                if (process.env.NODE_ENV === 'development') {
                    console.log('No Novu API key configured, skipping deletion');
                }
                return;
            }

            const novuClient = new Novu(app.novuApiKey);

            // Delete notification from Novu
            await novuClient.notifications.delete(notificationId);

            // Only log in development
            if (process.env.NODE_ENV === 'development') {
                console.log('Successfully deleted notification from Novu:', notificationId);
            }
        } catch (error) {
            console.error('Error deleting from Novu:', error);
            throw error;
        }
    }

    private mapToBaseNotification(dbNotification: any): BaseNotification {
        return {
            id: dbNotification.id,
            appId: dbNotification.app.appId,
            userId: dbNotification.recipientId,
            type: dbNotification.type as NotificationType,
            title: dbNotification.title,
            content: dbNotification.message,
            metadata: dbNotification.metadata,
            priority: dbNotification.priority as NotificationPriority,
            status: dbNotification.status as NotificationStatus,
            channels: dbNotification.deliveries.map((d: any) => d.channel) as DeliveryChannels,
            scheduledFor: dbNotification.scheduledFor,
            expiresAt: dbNotification.expiresAt,
            isRead: dbNotification.status === 'READ',
            readAt: dbNotification.readAt,
            createdAt: dbNotification.createdAt,
            updatedAt: dbNotification.updatedAt,
        };
    }
}

export const notificationService = new NotificationService();
