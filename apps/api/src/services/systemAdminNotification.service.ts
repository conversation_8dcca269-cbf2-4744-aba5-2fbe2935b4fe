import { BaseService } from "./base.service";
import { AppRegistration } from "@/types";


export class SystemAdminNotificationService extends BaseService {
    constructor() {
        super('SystemAdminNotification');
    }

        
    // Helper function to send admin notification
    async sendAdminNotification(appData: AppRegistration) {
        try {
            // Use the notification system's own infrastructure to notify admins
            const notificationData = {
                type: 'APP_REGISTRATION_REQUEST',
                appId: appData.appId,
                appName: appData.name,
                contactEmail: appData.contactEmail,
                contactName: appData.contactName,
                website: appData.website,
                businessType: appData.businessType,
                expectedVolume: appData.expectedVolume,
                message: `New application registration request from ${appData.name} (${appData.appId})`,
                timestamp: new Date()
            };

            // Try to send via Novu first (if configured)
            if (process.env.NOVU_API_KEY && process.env.NOVU_APP_ID) {
                try {
                    const novuPayload = {
                        name: 'system-admin-notification',
                        to: {
                            subscriberId: 'admin-bolu',
                            email: '<EMAIL>', //process.env.ADMIN_EMAIL || '<EMAIL>',
                            firstName: 'Admin'
                        },
                        payload: {
                            type: notificationData.type,
                            appId: notificationData.appId,
                            appName: notificationData.appName,
                            contactEmail: notificationData.contactEmail,
                            contactName: notificationData.contactName,
                            businessType: notificationData.businessType,
                            expectedVolume: notificationData.expectedVolume,
                            message: notificationData.message,
                            actionUrl: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/admin/applications/${notificationData.appId}/review`,
                            priority: 'HIGH'
                        }
                    };

                    const response = await fetch(`https://api.novu.co/v1/triggers/${novuPayload.name}`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `ApiKey ${process.env.NOVU_API_KEY}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(novuPayload)
                    });

                    if (response.ok) {
                        console.log('Admin notification sent via Novu successfully');
                        return;
                    }
                } catch (error) {
                    console.warn('Failed to send Novu notification, falling back to email:', error);
                }
            }

            // Fallback to Resend email
            if (process.env.RESEND_API_KEY) {
                try {
                    const emailData = {
                        from: process.env.RESEND_FROM_EMAIL || '<EMAIL>',
                        to: process.env.ADMIN_EMAIL || '<EMAIL>',
                        subject: `🚨 New App Registration: ${appData.name}`,
                        html: generateAdminEmailHTML(notificationData),
                        text: generateAdminEmailText(notificationData)
                    };

                    const response = await fetch('https://api.resend.com/emails', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${process.env.RESEND_API_KEY}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(emailData)
                    });

                    if (response.ok) {
                        console.log('Admin notification sent via Resend successfully');
                        return;
                    }
                } catch (error) {
                    console.warn('Failed to send Resend notification:', error);
                }
            }

            // Last resort: console log
            console.log('=== ADMIN NOTIFICATION ===');
            console.log('New app registration request:');
            console.log(`App: ${appData.name} (${appData.appId})`);
            console.log(`Contact: ${appData.contactName} at ${appData.contactEmail}`);
            console.log(`Business: ${appData.businessType || 'Not specified'}`);
            console.log(`Expected Volume: ${appData.expectedVolume || 'Not specified'} notifications/month`);
            console.log('==========================');

        } catch (error) {
            console.error('Failed to send admin notification:', error);
        }
    }

}