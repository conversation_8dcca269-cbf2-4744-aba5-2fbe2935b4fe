import { PrismaClient } from '@prisma/client';

// Global variable to prevent multiple Prisma Client instances in development
declare global {
    var __prisma: PrismaClient | undefined;
}

// Create a single Prisma Client instance with proper configuration
export const prisma = globalThis.__prisma || new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    errorFormat: 'pretty',
    // Connection pooling configuration
    datasources: {
        db: {
            url: process.env.DATABASE_URL,
        },
    },
    // Prevent connection leaks
    __internal: {
        engine: {
            enableEngineDebugMode: false,
        },
    },
});

// Prevent multiple instances in development
if (process.env.NODE_ENV === 'development') {
    globalThis.__prisma = prisma;
}

// Database connection health check with timeout
export async function checkDatabaseConnection() {
    try {
        // Add timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Database connection timeout')), 5000);
        });

        const connectionPromise = prisma.$queryRaw`SELECT 1 as test`;

        await Promise.race([connectionPromise, timeoutPromise]);

        return {
            status: 'connected',
            timestamp: new Date().toISOString(),
            message: 'Database connection successful'
        };
    } catch (error) {
        console.error('Database connection error:', error);
        return {
            status: 'error',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        };
    }
}

// Graceful shutdown with proper cleanup
export async function closeDatabaseConnection() {
    try {
        await prisma.$disconnect();
        console.log('Database connection closed successfully');
    } catch (error) {
        console.error('Error closing database connection:', error);
    }
}

// Database transaction wrapper with timeout and error handling
export async function withTransaction<T>(
    fn: (tx: any) => Promise<T>,
    timeoutMs: number = 30000
): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Transaction timeout')), timeoutMs);
    });

    const transactionPromise = prisma.$transaction(async (tx) => {
        try {
            return await fn(tx);
        } catch (error) {
            console.error('Transaction error:', error);
            throw error;
        }
    });

    return Promise.race([transactionPromise, timeoutPromise]);
}

// Handle process termination gracefully
process.on('beforeExit', async () => {
    await closeDatabaseConnection();
});

process.on('SIGINT', async () => {
    await closeDatabaseConnection();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    await closeDatabaseConnection();
    process.exit(0);
});
