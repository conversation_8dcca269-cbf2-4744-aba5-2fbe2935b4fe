import { NextRequest, NextResponse } from 'next/server';
import { prisma } from './db';
import { compare } from 'bcryptjs';

export interface AuthenticatedApp {
    appId: string;
    permissions: string[];
    rateLimits: any;
}

export interface AuthResult {
    success: boolean;
    app?: AuthenticatedApp;
    error?: string;
    statusCode?: number;
}

/**
 * Authenticate an app using API key and app ID
 */
export async function authenticateApp(apiKey: string, appId: string): Promise<AuthResult> {
    try {
        if (!apiKey || !appId) {
            return {
                success: false,
                error: 'API key and App ID are required',
                statusCode: 400
            };
        }

        // Find the API key record
        const apiKeyRecord = await prisma.appApiKey.findFirst({
            where: {
                appId,
                isActive: true,
                expiresAt: {
                    gte: new Date() // Not expired
                }
            },
            include: {
                appRegistration: {
                    select: {
                        status: true,
                        rateLimits: true
                    }
                }
            }
        });

        if (!apiKeyRecord) {
            return {
                success: false,
                error: 'Invalid API key or App ID',
                statusCode: 401
            };
        }

        // Verify the app is approved
        if (apiKeyRecord.appRegistration.status !== 'APPROVED') {
            return {
                success: false,
                error: 'Application not approved',
                statusCode: 403
            };
        }

        // Compare the provided API key with the stored hash
        const isValidKey = await compare(apiKey, apiKeyRecord.apiKeyHash);
        if (!isValidKey) {
            return {
                success: false,
                error: 'Invalid API key',
                statusCode: 401
            };
        }

        // Update last used timestamp
        await prisma.appApiKey.update({
            where: { id: apiKeyRecord.id },
            data: { lastUsedAt: new Date() }
        });

        return {
            success: true,
            app: {
                appId,
                permissions: apiKeyRecord.permissions,
                rateLimits: apiKeyRecord.appRegistration.rateLimits || {}
            }
        };

    } catch (error) {
        console.error('Authentication error:', error);
        return {
            success: false,
            error: 'Authentication failed',
            statusCode: 500
        };
    }
}

/**
 * Check rate limits for an app
 */
export async function checkRateLimit(appId: string, rateLimits: any): Promise<boolean> {
    try {
        if (!rateLimits || Object.keys(rateLimits).length === 0) {
            return true; // No rate limits configured, allow
        }

        const now = new Date();
        const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

        // Check per-minute limit
        if (rateLimits.requestsPerMinute) {
            const minuteCount = await prisma.notification.count({
                where: {
                    appId,
                    createdAt: { gte: oneMinuteAgo }
                }
            });
            if (minuteCount >= rateLimits.requestsPerMinute) {
                return false;
            }
        }

        // Check per-hour limit
        if (rateLimits.requestsPerHour) {
            const hourCount = await prisma.notification.count({
                where: {
                    appId,
                    createdAt: { gte: oneHourAgo }
                }
            });
            if (hourCount >= rateLimits.requestsPerHour) {
                return false;
            }
        }

        // Check per-day limit
        if (rateLimits.requestsPerDay) {
            const dayCount = await prisma.notification.count({
                where: {
                    appId,
                    createdAt: { gte: oneDayAgo }
                }
            });
            if (dayCount >= rateLimits.requestsPerDay) {
                return false;
            }
        }

        return true;
    } catch (error) {
        console.error('Rate limit check error:', error);
        return true; // Allow if rate limit check fails
    }
}

/**
 * Extract API key from request headers or query parameters
 */
export function extractApiKey(request: NextRequest): string | null {
    // Try Authorization header first
    const authHeader = request.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
        return authHeader.replace('Bearer ', '');
    }

    // Try X-API-Key header
    const apiKeyHeader = request.headers.get('x-api-key');
    if (apiKeyHeader) {
        return apiKeyHeader;
    }

    // Try query parameter
    const { searchParams } = new URL(request.url);
    return searchParams.get('apiKey');
}

/**
 * Create an authentication middleware function
 */
export function createAuthMiddleware() {
    return async (request: NextRequest, appId: string): Promise<AuthResult> => {
        const apiKey = extractApiKey(request);

        if (!apiKey) {
            return {
                success: false,
                error: 'API key is required',
                statusCode: 400
            };
        }

        return await authenticateApp(apiKey, appId);
    };
}

/**
 * Helper function to create error response
 */
export function createAuthErrorResponse(authResult: AuthResult): NextResponse {
    return NextResponse.json(
        {
            success: false,
            error: authResult.error
        },
        { status: authResult.statusCode || 401 }
    );
} 