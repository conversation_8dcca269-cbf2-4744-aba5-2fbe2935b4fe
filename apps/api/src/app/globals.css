@import "tailwindcss";

/* Custom styles for documentation */
@reference {
  .docs-content h1 {
    @apply text-3xl font-bold text-gray-900 mb-4;
  }

  .docs-content h2 {
    @apply text-2xl font-semibold text-gray-900 mb-4;
  }

  .docs-content h3 {
    @apply text-lg font-medium text-gray-900 mb-3;
  }

  .docs-content p {
    @apply text-gray-600 mb-3;
  }

  .docs-content code {
    @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono;
  }

  .docs-content pre {
    @apply bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm overflow-x-auto;
  }

  .docs-content ul {
    @apply list-disc list-inside space-y-2;
  }

  .docs-content li {
    @apply text-gray-600;
  }
}

