import { NextResponse } from 'next/server';
import { checkDatabaseConnection } from '@/lib/db';

// Memory usage monitoring
function getMemoryUsage() {
    const usage = process.memoryUsage();
    return {
        rss: `${Math.round(usage.rss / 1024 / 1024)} MB`,
        heapTotal: `${Math.round(usage.heapTotal / 1024 / 1024)} MB`,
        heapUsed: `${Math.round(usage.heapUsed / 1024 / 1024)} MB`,
        external: `${Math.round(usage.external / 1024 / 1024)} MB`,
        arrayBuffers: `${Math.round(usage.arrayBuffers / 1024 / 1024)} MB`,
    };
}

// System resource monitoring
function getSystemResources() {
    const startTime = process.uptime();
    const cpuUsage = process.cpuUsage();

    return {
        uptime: `${Math.round(startTime)}s`,
        cpu: {
            user: `${Math.round(cpuUsage.user / 1000)}ms`,
            system: `${Math.round(cpuUsage.system / 1000)}ms`,
        },
        platform: process.platform,
        nodeVersion: process.version,
        pid: process.pid,
    };
}

// Check if system is under stress
function isSystemHealthy() {
    const usage = process.memoryUsage();
    const memoryUsageMB = usage.heapUsed / 1024 / 1024;

    // Warning if memory usage exceeds 80% of available heap
    const heapTotalMB = usage.heapTotal / 1024 / 1024;
    const memoryPercentage = (memoryUsageMB / heapTotalMB) * 100;

    return {
        healthy: memoryPercentage < 80,
        memoryPercentage: Math.round(memoryPercentage),
        warning: memoryPercentage > 70 ? 'High memory usage detected' : null,
    };
}

export async function GET() {
    try {
        const startTime = Date.now();

        // Check database connection
        const dbStatus = await checkDatabaseConnection();

        // Get system metrics
        const memoryUsage = getMemoryUsage();
        const systemResources = getSystemResources();
        const systemHealth = isSystemHealthy();

        // Calculate response time
        const responseTime = Date.now() - startTime;

        // Determine overall health status
        const isHealthy = dbStatus.status === 'connected' && systemHealth.healthy;

        // Check external service connectivity
        const externalServices = await checkExternalServices();

        const healthData = {
            status: isHealthy ? 'healthy' : 'unhealthy',
            timestamp: new Date().toISOString(),
            responseTime: `${responseTime}ms`,

            database: dbStatus,
            system: {
                memory: memoryUsage,
                resources: systemResources,
                health: systemHealth,
            },

            // Environment information
            environment: {
                nodeEnv: process.env.NODE_ENV || 'development',
                port: process.env.PORT || '3000',
                databaseUrl: process.env.DATABASE_URL ? 'configured' : 'not configured',
                novuApiKey: process.env.NOVU_API_KEY ? 'configured' : 'not configured',
            },

            // Performance warnings
            warnings: [
                ...(systemHealth.warning ? [systemHealth.warning] : []),
                ...(dbStatus.status !== 'connected' ? ['Database connection issue'] : []),
                ...(responseTime > 1000 ? ['Slow response time'] : []),
            ],

            // External services
            externalServices,
        };

        // Return appropriate status code
        const statusCode = isHealthy ? 200 : 503;

        return NextResponse.json(healthData, {
            status: statusCode,
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0',
            }
        });

    } catch (error) {
        console.error('Health check error:', error);

        return NextResponse.json({
            status: 'error',
            timestamp: new Date().toISOString(),
            error: error instanceof Error ? error.message : 'Unknown error',
            system: {
                memory: getMemoryUsage(),
                resources: getSystemResources(),
            },
        }, {
            status: 500,
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
            }
        });
    }
}

// HEAD /api/health - Lightweight health check (no response body)
export async function HEAD() {
    return new NextResponse(null, { status: 200 });
}


async function checkExternalServices() {
    const services: Record<string, any> = {};

    // Check Novu connectivity
    if (process.env.NOVU_API_KEY) {
        try {
            // Simple connectivity check - in production, you might want to make a real API call
            services.novu = {
                status: 'connected',
                apiKey: process.env.NOVU_API_KEY ? 'configured' : 'not configured',
            };
        } catch (error) {
            services.novu = {
                status: 'error',
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    } else {
        services.novu = {
            status: 'not configured',
        };
    }

    // Check Resend connectivity (if configured)
    if (process.env.RESEND_API_KEY) {
        try {
            services.resend = {
                status: 'connected',
                apiKey: 'configured',
            };
        } catch (error) {
            services.resend = {
                status: 'error',
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    } else {
        services.resend = {
            status: 'not configured',
        };
    }

    // Check Twilio connectivity (if configured)
    if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
        try {
            services.twilio = {
                status: 'connected',
                accountSid: 'configured',
                authToken: 'configured',
            };
        } catch (error) {
            services.twilio = {
                status: 'error',
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    } else {
        services.twilio = {
            status: 'not configured',
        };
    }

    return services;
}
