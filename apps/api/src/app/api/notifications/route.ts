import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { hash, compare } from 'bcryptjs';
import { NotificationService } from '@/app/services/notification.service';

// Initialize notification service
const notificationService = new NotificationService();

// Helper function to validate API key
async function validateApiKey(apiKey: string, appId: string): Promise<boolean> {
    try {
        const apiKeyRecord = await prisma.appApiKey.findFirst({
            where: {
                appId,
                isActive: true,
                expiresAt: {
                    gte: new Date() // Not expired
                }
            }
        });

        if (!apiKeyRecord) {
            return false;
        }

        // Compare the provided API key with the stored hash
        return await compare(apiKey, apiKeyRecord.apiKeyHash);
    } catch (error) {
        console.error('Error validating API key:', error);
        return false;
    }
}

// Helper function to check rate limits
async function checkRateLimit(appId: string): Promise<boolean> {
    try {
        // Get the app's rate limits
        const appRegistration = await prisma.appRegistration.findUnique({
            where: { appId },
            select: { rateLimits: true }
        });

        if (!appRegistration?.rateLimits) {
            return true; // No rate limits configured, allow
        }

        const rateLimits = appRegistration.rateLimits as any;
        const now = new Date();
        const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

        // Check per-minute limit
        if (rateLimits.requestsPerMinute) {
            const minuteCount = await prisma.notification.count({
                where: {
                    appId,
                    createdAt: { gte: oneMinuteAgo }
                }
            });
            if (minuteCount >= rateLimits.requestsPerMinute) {
                return false;
            }
        }

        // Check per-hour limit
        if (rateLimits.requestsPerHour) {
            const hourCount = await prisma.notification.count({
                where: {
                    appId,
                    createdAt: { gte: oneHourAgo }
                }
            });
            if (hourCount >= rateLimits.requestsPerHour) {
                return false;
            }
        }

        // Check per-day limit
        if (rateLimits.requestsPerDay) {
            const dayCount = await prisma.notification.count({
                where: {
                    appId,
                    createdAt: { gte: oneDayAgo }
                }
            });
            if (dayCount >= rateLimits.requestsPerDay) {
                return false;
            }
        }

        return true;
    } catch (error) {
        console.error('Error checking rate limits:', error);
        return true; // Allow if rate limit check fails
    }
}

// Helper function to send notification via Novu
async function sendViaNovu(notification: any, channel: string, application: any) {
    try {
        if (!process.env.NOVU_API_KEY) {
            throw new Error('Novu API key not configured');
        }

        // Update delivery status to SENT
        await prisma.deliveryStatus.updateMany({
            where: {
                notificationId: notification.id,
                channel
            },
            data: {
                status: 'SENT',
                providerMessageId: `novu_${Date.now()}`,
                updatedAt: new Date()
            }
        });

        console.log(`Notification sent via Novu: ${notification.id}`);
    } catch (error) {
        throw error;
    }
}

// Helper function to send notification via Resend
async function sendViaResend(notification: any, channel: string, application: any) {
    try {
        if (!process.env.RESEND_API_KEY) {
            throw new Error('Resend API key not configured');
        }

        // Get recipient email (this would need to be implemented based on your user model)
        const recipient = await prisma.user.findUnique({
            where: { id: notification.recipientId },
            select: { email: true }
        });

        if (!recipient?.email) {
            throw new Error('Recipient email not found');
        }

        // Send email via Resend
        const emailData = {
            from: process.env.RESEND_FROM_EMAIL || '<EMAIL>',
            to: recipient.email,
            subject: notification.title,
            html: `
                <h2>${notification.title}</h2>
                <p>${notification.message}</p>
                <p><strong>Type:</strong> ${notification.type}</p>
                <p><strong>Priority:</strong> ${notification.priority}</p>
                <p><small>Sent at: ${new Date().toLocaleString()}</small></p>
            `,
            text: `
                ${notification.title}
                
                ${notification.message}
                
                Type: ${notification.type}
                Priority: ${notification.priority}
                Sent at: ${new Date().toLocaleString()}
            `
        };

        const response = await fetch('https://api.resend.com/emails', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${process.env.RESEND_API_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(emailData)
        });

        if (!response.ok) {
            throw new Error(`Resend API error: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        // Update delivery status to SENT
        await prisma.deliveryStatus.updateMany({
            where: {
                notificationId: notification.id,
                channel
            },
            data: {
                status: 'SENT',
                providerMessageId: result.id || `resend_${Date.now()}`,
                providerResponse: result,
                updatedAt: new Date()
            }
        });

        console.log(`Notification sent via Resend: ${notification.id}`);
    } catch (error) {
        throw error;
    }
}

// Helper function to send notification via Twilio
async function sendViaTwilio(notification: any, channel: string, application: any) {
    try {
        if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN) {
            throw new Error('Twilio credentials not configured');
        }

        // Get recipient phone number (this would need to be implemented based on your user model)
        const recipient = await prisma.user.findUnique({
            where: { id: notification.recipientId },
            select: { phone: true } // Assuming you have a phone field
        });

        if (!recipient?.phone) {
            throw new Error('Recipient phone number not found');
        }

        // Send SMS via Twilio
        const auth = Buffer.from(`${process.env.TWILIO_ACCOUNT_SID}:${process.env.TWILIO_AUTH_TOKEN}`).toString('base64');

        const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${process.env.TWILIO_ACCOUNT_SID}/Messages.json`, {
            method: 'POST',
            headers: {
                'Authorization': `Basic ${auth}`,
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams({
                To: recipient.phone,
                From: process.env.TWILIO_PHONE_NUMBER || '+**********', // You'd need to add this to env
                Body: `${notification.title}: ${notification.message}`
            })
        });

        if (!response.ok) {
            throw new Error(`Twilio API error: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        // Update delivery status to SENT
        await prisma.deliveryStatus.updateMany({
            where: {
                notificationId: notification.id,
                channel
            },
            data: {
                status: 'SENT',
                providerMessageId: result.sid || `twilio_${Date.now()}`,
                providerResponse: result,
                updatedAt: new Date()
            }
        });

        console.log(`Notification sent via Twilio: ${notification.id}`);
    } catch (error) {
        throw error;
    }
}

// Helper function to send notification via direct provider
async function sendViaDirectProvider(notification: any, channel: string, application: any) {
    try {
        // For in-app notifications or other direct methods
        // This could integrate with your frontend notification system

        // Update delivery status to SENT
        await prisma.deliveryStatus.updateMany({
            where: {
                notificationId: notification.id,
                channel
            },
            data: {
                status: 'SENT',
                providerMessageId: `direct_${Date.now()}`,
                updatedAt: new Date()
            }
        });

        console.log(`Notification sent via direct provider: ${notification.id}`);
    } catch (error) {
        throw error;
    }
}

// GET /api/notifications - Get notifications (requires API key)
export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const apiKey = searchParams.get('apiKey');
        const appId = searchParams.get('appId');
        const userId = searchParams.get('userId');
        const limit = parseInt(searchParams.get('limit') || '50');
        const offset = parseInt(searchParams.get('offset') || '0');

        if (!apiKey || !appId) {
            return NextResponse.json(
                { success: false, error: 'API key and App ID are required' },
                { status: 400 }
            );
        }

        // Validate API key
        const isValidKey = await validateApiKey(apiKey, appId);
        if (!isValidKey) {
            return NextResponse.json(
                { success: false, error: 'Invalid API key' },
                { status: 401 }
            );
        }

        // Check rate limits
        const withinRateLimit = await checkRateLimit(appId);
        if (!withinRateLimit) {
            return NextResponse.json(
                { success: false, error: 'Rate limit exceeded' },
                { status: 429 }
            );
        }

        // Build query
        const where: any = { appId };
        if (userId) {
            where.recipientId = userId;
        }

        // Get notifications
        const notifications = await prisma.notification.findMany({
            where,
            orderBy: { createdAt: 'desc' },
            take: Math.min(limit, 100), // Cap at 100
            skip: offset,
            select: {
                id: true,
                title: true,
                message: true,
                type: true,
                priority: true,
                status: true,
                recipientId: true,
                createdAt: true,
                readAt: true
            }
        });

        // Get total count for pagination
        const total = await prisma.notification.count({ where });

        return NextResponse.json({
            success: true,
            data: notifications,
            pagination: {
                total,
                limit,
                offset,
                hasMore: offset + limit < total
            }
        });

    } catch (error) {
        console.error('Error fetching notifications:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// POST /api/notifications - Create notification (requires API key)
export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        const {
            appId,
            userId,
            title,
            message,
            type = 'INFO',
            priority = 'NORMAL',
            channels = { inApp: true },
            metadata = {},
            scheduledFor,
            expiresAt
        } = body;

        // Validate required fields
        if (!appId || !userId || !title || !message) {
            return NextResponse.json(
                { success: false, error: 'App ID, user ID, title, and message are required' },
                { status: 400 }
            );
        }

        // Extract API key from headers
        const authHeader = request.headers.get('authorization');
        const apiKey = authHeader?.replace('Bearer ', '');

        if (!apiKey) {
            return NextResponse.json(
                { success: false, error: 'API key is required in Authorization header' },
                { status: 400 }
            );
        }

        // Validate API key
        const isValidKey = await validateApiKey(apiKey, appId);
        if (!isValidKey) {
            return NextResponse.json(
                { success: false, error: 'Invalid API key' },
                { status: 401 }
            );
        }

        // Check rate limits
        const withinRateLimit = await checkRateLimit(appId);
        if (!withinRateLimit) {
            return NextResponse.json(
                { success: false, error: 'Rate limit exceeded' },
                { status: 429 }
            );
        }

        // Verify app is approved
        const appRegistration = await prisma.appRegistration.findUnique({
            where: { appId },
            select: { status: true }
        });

        if (!appRegistration || appRegistration.status !== 'APPROVED') {
            return NextResponse.json(
                { success: false, error: 'Application not approved or not found' },
                { status: 403 }
            );
        }

        // Create notification
        const notification = await prisma.notification.create({
            data: {
                title,
                message,
                type,
                priority,
                recipientId: userId,
                appId,
                metadata,
                scheduledFor: scheduledFor ? new Date(scheduledFor) : null,
                expiresAt: expiresAt ? new Date(expiresAt) : null,
                status: 'PENDING'
            }
        });

        // Process notification delivery through the notification service
        try {
            // Get the application configuration to determine delivery channels
            const application = await prisma.application.findUnique({
                where: { appId },
                select: {
                    defaultProvider: true,
                    novuAppId: true,
                    resendApiKey: true,
                    twilioAccountSid: true,
                    fallbackStrategy: true,
                    maxRetries: true,
                    retryDelay: true
                }
            });

            if (application) {
                // Determine delivery channels based on app configuration
                const channels: string[] = [];

                if (application.novuAppId) {
                    channels.push('novu');
                }
                if (application.resendApiKey) {
                    channels.push('email');
                }
                if (application.twilioAccountSid) {
                    channels.push('sms');
                }

                // If no specific channels configured, use default
                if (channels.length === 0) {
                    channels.push('inApp'); // Default to in-app notifications
                }

                // Create delivery status records for each channel
                const deliveryRecords = channels.map(channel => ({
                    notificationId: notification.id,
                    channel,
                    provider: channel === 'novu' ? 'novu' :
                        channel === 'email' ? 'resend' :
                            channel === 'sms' ? 'twilio' : 'direct',
                    status: 'PENDING',
                    attempts: 0,
                    createdAt: new Date(),
                    updatedAt: new Date()
                }));

                await prisma.deliveryStatus.createMany({
                    data: deliveryRecords
                });

                // Process immediate delivery for non-scheduled notifications
                if (!scheduledFor || new Date(scheduledFor) <= new Date()) {
                    // Send via configured providers
                    for (const channel of channels) {
                        try {
                            if (channel === 'novu' && application.novuAppId) {
                                // Send via Novu
                                await sendViaNovu(notification, channel, application);
                            } else if (channel === 'email' && application.resendApiKey) {
                                // Send via Resend
                                await sendViaResend(notification, channel, application);
                            } else if (channel === 'sms' && application.twilioAccountSid) {
                                // Send via Twilio
                                await sendViaTwilio(notification, channel, application);
                            } else {
                                // Send via direct provider
                                await sendViaDirectProvider(notification, channel, application);
                            }
                        } catch (error) {
                            console.error(`Failed to send notification via ${channel}:`, error);
                            // Update delivery status to failed
                            await prisma.deliveryStatus.updateMany({
                                where: {
                                    notificationId: notification.id,
                                    channel
                                },
                                data: {
                                    status: 'FAILED',
                                    errorMessage: error instanceof Error ? error.message : 'Unknown error',
                                    updatedAt: new Date()
                                }
                            });
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error processing notification delivery:', error);
            // Don't fail the notification creation if delivery fails
            // The notification is still created and can be retried later
        }

        return NextResponse.json({
            success: true,
            data: {
                id: notification.id,
                status: notification.status,
                message: 'Notification created successfully'
            }
        }, { status: 201 });

    } catch (error) {
        console.error('Error creating notification:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// PUT /api/notifications/:id - Update notification (requires API key)
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
    try {
        const { id } = params;
        const body = await request.json();
        const { apiKey, appId, ...updateData } = body;

        if (!apiKey || !appId) {
            return NextResponse.json(
                { success: false, error: 'API key and App ID are required' },
                { status: 400 }
            );
        }

        // Validate API key
        const isValidKey = await validateApiKey(apiKey, appId);
        if (!isValidKey) {
            return NextResponse.json(
                { success: false, error: 'Invalid API key' },
                { status: 401 }
            );
        }

        // Get the notification to verify ownership
        const notification = await prisma.notification.findUnique({
            where: { id },
            select: { appId: true }
        });

        if (!notification) {
            return NextResponse.json(
                { success: false, error: 'Notification not found' },
                { status: 404 }
            );
        }

        if (notification.appId !== appId) {
            return NextResponse.json(
                { success: false, error: 'Unauthorized to modify this notification' },
                { status: 403 }
            );
        }

        // Update the notification
        const updatedNotification = await prisma.notification.update({
            where: { id },
            data: updateData
        });

        return NextResponse.json({
            success: true,
            data: updatedNotification
        });

    } catch (error) {
        console.error('Error updating notification:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// DELETE /api/notifications/:id - Delete notification (requires API key)
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
    try {
        const { id } = params;
        const { searchParams } = new URL(request.url);
        const apiKey = searchParams.get('apiKey');
        const appId = searchParams.get('appId');

        if (!apiKey || !appId) {
            return NextResponse.json(
                { success: false, error: 'API key and App ID are required' },
                { status: 400 }
            );
        }

        // Validate API key
        const isValidKey = await validateApiKey(apiKey, appId);
        if (!isValidKey) {
            return NextResponse.json(
                { success: false, error: 'Invalid API key' },
                { status: 401 }
            );
        }

        // Get the notification to verify ownership
        const notification = await prisma.notification.findUnique({
            where: { id },
            select: { appId: true }
        });

        if (!notification) {
            return NextResponse.json(
                { success: false, error: 'Notification not found' },
                { status: 404 }
            );
        }

        if (notification.appId !== appId) {
            return NextResponse.json(
                { success: false, error: 'Unauthorized to delete this notification' },
                { status: 403 }
            );
        }

        // Delete the notification
        await prisma.notification.delete({
            where: { id }
        });

        return NextResponse.json({
            success: true,
            message: 'Notification deleted successfully'
        });

    } catch (error) {
        console.error('Error deleting notification:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
}
