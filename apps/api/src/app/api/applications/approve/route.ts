import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { hash } from 'bcryptjs';

// Helper function to generate secure API key
function generateSecureApiKey(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    return `spark_${timestamp}_${random}`;
}

// Helper function to send admin notification
async function sendAdminNotification(appData: any, action: string, reason?: string) {
    try {
        const notificationData = {
            type: action === 'APPROVE' ? 'APP_APPROVED' : 'APP_REJECTED',
            appId: appData.appId,
            appName: appData.name,
            contactEmail: appData.contactEmail,
            contactName: appData.contactName,
            website: appData.website,
            businessType: appData.businessType,
            expectedVolume: appData.expectedVolume,
            message: action === 'APPROVE'
                ? `Application ${appData.name} has been approved`
                : `Application ${appData.name} has been rejected. Reason: ${reason || 'No reason provided'}`,
            timestamp: new Date()
        };

        // Try to send via Novu first (if configured)
        if (process.env.NOVU_API_KEY && process.env.NOVU_APP_ID) {
            try {
                const novuPayload = {
                    name: 'admin-notification',
                    to: {
                        subscriberId: 'admin',
                        email: process.env.ADMIN_EMAIL || '<EMAIL>',
                        firstName: 'Admin'
                    },
                    payload: {
                        type: notificationData.type,
                        appId: notificationData.appId,
                        appName: notificationData.appName,
                        contactEmail: notificationData.contactEmail,
                        contactName: notificationData.contactName,
                        businessType: notificationData.businessType,
                        expectedVolume: notificationData.expectedVolume,
                        message: notificationData.message,
                        actionUrl: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/admin/applications/${notificationData.appId}`,
                        priority: 'NORMAL'
                    }
                };

                const response = await fetch(`https://api.novu.co/v1/triggers/${novuPayload.name}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `ApiKey ${process.env.NOVU_API_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(novuPayload)
                });

                if (response.ok) {
                    console.log(`Admin notification sent via Novu for ${action.toLowerCase()}`);
                    return;
                }
            } catch (error) {
                console.warn('Failed to send Novu notification, falling back to email:', error);
            }
        }

        // Fallback to Resend email
        if (process.env.RESEND_API_KEY) {
            try {
                const emailData = {
                    from: process.env.RESEND_FROM_EMAIL || '<EMAIL>',
                    to: process.env.ADMIN_EMAIL || '<EMAIL>',
                    subject: action === 'APPROVE'
                        ? `✅ App Approved: ${appData.name}`
                        : `❌ App Rejected: ${appData.name}`,
                    html: generateAdminEmailHTML(notificationData),
                    text: generateAdminEmailText(notificationData)
                };

                const response = await fetch('https://api.resend.com/emails', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${process.env.RESEND_API_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(emailData)
                });

                if (response.ok) {
                    console.log(`Admin notification sent via Resend for ${action.toLowerCase()}`);
                    return;
                }
            } catch (error) {
                console.warn('Failed to send Resend notification:', error);
            }
        }

        // Last resort: console log
        console.log('=== ADMIN NOTIFICATION ===');
        console.log(`Application ${action.toLowerCase()}: ${appData.name} (${appData.appId})`);
        console.log(`Contact: ${appData.contactName} at ${appData.contactEmail}`);
        if (action === 'REJECT' && reason) {
            console.log(`Reason: ${reason}`);
        }
        console.log('==========================');

    } catch (error) {
        console.error('Failed to send admin notification:', error);
    }
}

// Helper function to generate HTML email content
function generateAdminEmailHTML(notification: any): string {
    const isApproved = notification.type === 'APP_APPROVED';
    const statusColor = isApproved ? '#10b981' : '#ef4444';
    const statusIcon = isApproved ? '✅' : '❌';
    const statusText = isApproved ? 'APPROVED' : 'REJECTED';

    return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Application ${statusText}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: ${statusColor}; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
                .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
                .status { display: inline-block; background: ${statusColor}; color: white; padding: 8px 16px; border-radius: 20px; font-weight: bold; }
                .app-info { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid ${statusColor}; }
                .contact-info { background: #e8f4fd; padding: 15px; margin: 15px 0; border-radius: 5px; }
                .action-btn { display: inline-block; background: ${statusColor}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin-top: 15px; }
                .footer { text-align: center; margin-top: 30px; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>${statusIcon} Application ${statusText}</h1>
                    <div class="status">${statusText}</div>
                </div>
                
                <div class="content">
                    <h2>Application Details</h2>
                    <div class="app-info">
                        <strong>App Name:</strong> ${notification.appName}<br>
                        <strong>App ID:</strong> ${notification.appId}<br>
                        <strong>Business Type:</strong> ${notification.businessType || 'Not specified'}<br>
                        <strong>Expected Volume:</strong> ${notification.expectedVolume || 'Not specified'} notifications/month
                    </div>
                    
                    <h3>Contact Information</h3>
                    <div class="contact-info">
                        <strong>Name:</strong> ${notification.contactName}<br>
                        <strong>Email:</strong> ${notification.contactEmail}<br>
                        ${notification.website ? `<strong>Website:</strong> ${notification.website}<br>` : ''}
                    </div>
                    
                    <p><strong>Status:</strong> ${notification.message}</p>
                    
                    <a href="${notification.actionUrl}" class="action-btn">
                        View Application
                    </a>
                </div>
                
                <div class="footer">
                    <p>This is an automated notification from your Notification System.</p>
                    <p>Application has been ${notification.type === 'APP_APPROVED' ? 'approved and is now active' : 'rejected'}.</p>
                </div>
            </div>
        </body>
        </html>
    `;
}

// Helper function to generate plain text email content
function generateAdminEmailText(notification: any): string {
    const isApproved = notification.type === 'APP_APPROVED';
    const statusText = isApproved ? 'APPROVED' : 'REJECTED';

    return `
Application ${statusText}

Application Details:
- App Name: ${notification.appName}
- App ID: ${notification.appId}
- Business Type: ${notification.businessType || 'Not specified'}
- Expected Volume: ${notification.expectedVolume || 'Not specified'} notifications/month

Contact Information:
- Name: ${notification.contactName}
- Email: ${notification.contactEmail}
${notification.website ? `- Website: ${notification.website}` : ''}

Status: ${notification.message}

View application at: ${notification.actionUrl}

---
This is an automated notification from your Notification System.
Application has been ${isApproved ? 'approved and is now active' : 'rejected'}.
    `.trim();
}

// POST /api/applications/approve - Approve or reject application registration (admin only)
export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        const {
            appId,
            action, // 'APPROVE' or 'REJECT'
            reason,
            adminNotes,
            adminSecret
        } = body;

        if (!appId || !action || !adminSecret) {
            return NextResponse.json(
                { success: false, error: 'App ID, action, and admin secret are required' },
                { status: 400 }
            );
        }

        // Verify admin secret (in production, use proper admin authentication)
        if (adminSecret !== process.env.ADMIN_SECRET) {
            return NextResponse.json(
                { success: false, error: 'Unauthorized' },
                { status: 401 }
            );
        }

        // Find the pending registration
        const appRegistration = await prisma.appRegistration.findUnique({
            where: { appId }
        });

        if (!appRegistration) {
            return NextResponse.json(
                { success: false, error: 'Application registration not found' },
                { status: 404 }
            );
        }

        if (appRegistration.status !== 'PENDING_APPROVAL') {
            return NextResponse.json(
                { success: false, error: 'Application is not pending approval' },
                { status: 400 }
            );
        }

        if (action === 'APPROVE') {
            // Generate a secure API key
            const apiKey = generateSecureApiKey();
            const hashedApiKey = await hash(apiKey, 12);

            // Create the API key record
            await prisma.appApiKey.create({
                data: {
                    appId,
                    apiKeyHash: hashedApiKey,
                    name: 'Primary API Key',
                    permissions: ['send_notifications', 'read_notifications', 'manage_preferences'],
                    isActive: true
                }
            });

            // Create the application in the main applications table
            await prisma.application.create({
                data: {
                    appId,
                    name: appRegistration.name,
                    description: appRegistration.description,
                    webhookUrl: null,
                    novuAppId: appRegistration.novuAppId,
                    novuApiKey: appRegistration.novuApiKey,
                    resendApiKey: appRegistration.resendApiKey,
                    twilioAccountSid: appRegistration.twilioAccountSid,
                    twilioAuthToken: appRegistration.twilioAuthToken,
                    defaultProvider: 'novu',
                    fallbackStrategy: appRegistration.fallbackStrategy,
                    maxRetries: appRegistration.maxRetries,
                    retryDelay: appRegistration.retryDelay,
                    rateLimits: appRegistration.rateLimits,
                    isActive: true,
                    metadata: {
                        approvedAt: new Date().toISOString(),
                        approvedBy: 'admin',
                        adminNotes: adminNotes || 'Approved by admin'
                    }
                }
            });

            // Update the app registration status
            await prisma.appRegistration.update({
                where: { appId },
                data: {
                    status: 'APPROVED',
                    adminNotes: adminNotes || 'Approved by admin',
                    approvedAt: new Date(),
                    approvedBy: 'admin'
                }
            });

            // Send admin notification about approval
            await sendAdminNotification(appRegistration, 'APPROVE', adminNotes);

            return NextResponse.json({
                success: true,
                appId,
                status: 'APPROVED',
                apiKey,
                message: 'Application approved and registered successfully'
            });

        } else if (action === 'REJECT') {
            // Update the app registration status to rejected
            await prisma.appRegistration.update({
                where: { appId },
                data: {
                    status: 'REJECTED',
                    adminNotes: reason || 'Rejected by admin'
                }
            });

            // Send admin notification about rejection
            await sendAdminNotification(appRegistration, 'REJECT', reason);

            return NextResponse.json({
                success: true,
                appId,
                status: 'REJECTED',
                message: `Application rejected. Reason: ${reason || 'No reason provided'}`
            });

        } else {
            return NextResponse.json(
                { success: false, error: 'Invalid action. Must be APPROVE or REJECT' },
                { status: 400 }
            );
        }

    } catch (error) {
        console.error('Error processing application approval:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// GET /api/applications/approve - Get pending registrations (admin only)
export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const adminSecret = searchParams.get('adminSecret');

        if (!adminSecret) {
            return NextResponse.json(
                { success: false, error: 'Admin secret is required' },
                { status: 400 }
            );
        }

        // Verify admin secret
        if (adminSecret !== process.env.ADMIN_SECRET) {
            return NextResponse.json(
                { success: false, error: 'Unauthorized' },
                { status: 401 }
            );
        }

        // Get all pending registrations
        const pendingRegistrations = await prisma.appRegistration.findMany({
            where: { status: 'PENDING_APPROVAL' },
            orderBy: { createdAt: 'asc' },
            select: {
                id: true,
                appId: true,
                name: true,
                description: true,
                contactEmail: true,
                contactName: true,
                website: true,
                businessType: true,
                expectedVolume: true,
                status: true,
                createdAt: true,
                updatedAt: true
            }
        });

        return NextResponse.json({
            success: true,
            data: pendingRegistrations
        });

    } catch (error) {
        console.error('Error fetching pending registrations:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
} 