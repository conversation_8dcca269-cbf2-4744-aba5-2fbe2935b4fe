import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/applications/status - Check application registration status
export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const appId = searchParams.get('appId');

        if (!appId) {
            return NextResponse.json(
                { success: false, error: 'App ID is required' },
                { status: 400 }
            );
        }

        // Check if application exists in registrations table
        const appRegistration = await prisma.appRegistration.findUnique({
            where: { appId },
            select: {
                appId: true,
                name: true,
                status: true,
                adminNotes: true,
                approvedAt: true,
                approvedBy: true,
                createdAt: true
            }
        });

        if (!appRegistration) {
            return NextResponse.json(
                { success: false, error: 'Application not found' },
                { status: 404 }
            );
        }

        // If approved, check if it exists in applications table and get API key
        if (appRegistration.status === 'APPROVED') {
            const application = await prisma.application.findUnique({
                where: { appId },
                select: {
                    appId: true,
                    name: true,
                    isActive: true
                }
            });

            if (!application || !application.isActive) {
                return NextResponse.json(
                    { success: false, error: 'Application was approved but is not active' },
                    { status: 500 }
                );
            }

            // Get the primary API key for this application
            const apiKey = await prisma.appApiKey.findFirst({
                where: {
                    appId,
                    isActive: true,
                    name: 'Primary API Key'
                },
                select: {
                    id: true,
                    name: true,
                    isActive: true,
                    createdAt: true
                }
            });

            if (!apiKey) {
                return NextResponse.json(
                    { success: false, error: 'Application approved but no API key found' },
                    { status: 500 }
                );
            }

            return NextResponse.json({
                success: true,
                data: {
                    appId: appRegistration.appId,
                    name: appRegistration.name,
                    status: 'APPROVED',
                    message: 'Application has been approved and is now active',
                    approvedAt: appRegistration.approvedAt,
                    approvedBy: appRegistration.approvedBy,
                    apiKey: `spark_${apiKey.id}`, // Generate a readable API key format
                    apiKeyCreatedAt: apiKey.createdAt
                }
            });
        }

        // If rejected
        if (appRegistration.status === 'REJECTED') {
            return NextResponse.json({
                success: true,
                data: {
                    appId: appRegistration.appId,
                    name: appRegistration.name,
                    status: 'REJECTED',
                    message: 'Application has been rejected',
                    adminNotes: appRegistration.adminNotes,
                    createdAt: appRegistration.createdAt
                }
            });
        }

        // If still pending
        if (appRegistration.status === 'PENDING_APPROVAL') {
            return NextResponse.json({
                success: true,
                data: {
                    appId: appRegistration.appId,
                    name: appRegistration.name,
                    status: 'PENDING_APPROVAL',
                    message: 'Application is pending approval',
                    createdAt: appRegistration.createdAt,
                    estimatedReviewTime: '24-48 hours'
                }
            });
        }

        // Unknown status
        return NextResponse.json({
            success: true,
            data: {
                appId: appRegistration.appId,
                name: appRegistration.name,
                status: appRegistration.status,
                message: 'Application status is unknown',
                createdAt: appRegistration.createdAt
            }
        });

    } catch (error) {
        console.error('Error checking application status:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
} 