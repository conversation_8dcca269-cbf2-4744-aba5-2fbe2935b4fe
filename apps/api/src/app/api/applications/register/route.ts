import { NextRequest, NextResponse } from "next/server";
import { applicationService, ApiResponse } from "@/services";
import { CreateAppRegistrationSchema } from "@/validations";
import { CreateAppRegistration } from "@/types";

/**
 * Register a new application 
 */
export async function POST(request: NextRequest) {
    try {
        const body: CreateAppRegistration = await request.json();
        const validateBody = ApiResponse.zodRunner(CreateAppRegistrationSchema, body, 'application');
        if(!validateBody.isSuccess()) {
            return validateBody.send();
        }
        const result = await applicationService.createAppRegistration(validateBody.getData()!);
        return ApiResponse.fromResult(result).send();
    } catch(error: any){
        return ApiResponse.serverError('Internal server error').send();
    }
}