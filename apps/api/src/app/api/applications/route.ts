import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { hash, compare } from 'bcryptjs';

// Helper function to hash API keys
async function hashApiKey(apiKey: string): Promise<string> {
    return await hash(api<PERSON><PERSON>, 12);
}

// Helper function to validate API key
async function validateApiKey(apiKey: string, appId: string): Promise<boolean> {
    try {
        const apiKeyRecord = await prisma.appApiKey.findFirst({
            where: {
                appId,
                isActive: true,
                expiresAt: {
                    gte: new Date() // Not expired
                }
            }
        });

        if (!apiKeyRecord) {
            return false;
        }

        // Compare the provided API key with the stored hash
        return await compare(apiKey, apiKeyRecord.apiKeyHash);
    } catch (error) {
        console.error('Error validating API key:', error);
        return false;
    }
}

// Helper function to send admin notification
async function sendAdminNotification(appData: any) {
    try {
        // Use the notification system's own infrastructure to notify admins
        const notificationData = {
            type: 'APP_REGISTRATION_REQUEST',
            appId: appData.appId,
            appName: appData.name,
            contactEmail: appData.contactEmail,
            contactName: appData.contactName,
            website: appData.website,
            businessType: appData.businessType,
            expectedVolume: appData.expectedVolume,
            message: `New application registration request from ${appData.name} (${appData.appId})`,
            timestamp: new Date()
        };

        // Try to send via Novu first (if configured)
        if (process.env.NOVU_API_KEY && process.env.NOVU_APP_ID) {
            try {
                const novuPayload = {
                    name: 'admin-notification',
                    to: {
                        subscriberId: 'admin',
                        email: process.env.ADMIN_EMAIL || '<EMAIL>',
                        firstName: 'Admin'
                    },
                    payload: {
                        type: notificationData.type,
                        appId: notificationData.appId,
                        appName: notificationData.appName,
                        contactEmail: notificationData.contactEmail,
                        contactName: notificationData.contactName,
                        businessType: notificationData.businessType,
                        expectedVolume: notificationData.expectedVolume,
                        message: notificationData.message,
                        actionUrl: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/admin/applications/${notificationData.appId}/review`,
                        priority: 'HIGH'
                    }
                };

                const response = await fetch(`https://api.novu.co/v1/triggers/${novuPayload.name}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `ApiKey ${process.env.NOVU_API_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(novuPayload)
                });

                if (response.ok) {
                    console.log('Admin notification sent via Novu successfully');
                    return;
                }
            } catch (error) {
                console.warn('Failed to send Novu notification, falling back to email:', error);
            }
        }

        // Fallback to Resend email
        if (process.env.RESEND_API_KEY) {
            try {
                const emailData = {
                    from: process.env.RESEND_FROM_EMAIL || '<EMAIL>',
                    to: process.env.ADMIN_EMAIL || '<EMAIL>',
                    subject: `🚨 New App Registration: ${appData.name}`,
                    html: generateAdminEmailHTML(notificationData),
                    text: generateAdminEmailText(notificationData)
                };

                const response = await fetch('https://api.resend.com/emails', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${process.env.RESEND_API_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(emailData)
                });

                if (response.ok) {
                    console.log('Admin notification sent via Resend successfully');
                    return;
                }
            } catch (error) {
                console.warn('Failed to send Resend notification:', error);
            }
        }

        // Last resort: console log
        console.log('=== ADMIN NOTIFICATION ===');
        console.log('New app registration request:');
        console.log(`App: ${appData.name} (${appData.appId})`);
        console.log(`Contact: ${appData.contactName} at ${appData.contactEmail}`);
        console.log(`Business: ${appData.businessType || 'Not specified'}`);
        console.log(`Expected Volume: ${appData.expectedVolume || 'Not specified'} notifications/month`);
        console.log('==========================');

    } catch (error) {
        console.error('Failed to send admin notification:', error);
    }
}

// Helper function to send developer confirmation email
async function sendDeveloperConfirmationEmail(appData: any) {
    try {
        if (process.env.RESEND_API_KEY) {
            const emailData = {
                from: process.env.RESEND_FROM_EMAIL || '<EMAIL>',
                to: appData.contactEmail,
                subject: `✅ Application Registration Received: ${appData.name}`,
                html: generateDeveloperEmailHTML(appData),
                text: generateDeveloperEmailText(appData)
            };

            const response = await fetch('https://api.resend.com/emails', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${process.env.RESEND_API_KEY}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(emailData)
            });

            if (response.ok) {
                console.log('Developer confirmation email sent successfully');
                return;
            }
        }

        // Fallback: console log
        console.log('=== DEVELOPER CONFIRMATION ===');
        console.log(`Confirmation sent to: ${appData.contactEmail}`);
        console.log(`App: ${appData.name} (${appData.appId})`);
        console.log('Status: PENDING_APPROVAL');
        console.log('============================');

    } catch (error) {
        console.error('Failed to send developer confirmation email:', error);
    }
}

// Helper function to generate HTML email content
function generateAdminEmailHTML(notification: any): string {
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>New App Registration Request</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #f59e0b; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
                .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
                .status { display: inline-block; background: #f59e0b; color: white; padding: 8px 16px; border-radius: 20px; font-weight: bold; }
                .app-info { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #f59e0b; }
                .contact-info { background: #e8f4fd; padding: 15px; margin: 15px 0; border-radius: 5px; }
                .action-btn { display: inline-block; background: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin-top: 15px; }
                .footer { text-align: center; margin-top: 30px; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🚨 New Application Registration Request</h1>
                    <div class="status">APP REGISTRATION REQUEST</div>
                </div>
                
                <div class="content">
                    <h2>Application Details</h2>
                    <div class="app-info">
                        <strong>App Name:</strong> ${notification.appName}<br>
                        <strong>App ID:</strong> ${notification.appId}<br>
                        <strong>Business Type:</strong> ${notification.businessType || 'Not specified'}<br>
                        <strong>Expected Volume:</strong> ${notification.expectedVolume || 'Not specified'} notifications/month
                    </div>
                    
                    <h3>Contact Information</h3>
                    <div class="contact-info">
                        <strong>Name:</strong> ${notification.contactName}<br>
                        <strong>Email:</strong> ${notification.contactEmail}<br>
                        ${notification.website ? `<strong>Website:</strong> ${notification.website}<br>` : ''}
                    </div>
                    
                    <p><strong>Message:</strong> ${notification.message}</p>
                    
                    <a href="${notification.actionUrl}" class="action-btn">
                        Review Application
                    </a>
                </div>
                
                <div class="footer">
                    <p>This is an automated notification from your Notification System.</p>
                    <p>Action Required: Review and approve/reject this application.</p>
                </div>
            </div>
        </body>
        </html>
    `;
}

// Helper function to generate plain text email content
function generateAdminEmailText(notification: any): string {
    return `
New Application Registration Request

Application Details:
- App Name: ${notification.appName}
- App ID: ${notification.appId}
- Business Type: ${notification.businessType || 'Not specified'}
- Expected Volume: ${notification.expectedVolume || 'Not specified'} notifications/month

Contact Information:
- Name: ${notification.contactName}
- Email: ${notification.contactEmail}
${notification.website ? `- Website: ${notification.website}` : ''}

Message: ${notification.message}

Action Required: Review this application at ${notification.actionUrl}

---
This is an automated notification from your Notification System.
Action Required: Review and approve/reject this application.
    `.trim();
}

// Helper function to generate developer email HTML
function generateDeveloperEmailHTML(appData: any): string {
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Application Registration Received</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #10b981; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
                .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
                .status { display: inline-block; background: #f59e0b; color: white; padding: 8px 16px; border-radius: 20px; font-weight: bold; }
                .app-info { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #10b981; }
                .next-steps { background: #e8f4fd; padding: 15px; margin: 15px 0; border-radius: 5px; }
                .footer { text-align: center; margin-top: 30px; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>✅ Application Registration Received</h1>
                    <div class="status">PENDING APPROVAL</div>
                </div>
                
                <div class="content">
                    <h2>Hello ${appData.contactName},</h2>
                    
                    <p>Thank you for registering your application with our notification system! We've received your request and it's currently under review.</p>
                    
                    <h3>Application Details</h3>
                    <div class="app-info">
                        <strong>App Name:</strong> ${appData.name}<br>
                        <strong>App ID:</strong> ${appData.appId}<br>
                        <strong>Business Type:</strong> ${appData.businessType || 'Not specified'}<br>
                        <strong>Expected Volume:</strong> ${appData.expectedVolume || 'Not specified'} notifications/month<br>
                        <strong>Website:</strong> ${appData.website || 'Not provided'}<br>
                        <strong>Description:</strong> ${appData.description || 'No description provided'}
                    </div>
                    
                    <h3>What Happens Next?</h3>
                    <div class="next-steps">
                        <p><strong>1. Review Process:</strong> Our team will review your application within 24-48 hours</p>
                        <p><strong>2. Approval:</strong> Once approved, you'll receive your API credentials</p>
                        <p><strong>3. Integration:</strong> You can then integrate our notification system into your application</p>
                    </div>
                    
                    <h3>Stay Updated</h3>
                    <p>You can check your application status anytime by visiting:</p>
                    <p><a href="${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/register" style="color: #10b981;">${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/register</a></p>
                    
                    <p>We'll also send you an email once your application is approved or if we need additional information.</p>
                </div>
                
                <div class="footer">
                    <p>This is an automated confirmation from your Notification System registration.</p>
                    <p>If you have any questions, please contact our support team.</p>
                </div>
            </div>
        </body>
        </html>
    `;
}

// Helper function to generate developer email text
function generateDeveloperEmailText(appData: any): string {
    return `
Application Registration Received

Hello ${appData.contactName},

Thank you for registering your application with our notification system! We've received your request and it's currently under review.

Application Details:
- App Name: ${appData.name}
- App ID: ${appData.appId}
- Business Type: ${appData.businessType || 'Not specified'}
- Expected Volume: ${appData.expectedVolume || 'Not specified'} notifications/month
- Website: ${appData.website || 'Not provided'}
- Description: ${appData.description || 'No description provided'}

What Happens Next?
1. Review Process: Our team will review your application within 24-48 hours
2. Approval: Once approved, you'll receive your API credentials
3. Integration: You can then integrate our notification system into your application

Stay Updated:
You can check your application status anytime by visiting:
${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/register

We'll also send you an email once your application is approved or if we need additional information.

---
This is an automated confirmation from your Notification System registration.
If you have any questions, please contact our support team.
    `.trim();
}

// GET /api/applications - Get application configuration (requires API key)
export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const apiKey = searchParams.get('apiKey');
        const appId = searchParams.get('appId');

        if (!apiKey || !appId) {
            return NextResponse.json(
                { success: false, error: 'API key and App ID are required' },
                { status: 400 }
            );
        }

        // Validate API key
        const isValidKey = await validateApiKey(apiKey, appId);
        if (!isValidKey) {
            return NextResponse.json(
                { success: false, error: 'Invalid API key' },
                { status: 401 }
            );
        }

        // Get the application from the main applications table
        const app = await prisma.application.findUnique({
            where: { appId },
            select: {
                appId: true,
                name: true,
                description: true,
                webhookUrl: true,
                novuAppId: true,
                novuApiKey: true,
                resendApiKey: true,
                twilioAccountSid: true,
                twilioAuthToken: true,
                defaultProvider: true,
                fallbackStrategy: true,
                maxRetries: true,
                retryDelay: true,
                rateLimits: true,
                isActive: true,
                metadata: true,
                createdAt: true,
                updatedAt: true
            }
        });

        if (!app) {
            return NextResponse.json(
                { success: false, error: 'Application not found' },
                { status: 404 }
            );
        }

        if (!app.isActive) {
            return NextResponse.json(
                { success: false, error: 'Application is not active' },
                { status: 403 }
            );
        }

        // Update last used timestamp for the API key
        await prisma.appApiKey.updateMany({
            where: { appId, isActive: true },
            data: { lastUsedAt: new Date() }
        });

        // Return safe application data (no sensitive API keys)
        const safeApp = {
            ...app,
            novuApiKey: app.novuApiKey ? '***configured***' : null,
            resendApiKey: app.resendApiKey ? '***configured***' : null,
            twilioAuthToken: app.twilioAuthToken ? '***configured***' : null
        };

        return NextResponse.json({ success: true, data: safeApp });
    } catch (error) {
        console.error('Error fetching application:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// POST /api/applications - Submit new application registration (no auth required)
export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        const {
            appId,
            name,
            description,
            contactEmail,
            contactName,
            website,
            businessType,
            expectedVolume,
            novuAppId,
            novuApiKey,
            resendApiKey,
            twilioAccountSid,
            twilioAuthToken,
            fallbackStrategy = 'failover',
            maxRetries = 3,
            retryDelay = 1000,
            rateLimits = {}
        } = body;

        // Validate required fields
        if (!appId || !name || !contactEmail || !contactName) {
            return NextResponse.json(
                { success: false, error: 'App ID, name, contact email, and contact name are required' },
                { status: 400 }
            );
        }

        // Validate appId format (alphanumeric and hyphens only)
        const appIdRegex = /^[a-zA-Z0-9-]+$/;
        if (!appIdRegex.test(appId)) {
            return NextResponse.json(
                { success: false, error: 'App ID can only contain letters, numbers, and hyphens' },
                { status: 400 }
            );
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(contactEmail)) {
            return NextResponse.json(
                { success: false, error: 'Invalid contact email format' },
                { status: 400 }
            );
        }

        // Check if app is already registered or pending
        const existingApp = await prisma.appRegistration.findUnique({
            where: { appId }
        });

        if (existingApp) {
            return NextResponse.json(
                { success: false, error: 'Application is already registered or pending approval' },
                { status: 409 }
            );
        }

        // Create the app registration request
        const appRegistration = await prisma.appRegistration.create({
            data: {
                appId,
                name,
                description,
                contactEmail,
                contactName,
                website,
                businessType,
                expectedVolume: expectedVolume ? parseInt(expectedVolume) : null,
                novuAppId,
                novuApiKey,
                resendApiKey,
                twilioAccountSid,
                twilioAuthToken,
                fallbackStrategy,
                maxRetries: parseInt(maxRetries),
                retryDelay: parseInt(retryDelay),
                rateLimits
            }
        });

        // Send admin notification using the notification system's own infrastructure
        await sendAdminNotification({
            appId,
            name,
            contactEmail,
            contactName,
            website,
            businessType,
            expectedVolume
        });

        // Also send Slack notification for immediate admin action
        try {
            await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/slack/webhook`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'x-webhook-secret': process.env.SLACK_WEBHOOK_SECRET || 'default-secret'
                },
                body: JSON.stringify({
                    type: 'NEW_APP_REGISTRATION',
                    appId,
                    appName: name,
                    contactEmail,
                    contactName,
                    website,
                    businessType,
                    expectedVolume,
                    description
                })
            });
        } catch (error) {
            console.warn('Failed to send Slack notification:', error);
            // Don't fail the registration if Slack notification fails
        }

        // Send developer confirmation email
        await sendDeveloperConfirmationEmail({
            appId,
            name,
            contactEmail,
            contactName,
            website,
            businessType,
            expectedVolume,
            description
        });

        return NextResponse.json(
            {
                success: true,
                data: {
                    appId: appRegistration.appId,
                    status: 'PENDING_APPROVAL',
                    message: 'Application registration submitted for approval. You will be notified once approved.',
                    estimatedReviewTime: '24-48 hours'
                }
            },
            { status: 201 }
        );
    } catch (error) {
        console.error('Error creating application registration:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// PUT /api/applications - Update application configuration (requires API key)
export async function PUT(request: NextRequest) {
    try {
        const body = await request.json();
        const {
            apiKey,
            appId,
            novuAppId,
            novuApiKey,
            resendApiKey,
            twilioAccountSid,
            twilioAuthToken,
            defaultProvider,
            fallbackStrategy,
            maxRetries,
            retryDelay,
            rateLimits,
            metadata
        } = body;

        if (!apiKey || !appId) {
            return NextResponse.json(
                { success: false, error: 'API key and App ID are required' },
                { status: 400 }
            );
        }

        // Validate the API key
        const isValidKey = await validateApiKey(apiKey, appId);
        if (!isValidKey) {
            return NextResponse.json(
                { success: false, error: 'Invalid API key' },
                { status: 401 }
            );
        }

        // Update the main applications table
        const updateData: any = {};
        if (novuAppId !== undefined) updateData.novuAppId = novuAppId;
        if (novuApiKey !== undefined) updateData.novuApiKey = novuApiKey;
        if (resendApiKey !== undefined) updateData.resendApiKey = resendApiKey;
        if (twilioAccountSid !== undefined) updateData.twilioAccountSid = twilioAccountSid;
        if (twilioAuthToken !== undefined) updateData.twilioAuthToken = twilioAuthToken;
        if (defaultProvider !== undefined) updateData.defaultProvider = defaultProvider;
        if (fallbackStrategy !== undefined) updateData.fallbackStrategy = fallbackStrategy;
        if (maxRetries !== undefined) updateData.maxRetries = parseInt(maxRetries);
        if (retryDelay !== undefined) updateData.retryDelay = parseInt(retryDelay);
        if (rateLimits !== undefined) updateData.rateLimits = rateLimits;
        if (metadata !== undefined) updateData.metadata = metadata;

        const updatedApp = await prisma.application.update({
            where: { appId },
            data: updateData
        });

        return NextResponse.json({
            success: true,
            data: {
                appId: updatedApp.appId,
                name: updatedApp.name,
                status: 'UPDATED',
                message: 'Application configuration updated successfully'
            }
        });
    } catch (error) {
        console.error('Error updating application:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// DELETE /api/applications - Revoke application access (admin only)
export async function DELETE(request: NextRequest) {
    try {
        const body = await request.json();
        const { appId, adminSecret } = body;

        if (!appId || !adminSecret) {
            return NextResponse.json(
                { success: false, error: 'App ID and admin secret are required' },
                { status: 400 }
            );
        }

        // Verify admin secret (in production, use proper admin authentication)
        if (adminSecret !== process.env.ADMIN_SECRET) {
            return NextResponse.json(
                { success: false, error: 'Unauthorized' },
                { status: 401 }
            );
        }

        // Deactivate all API keys for this app
        await prisma.appApiKey.updateMany({
            where: { appId },
            data: { isActive: false }
        });

        // Deactivate the application
        await prisma.application.update({
            where: { appId },
            data: {
                isActive: false,
                metadata: {
                    revokedAt: new Date().toISOString(),
                    reason: 'Access revoked by admin'
                }
            }
        });

        // Update app registration status
        await prisma.appRegistration.update({
            where: { appId },
            data: {
                status: 'REVOKED',
                adminNotes: 'Access revoked by admin'
            }
        });

        return NextResponse.json({
            success: true,
            message: 'Application access revoked successfully'
        });
    } catch (error) {
        console.error('Error revoking application access:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
}
