import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import crypto from 'crypto';

// Verify Slack request signature
function verifySlackRequest(request: NextRequest, body: string): boolean {
    const timestamp = request.headers.get('x-slack-request-timestamp');
    const signature = request.headers.get('x-slack-signature');

    if (!timestamp || !signature) {
        return false;
    }

    // Check if timestamp is within 5 minutes (replay attack protection)
    const now = Math.floor(Date.now() / 1000);
    const requestTime = parseInt(timestamp);
    if (Math.abs(now - requestTime) > 300) { // 5 minutes
        return false;
    }

    // Verify signature
    const baseString = `v0:${timestamp}:${body}`;
    const expectedSignature = 'v0=' + crypto
        .createHmac('sha256', process.env.SLACK_SIGNING_SECRET || '')
        .update(baseString)
        .digest('hex');

    return crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(expectedSignature)
    );
}

// POST /api/slack/events - Handle Slack events (new app registrations)
export async function POST(request: NextRequest) {
    try {
        const body = await request.json();

        // Verify Slack request
        if (!verifySlackRequest(request, JSON.stringify(body))) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Handle Slack URL verification
        if (body.type === 'url_verification') {
            return NextResponse.json({ challenge: body.challenge });
        }

        // Handle app_mention events (when someone mentions the bot)
        if (body.event && body.event.type === 'app_mention') {
            const channel = body.event.channel;
            const user = body.event.user;
            const text = body.event.text;

            // Check if user is asking for pending registrations
            if (text.includes('pending') || text.includes('registrations')) {
                await sendPendingRegistrationsMessage(channel);
            } else if (text.includes('help')) {
                await sendHelpMessage(channel);
            } else {
                await sendHelpMessage(channel);
            }
        }

        return NextResponse.json({ success: true });

    } catch (error) {
        console.error('Error handling Slack event:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// Send message with pending registrations
async function sendPendingRegistrationsMessage(channel: string) {
    try {
        const pendingApps = await prisma.appRegistration.findMany({
            where: { status: 'PENDING_APPROVAL' },
            orderBy: { createdAt: 'asc' }
        });

        if (pendingApps.length === 0) {
            await sendSlackMessage(channel, {
                text: "🎉 No pending application registrations!",
                attachments: [{
                    color: "good",
                    text: "All applications have been processed."
                }]
            });
            return;
        }

        // Send each pending registration as a separate message with action buttons
        for (const app of pendingApps) {
            const message = {
                text: `📝 *New Application Registration: ${app.name}*`,
                attachments: [
                    {
                        color: "warning",
                        fields: [
                            {
                                title: "App ID",
                                value: app.appId,
                                short: true
                            },
                            {
                                title: "Contact",
                                value: `${app.contactName} (${app.contactEmail})`,
                                short: true
                            },
                            {
                                title: "Business Type",
                                value: app.businessType || "Not specified",
                                short: true
                            },
                            {
                                title: "Expected Volume",
                                value: app.expectedVolume ? `${app.expectedVolume}/month` : "Not specified",
                                short: true
                            },
                            {
                                title: "Description",
                                value: app.description || "No description provided",
                                short: false
                            },
                            {
                                title: "Submitted",
                                value: new Date(app.createdAt).toLocaleString(),
                                short: true
                            }
                        ],
                        actions: [
                            {
                                name: "approve",
                                text: "✅ Approve",
                                type: "button",
                                style: "primary",
                                value: app.appId,
                                confirm: {
                                    title: "Approve Application?",
                                    text: `Are you sure you want to approve "${app.name}" (${app.appId})?`,
                                    ok_text: "Yes, Approve",
                                    dismiss_text: "Cancel"
                                }
                            },
                            {
                                name: "reject",
                                text: "❌ Reject",
                                type: "button",
                                style: "danger",
                                value: app.appId,
                                confirm: {
                                    title: "Reject Application?",
                                    text: `Are you sure you want to reject "${app.name}" (${app.appId})? Please provide a reason.`,
                                    ok_text: "Yes, Reject",
                                    dismiss_text: "Cancel"
                                }
                            },
                            {
                                name: "view_details",
                                text: "👁️ View Details",
                                type: "button",
                                value: app.appId
                            }
                        ]
                    }
                ]
            };

            await sendSlackMessage(channel, message);
        }

    } catch (error) {
        console.error('Error sending pending registrations message:', error);
        await sendSlackMessage(channel, {
            text: "❌ Error fetching pending registrations",
            attachments: [{
                color: "danger",
                text: "Please try again or contact support."
            }]
        });
    }
}

// Send help message
async function sendHelpMessage(channel: string) {
    const helpMessage = {
        text: "🤖 *Notification System Admin Bot*",
        attachments: [
            {
                color: "good",
                fields: [
                    {
                        title: "Available Commands",
                        value: "• `@bot pending` - Show pending registrations\n• `@bot registrations` - Show pending registrations\n• `@bot help` - Show this help message",
                        short: false
                    },
                    {
                        title: "Actions",
                        value: "• Click ✅ Approve to approve applications\n• Click ❌ Reject to reject applications\n• Click 👁️ View Details for more info",
                        short: false
                    }
                ]
            }
        ]
    };

    await sendSlackMessage(channel, helpMessage);
}

// Send message to Slack channel
async function sendSlackMessage(channel: string, message: any) {
    try {
        const response = await fetch('https://slack.com/api/chat.postMessage', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                channel,
                ...message
            })
        });

        if (!response.ok) {
            console.error('Failed to send Slack message:', await response.text());
        }
    } catch (error) {
        console.error('Error sending Slack message:', error);
    }
} 