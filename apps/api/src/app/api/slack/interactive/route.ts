import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { hash } from 'bcryptjs';
import crypto from 'crypto';

// Verify Slack request signature
function verifySlackRequest(request: NextRequest, body: string): boolean {
    const timestamp = request.headers.get('x-slack-request-timestamp');
    const signature = request.headers.get('x-slack-signature');

    if (!timestamp || !signature) {
        return false;
    }

    // Check if timestamp is within 5 minutes (replay attack protection)
    const now = Math.floor(Date.now() / 1000);
    const requestTime = parseInt(timestamp);
    if (Math.abs(now - requestTime) > 300) { // 5 minutes
        return false;
    }

    // Verify signature
    const baseString = `v0:${timestamp}:${body}`;
    const expectedSignature = 'v0=' + crypto
        .createHmac('sha256', process.env.SLACK_SIGNING_SECRET || '')
        .update(baseString)
        .digest('hex');

    return crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(expectedSignature)
    );
}

// Generate secure API key
function generateSecureApiKey(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    return `spark_${timestamp}_${random}`;
}

// Send message to Slack channel
async function sendSlackMessage(channel: string, message: any) {
    try {
        const response = await fetch('https://slack.com/api/chat.postMessage', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                channel,
                ...message
            })
        });

        if (!response.ok) {
            console.error('Failed to send Slack message:', await response.text());
        }
    } catch (error) {
        console.error('Error sending Slack message:', error);
    }
}

// Send admin notification about approval/rejection
async function sendAdminNotification(appData: any, action: string, reason?: string, adminUser?: string) {
    try {
        const notificationData = {
            type: action === 'APPROVE' ? 'APP_APPROVED' : 'APP_REJECTED',
            appId: appData.appId,
            appName: appData.name,
            contactEmail: appData.contactEmail,
            contactName: appData.contactName,
            website: appData.website,
            businessType: appData.businessType,
            expectedVolume: appData.expectedVolume,
            message: action === 'APPROVE'
                ? `Application ${appData.name} has been approved by ${adminUser || 'admin'} via Slack`
                : `Application ${appData.name} has been rejected by ${adminUser || 'admin'} via Slack. Reason: ${reason || 'No reason provided'}`,
            timestamp: new Date()
        };

        // Try to send via Novu first (if configured)
        if (process.env.NOVU_API_KEY && process.env.NOVU_APP_ID) {
            try {
                const novuPayload = {
                    name: 'admin-notification',
                    to: {
                        subscriberId: 'admin',
                        email: process.env.ADMIN_EMAIL || '<EMAIL>',
                        firstName: 'Admin'
                    },
                    payload: {
                        type: notificationData.type,
                        appId: notificationData.appId,
                        appName: notificationData.appName,
                        contactEmail: notificationData.contactEmail,
                        contactName: notificationData.contactName,
                        businessType: notificationData.businessType,
                        expectedVolume: notificationData.expectedVolume,
                        message: notificationData.message,
                        actionUrl: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/admin/applications/${notificationData.appId}`,
                        priority: 'NORMAL'
                    }
                };

                const response = await fetch(`https://api.novu.co/v1/triggers/${novuPayload.name}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `ApiKey ${process.env.NOVU_API_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(novuPayload)
                });

                if (response.ok) {
                    console.log(`Admin notification sent via Novu for ${action.toLowerCase()}`);
                    return;
                }
            } catch (error) {
                console.warn('Failed to send Novu notification, falling back to email:', error);
            }
        }

        // Fallback to Resend email
        if (process.env.RESEND_API_KEY) {
            try {
                const emailData = {
                    from: process.env.RESEND_FROM_EMAIL || '<EMAIL>',
                    to: process.env.ADMIN_EMAIL || '<EMAIL>',
                    subject: action === 'APPROVE'
                        ? `✅ App Approved via Slack: ${appData.name}`
                        : `❌ App Rejected via Slack: ${appData.name}`,
                    html: generateAdminEmailHTML(notificationData),
                    text: generateAdminEmailText(notificationData)
                };

                const response = await fetch('https://api.resend.com/emails', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${process.env.RESEND_API_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(emailData)
                });

                if (response.ok) {
                    console.log(`Admin notification sent via Resend for ${action.toLowerCase()}`);
                    return;
                }
            } catch (error) {
                console.warn('Failed to send Resend notification:', error);
            }
        }

        // Last resort: console log
        console.log('=== ADMIN NOTIFICATION ===');
        console.log(`Application ${action.toLowerCase()} via Slack: ${appData.name} (${appData.appId})`);
        console.log(`Admin: ${adminUser || 'Unknown'}`);
        console.log(`Contact: ${appData.contactName} at ${appData.contactEmail}`);
        if (action === 'REJECT' && reason) {
            console.log(`Reason: ${reason}`);
        }
        console.log('==========================');

    } catch (error) {
        console.error('Failed to send admin notification:', error);
    }
}

// Generate HTML email content
function generateAdminEmailHTML(notification: any): string {
    const isApproved = notification.type === 'APP_APPROVED';
    const statusColor = isApproved ? '#10b981' : '#ef4444';
    const statusIcon = isApproved ? '✅' : '❌';
    const statusText = isApproved ? 'APPROVED' : 'REJECTED';

    return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Application ${statusText} via Slack</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: ${statusColor}; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
                .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
                .status { display: inline-block; background: ${statusColor}; color: white; padding: 8px 16px; border-radius: 20px; font-weight: bold; }
                .app-info { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid ${statusColor}; }
                .contact-info { background: #e8f4fd; padding: 15px; margin: 15px 0; border-radius: 5px; }
                .slack-info { background: #f0f0f0; padding: 15px; margin: 15px 0; border-radius: 5px; }
                .action-btn { display: inline-block; background: ${statusColor}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin-top: 15px; }
                .footer { text-align: center; margin-top: 30px; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>${statusIcon} Application ${statusText} via Slack</h1>
                    <div class="status">${statusText}</div>
                </div>
                
                <div class="content">
                    <h2>Application Details</h2>
                    <div class="app-info">
                        <strong>App Name:</strong> ${notification.appName}<br>
                        <strong>App ID:</strong> ${notification.appId}<br>
                        <strong>Business Type:</strong> ${notification.businessType || 'Not specified'}<br>
                        <strong>Expected Volume:</strong> ${notification.expectedVolume || 'Not specified'} notifications/month
                    </div>
                    
                    <h3>Contact Information</h3>
                    <div class="contact-info">
                        <strong>Name:</strong> ${notification.contactName}<br>
                        <strong>Email:</strong> ${notification.contactEmail}<br>
                        ${notification.website ? `<strong>Website:</strong> ${notification.website}<br>` : ''}
                    </div>
                    
                    <h3>Slack Action</h3>
                    <div class="slack-info">
                        <strong>Action:</strong> ${notification.message}<br>
                        <strong>Timestamp:</strong> ${notification.timestamp.toLocaleString()}
                    </div>
                    
                    <p><strong>Status:</strong> ${notification.message}</p>
                    
                    <a href="${notification.actionUrl}" class="action-btn">
                        View Application
                    </a>
                </div>
                
                <div class="footer">
                    <p>This is an automated notification from your Notification System.</p>
                    <p>Application was ${notification.type === 'APP_APPROVED' ? 'approved' : 'rejected'} via Slack.</p>
                </div>
            </div>
        </body>
        </html>
    `;
}

// Generate plain text email content
function generateAdminEmailText(notification: any): string {
    const isApproved = notification.type === 'APP_APPROVED';
    const statusText = isApproved ? 'APPROVED' : 'REJECTED';

    return `
Application ${statusText} via Slack

Application Details:
- App Name: ${notification.appName}
- App ID: ${notification.appId}
- Business Type: ${notification.businessType || 'Not specified'}
- Expected Volume: ${notification.expectedVolume || 'Not specified'} notifications/month

Contact Information:
- Name: ${notification.contactName}
- Email: ${notification.contactEmail}
${notification.website ? `- Website: ${notification.website}` : ''}

Slack Action:
- Action: ${notification.message}
- Timestamp: ${notification.timestamp.toLocaleString()}

Status: ${notification.message}

View application at: ${notification.actionUrl}

---
This is an automated notification from your Notification System.
Application was ${isApproved ? 'approved' : 'rejected'} via Slack.
    `.trim();
}

// POST /api/slack/interactive - Handle Slack interactive messages (button clicks)
export async function POST(request: NextRequest) {
    try {
        const formData = await request.formData();
        const payload = formData.get('payload');

        if (!payload) {
            return NextResponse.json({ error: 'No payload' }, { status: 400 });
        }

        const payloadData = JSON.parse(payload as string);

        // Verify Slack request
        if (!verifySlackRequest(request, payload as string)) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Handle button clicks
        if (payloadData.type === 'interactive_message' || payloadData.type === 'block_actions') {
            const actions = payloadData.actions || [];
            const user = payloadData.user;
            const channel = payloadData.channel?.id || payloadData.channel_id;

            for (const action of actions) {
                if (action.name === 'approve') {
                    await handleApproval(action.value, user, channel);
                } else if (action.name === 'reject') {
                    await handleRejection(action.value, user, channel);
                } else if (action.name === 'view_details') {
                    await handleViewDetails(action.value, user, channel);
                }
            }
        }

        return NextResponse.json({ success: true });

    } catch (error) {
        console.error('Error handling Slack interactive message:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// Handle application approval
async function handleApproval(appId: string, user: any, channel: string) {
    try {
        // Find the pending registration
        const appRegistration = await prisma.appRegistration.findUnique({
            where: { appId }
        });

        if (!appRegistration) {
            await sendSlackMessage(channel, {
                text: `❌ Error: Application ${appId} not found`
            });
            return;
        }

        if (appRegistration.status !== 'PENDING_APPROVAL') {
            await sendSlackMessage(channel, {
                text: `❌ Error: Application ${appId} is not pending approval (status: ${appRegistration.status})`
            });
            return;
        }

        // Generate a secure API key
        const apiKey = generateSecureApiKey();
        const hashedApiKey = await hash(apiKey, 12);

        // Create the API key record
        await prisma.appApiKey.create({
            data: {
                appId,
                apiKeyHash: hashedApiKey,
                name: 'Primary API Key',
                permissions: ['send_notifications', 'read_notifications', 'manage_preferences'],
                isActive: true
            }
        });

        // Create the application in the main applications table
        await prisma.application.create({
            data: {
                appId,
                name: appRegistration.name,
                description: appRegistration.description,
                webhookUrl: null,
                novuAppId: appRegistration.novuAppId,
                novuApiKey: appRegistration.novuApiKey,
                resendApiKey: appRegistration.resendApiKey,
                twilioAccountSid: appRegistration.twilioAccountSid,
                twilioAuthToken: appRegistration.twilioAuthToken,
                defaultProvider: 'novu',
                fallbackStrategy: appRegistration.fallbackStrategy,
                maxRetries: appRegistration.maxRetries,
                retryDelay: appRegistration.retryDelay,
                rateLimits: appRegistration.rateLimits,
                isActive: true,
                metadata: {
                    approvedAt: new Date().toISOString(),
                    approvedBy: `${user.username || user.name} (via Slack)`,
                    adminNotes: 'Approved via Slack'
                }
            }
        });

        // Update the app registration status
        await prisma.appRegistration.update({
            where: { appId },
            data: {
                status: 'APPROVED',
                adminNotes: `Approved via Slack by ${user.username || user.name}`,
                approvedAt: new Date(),
                approvedBy: user.username || user.name
            }
        });

        // Send success message to Slack
        await sendSlackMessage(channel, {
            text: `✅ *Application Approved Successfully!*`,
            attachments: [
                {
                    color: "good",
                    fields: [
                        {
                            title: "App Name",
                            value: appRegistration.name,
                            short: true
                        },
                        {
                            title: "App ID",
                            value: appId,
                            short: true
                        },
                        {
                            title: "Approved By",
                            value: user.username || user.name,
                            short: true
                        },
                        {
                            title: "API Key",
                            value: `\`${apiKey}\`\n*Copy this key and share it with the developer!*`,
                            short: false
                        }
                    ],
                    footer: "The application is now active and can use the notification system."
                }
            ]
        });

        // Send admin notification
        await sendAdminNotification(appRegistration, 'APPROVE', undefined, user.username || user.name);

    } catch (error) {
        console.error('Error handling approval:', error);
        await sendSlackMessage(channel, {
            text: `❌ Error approving application ${appId}`,
            attachments: [{
                color: "danger",
                text: "Please try again or contact support."
            }]
        });
    }
}

// Handle application rejection
async function handleRejection(appId: string, user: any, channel: string) {
    try {
        // Find the pending registration
        const appRegistration = await prisma.appRegistration.findUnique({
            where: { appId }
        });

        if (!appRegistration) {
            await sendSlackMessage(channel, {
                text: `❌ Error: Application ${appId} not found`
            });
            return;
        }

        if (appRegistration.status !== 'PENDING_APPROVAL') {
            await sendSlackMessage(channel, {
                text: `❌ Error: Application ${appId} is not pending approval (status: ${appRegistration.status})`
            });
            return;
        }

        // Update the app registration status to rejected
        await prisma.appRegistration.update({
            where: { appId },
            data: {
                status: 'REJECTED',
                adminNotes: `Rejected via Slack by ${user.username || user.name}`
            }
        });

        // Send success message to Slack
        await sendSlackMessage(channel, {
            text: `❌ *Application Rejected*`,
            attachments: [
                {
                    color: "danger",
                    fields: [
                        {
                            title: "App Name",
                            value: appRegistration.name,
                            short: true
                        },
                        {
                            title: "App ID",
                            value: appId,
                            short: true
                        },
                        {
                            title: "Rejected By",
                            value: user.username || user.name,
                            short: true
                        },
                        {
                            title: "Status",
                            value: "Application has been rejected and will not be activated.",
                            short: false
                        }
                    ],
                    footer: "The developer will need to submit a new registration request."
                }
            ]
        });

        // Send admin notification
        await sendAdminNotification(appRegistration, 'REJECT', 'Rejected via Slack', user.username || user.name);

    } catch (error) {
        console.error('Error handling rejection:', error);
        await sendSlackMessage(channel, {
            text: `❌ Error rejecting application ${appId}`,
            attachments: [{
                color: "danger",
                text: "Please try again or contact support."
            }]
        });
    }
}

// Handle view details request
async function handleViewDetails(appId: string, user: any, channel: string) {
    try {
        const appRegistration = await prisma.appRegistration.findUnique({
            where: { appId }
        });

        if (!appRegistration) {
            await sendSlackMessage(channel, {
                text: `❌ Error: Application ${appId} not found`
            });
            return;
        }

        await sendSlackMessage(channel, {
            text: `📋 *Application Details: ${appRegistration.name}*`,
            attachments: [
                {
                    color: "info",
                    fields: [
                        {
                            title: "App ID",
                            value: appRegistration.appId,
                            short: true
                        },
                        {
                            title: "Status",
                            value: appRegistration.status,
                            short: true
                        },
                        {
                            title: "Contact Name",
                            value: appRegistration.contactName,
                            short: true
                        },
                        {
                            title: "Contact Email",
                            value: appRegistration.contactEmail,
                            short: true
                        },
                        {
                            title: "Website",
                            value: appRegistration.website || "Not provided",
                            short: true
                        },
                        {
                            title: "Business Type",
                            value: appRegistration.businessType || "Not specified",
                            short: true
                        },
                        {
                            title: "Expected Volume",
                            value: appRegistration.expectedVolume ? `${appRegistration.expectedVolume} notifications/month` : "Not specified",
                            short: true
                        },
                        {
                            title: "Description",
                            value: appRegistration.description || "No description provided",
                            short: false
                        },
                        {
                            title: "Submitted",
                            value: new Date(appRegistration.createdAt).toLocaleString(),
                            short: true
                        },
                        {
                            title: "Last Updated",
                            value: new Date(appRegistration.updatedAt).toLocaleString(),
                            short: true
                        }
                    ]
                }
            ]
        });

    } catch (error) {
        console.error('Error handling view details:', error);
        await sendSlackMessage(channel, {
            text: `❌ Error fetching details for application ${appId}`,
            attachments: [{
                color: "danger",
                text: "Please try again or contact support."
            }]
        });
    }
} 