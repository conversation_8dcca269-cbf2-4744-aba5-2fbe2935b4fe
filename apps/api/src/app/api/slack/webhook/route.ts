import { NextRequest, NextResponse } from 'next/server';

// Send message to Slack channel
async function sendSlackMessage(channel: string, message: any) {
    try {
        const response = await fetch('https://slack.com/api/chat.postMessage', {
            method: 'POST',
            headers: {
                'Authorization': `Bear<PERSON> ${process.env.SLACK_BOT_TOKEN}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                channel,
                ...message
            })
        });

        if (!response.ok) {
            console.error('Failed to send Slack message:', await response.text());
        }
    } catch (error) {
        console.error('Error sending Slack message:', error);
    }
}

// POST /api/slack/webhook - Webhook endpoint for sending notifications to Slack
export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        const {
            type,
            appId,
            appName,
            contactEmail,
            contactName,
            website,
            businessType,
            expectedVolume,
            description
        } = body;

        // Verify webhook secret (optional security)
        const webhookSecret = request.headers.get('x-webhook-secret');
        if (process.env.SLACK_WEBHOOK_SECRET && webhookSecret !== process.env.SLACK_WEBHOOK_SECRET) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Get admin channel from environment
        const adminChannel = process.env.SLACK_ADMIN_CHANNEL || '#admin';

        if (type === 'NEW_APP_REGISTRATION') {
            // Send new registration notification to Slack
            const message = {
                text: `🚨 *New Application Registration Request*`,
                attachments: [
                    {
                        color: "warning",
                        fields: [
                            {
                                title: "App Name",
                                value: appName,
                                short: true
                            },
                            {
                                title: "App ID",
                                value: appId,
                                short: true
                            },
                            {
                                title: "Contact",
                                value: `${contactName} (${contactEmail})`,
                                short: true
                            },
                            {
                                title: "Business Type",
                                value: businessType || "Not specified",
                                short: true
                            },
                            {
                                title: "Expected Volume",
                                value: expectedVolume ? `${expectedVolume}/month` : "Not specified",
                                short: true
                            },
                            {
                                title: "Website",
                                value: website || "Not provided",
                                short: true
                            },
                            {
                                title: "Description",
                                value: description || "No description provided",
                                short: false
                            }
                        ],
                        actions: [
                            {
                                name: "approve",
                                text: "✅ Approve",
                                type: "button",
                                style: "primary",
                                value: appId,
                                confirm: {
                                    title: "Approve Application?",
                                    text: `Are you sure you want to approve "${appName}" (${appId})?`,
                                    ok_text: "Yes, Approve",
                                    dismiss_text: "Cancel"
                                }
                            },
                            {
                                name: "reject",
                                text: "❌ Reject",
                                type: "button",
                                style: "danger",
                                value: appId,
                                confirm: {
                                    title: "Reject Application?",
                                    text: `Are you sure you want to reject "${appName}" (${appId})? Please provide a reason.`,
                                    ok_text: "Yes, Reject",
                                    dismiss_text: "Cancel"
                                }
                            },
                            {
                                name: "view_details",
                                text: "👁️ View Details",
                                type: "button",
                                value: appId
                            }
                        ],
                        footer: "Click the buttons above to approve or reject this application."
                    }
                ]
            };

            await sendSlackMessage(adminChannel, message);

            return NextResponse.json({
                success: true,
                message: 'Slack notification sent successfully'
            });
        }

        return NextResponse.json({
            success: false,
            error: 'Unknown notification type'
        }, { status: 400 });

    } catch (error) {
        console.error('Error handling Slack webhook:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
} 