'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

interface RegistrationForm {
    appId: string;
    name: string;
    description: string;
    contactEmail: string;
    contactName: string;
    website: string;
    businessType: string;
    expectedVolume: string;
    novuAppId: string;
    novuApiKey: string;
    resendApiKey: string;
    twilioAccountSid: string;
    twilioAuthToken: string;
    fallbackStrategy: string;
    maxRetries: string;
    retryDelay: string;
}

interface RegistrationResponse {
    success: boolean;
    data?: {
        appId: string;
        status: string;
        message: string;
        estimatedReviewTime: string;
    };
    error?: string;
}

interface ApprovalStatus {
    appId: string;
    status: 'PENDING_APPROVAL' | 'APPROVED' | 'REJECTED';
    apiKey?: string;
    message?: string;
    adminNotes?: string;
}

export default function RegisterPage() {
    const router = useRouter();
    const [formData, setFormData] = useState<RegistrationForm>({
        appId: '',
        name: '',
        description: '',
        contactEmail: '',
        contactName: '',
        website: '',
        businessType: '',
        expectedVolume: '',
        novuAppId: '',
        novuApiKey: '',
        resendApiKey: '',
        twilioAccountSid: '',
        twilioAuthToken: '',
        fallbackStrategy: 'failover',
        maxRetries: '3',
        retryDelay: '1000'
    });

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [registrationResult, setRegistrationResult] = useState<RegistrationResponse | null>(null);
    const [approvalStatus, setApprovalStatus] = useState<ApprovalStatus | null>(null);
    const [isCheckingStatus, setIsCheckingStatus] = useState(false);
    const [statusCheckInterval, setStatusCheckInterval] = useState<NodeJS.Timeout | null>(null);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const validateForm = (): string[] => {
        const errors: string[] = [];

        if (!formData.appId.trim()) errors.push('App ID is required');
        if (!formData.name.trim()) errors.push('App name is required');
        if (!formData.contactEmail.trim()) errors.push('Contact email is required');
        if (!formData.contactName.trim()) errors.push('Contact name is required');

        // Validate App ID format (alphanumeric and hyphens only)
        if (formData.appId && !/^[a-zA-Z0-9-]+$/.test(formData.appId)) {
            errors.push('App ID can only contain letters, numbers, and hyphens');
        }

        // Validate email format
        if (formData.contactEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contactEmail)) {
            errors.push('Please enter a valid email address');
        }

        return errors;
    };

    const startStatusChecking = (appId: string) => {
        // Check status every 5 seconds
        const interval = setInterval(async () => {
            try {
                const response = await fetch(`/api/applications/status?appId=${appId}`);
                const data = await response.json();

                if (data.success && data.data) {
                    setApprovalStatus(data.data);

                    // If approved or rejected, stop checking
                    if (data.data.status === 'APPROVED' || data.data.status === 'REJECTED') {
                        clearInterval(interval);
                        setStatusCheckInterval(null);
                    }
                }
            } catch (error) {
                console.error('Error checking status:', error);
            }
        }, 5000);

        setStatusCheckInterval(interval);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        const errors = validateForm();
        if (errors.length > 0) {
            alert('Please fix the following errors:\n' + errors.join('\n'));
            return;
        }

        setIsSubmitting(true);
        setRegistrationResult(null);
        setApprovalStatus(null);

        try {
            const response = await fetch('/api/applications', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            });

            const result: RegistrationResponse = await response.json();
            setRegistrationResult(result);

            if (result.success && result.data) {
                // Start checking approval status
                startStatusChecking(result.data.appId);
            }

        } catch (error) {
            console.error('Error submitting registration:', error);
            setRegistrationResult({
                success: false,
                error: 'Failed to submit registration. Please try again.'
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleStopChecking = () => {
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
            setStatusCheckInterval(null);
        }
    };

    const resetForm = () => {
        setFormData({
            appId: '',
            name: '',
            description: '',
            contactEmail: '',
            contactName: '',
            website: '',
            businessType: '',
            expectedVolume: '',
            novuAppId: '',
            novuApiKey: '',
            resendApiKey: '',
            twilioAccountSid: '',
            twilioAuthToken: '',
            fallbackStrategy: 'failover',
            maxRetries: '3',
            retryDelay: '1000'
        });
        setRegistrationResult(null);
        setApprovalStatus(null);
        handleStopChecking();
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto">
                {/* Header */}
                <div className="text-center mb-12">
                    <h1 className="text-4xl font-bold text-gray-900 mb-4">
                        🚀 Register Your Application
                    </h1>
                    <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                        Get started with our powerful notification system. Register your application and receive your API credentials instantly upon approval.
                    </p>
                </div>

                {/* Registration Form */}
                {!registrationResult?.success && (
                    <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
                        <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                            Application Information
                        </h2>

                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Basic Information */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label htmlFor="appId" className="block text-sm font-medium text-gray-700 mb-2">
                                        App ID * <span className="text-gray-500">(unique identifier)</span>
                                    </label>
                                    <input
                                        type="text"
                                        id="appId"
                                        name="appId"
                                        value={formData.appId}
                                        onChange={handleInputChange}
                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="my-awesome-app"
                                        required
                                    />
                                    <p className="text-sm text-gray-500 mt-1">
                                        Only letters, numbers, and hyphens allowed
                                    </p>
                                </div>

                                <div>
                                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                                        Application Name *
                                    </label>
                                    <input
                                        type="text"
                                        id="name"
                                        name="name"
                                        value={formData.name}
                                        onChange={handleInputChange}
                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="My Awesome Application"
                                        required
                                    />
                                </div>
                            </div>

                            <div>
                                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                                    Description
                                </label>
                                <textarea
                                    id="description"
                                    name="description"
                                    value={formData.description}
                                    onChange={handleInputChange}
                                    rows={3}
                                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    placeholder="Brief description of your application and how you plan to use notifications..."
                                />
                            </div>

                            {/* Contact Information */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label htmlFor="contactName" className="block text-sm font-medium text-gray-700 mb-2">
                                        Contact Name *
                                    </label>
                                    <input
                                        type="text"
                                        id="contactName"
                                        name="contactName"
                                        value={formData.contactName}
                                        onChange={handleInputChange}
                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="John Developer"
                                        required
                                    />
                                </div>

                                <div>
                                    <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700 mb-2">
                                        Contact Email *
                                    </label>
                                    <input
                                        type="email"
                                        id="contactEmail"
                                        name="contactEmail"
                                        value={formData.contactEmail}
                                        onChange={handleInputChange}
                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="<EMAIL>"
                                        required
                                    />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-2">
                                        Website
                                    </label>
                                    <input
                                        type="url"
                                        id="website"
                                        name="website"
                                        value={formData.website}
                                        onChange={handleInputChange}
                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="https://myapp.com"
                                    />
                                </div>

                                <div>
                                    <label htmlFor="businessType" className="block text-sm font-medium text-gray-700 mb-2">
                                        Business Type
                                    </label>
                                    <select
                                        id="businessType"
                                        name="businessType"
                                        value={formData.businessType}
                                        onChange={handleInputChange}
                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="">Select business type...</option>
                                        <option value="SaaS">SaaS</option>
                                        <option value="E-commerce">E-commerce</option>
                                        <option value="Mobile App">Mobile App</option>
                                        <option value="Web App">Web App</option>
                                        <option value="Enterprise">Enterprise</option>
                                        <option value="Startup">Startup</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label htmlFor="expectedVolume" className="block text-sm font-medium text-gray-700 mb-2">
                                    Expected Monthly Notification Volume
                                </label>
                                <select
                                    id="expectedVolume"
                                    name="expectedVolume"
                                    value={formData.expectedVolume}
                                    onChange={handleInputChange}
                                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                >
                                    <option value="">Select volume...</option>
                                    <option value="100">Less than 100</option>
                                    <option value="1000">100 - 1,000</option>
                                    <option value="10000">1,000 - 10,000</option>
                                    <option value="100000">10,000 - 100,000</option>
                                    <option value="1000000">100,000 - 1,000,000</option>
                                    <option value="10000000">More than 1,000,000</option>
                                </select>
                            </div>

                            {/* Provider Configuration */}
                            <div className="border-t pt-6">
                                <h3 className="text-lg font-medium text-gray-900 mb-4">
                                    Provider Configuration (Optional)
                                </h3>
                                <p className="text-sm text-gray-600 mb-4">
                                    If you have existing notification provider accounts, you can configure them here. Otherwise, we'll use our default providers.
                                </p>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label htmlFor="novuAppId" className="block text-sm font-medium text-gray-700 mb-2">
                                            Novu App ID
                                        </label>
                                        <input
                                            type="text"
                                            id="novuAppId"
                                            name="novuAppId"
                                            value={formData.novuAppId}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                            placeholder="Your Novu app ID"
                                        />
                                    </div>

                                    <div>
                                        <label htmlFor="novuApiKey" className="block text-sm font-medium text-gray-700 mb-2">
                                            Novu API Key
                                        </label>
                                        <input
                                            type="password"
                                            id="novuApiKey"
                                            name="novuApiKey"
                                            value={formData.novuApiKey}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                            placeholder="Your Novu API key"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                                    <div>
                                        <label htmlFor="resendApiKey" className="block text-sm font-medium text-gray-700 mb-2">
                                            Resend API Key
                                        </label>
                                        <input
                                            type="password"
                                            id="resendApiKey"
                                            name="resendApiKey"
                                            value={formData.resendApiKey}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                            placeholder="Your Resend API key"
                                        />
                                    </div>

                                    <div>
                                        <label htmlFor="twilioAccountSid" className="block text-sm font-medium text-gray-700 mb-2">
                                            Twilio Account SID
                                        </label>
                                        <input
                                            type="text"
                                            id="twilioAccountSid"
                                            name="twilioAccountSid"
                                            value={formData.twilioAccountSid}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                            placeholder="Your Twilio account SID"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                                    <div>
                                        <label htmlFor="twilioAuthToken" className="block text-sm font-medium text-gray-700 mb-2">
                                            Twilio Auth Token
                                        </label>
                                        <input
                                            type="password"
                                            id="twilioAuthToken"
                                            name="twilioAuthToken"
                                            value={formData.twilioAuthToken}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                            placeholder="Your Twilio auth token"
                                        />
                                    </div>

                                    <div>
                                        <label htmlFor="fallbackStrategy" className="block text-sm font-medium text-gray-700 mb-2">
                                            Fallback Strategy
                                        </label>
                                        <select
                                            id="fallbackStrategy"
                                            name="fallbackStrategy"
                                            value={formData.fallbackStrategy}
                                            onChange={handleInputChange}
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        >
                                            <option value="failover">Failover (try next provider on failure)</option>
                                            <option value="parallel">Parallel (send to multiple providers)</option>
                                            <option value="hybrid">Hybrid (smart fallback)</option>
                                        </select>
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                                    <div>
                                        <label htmlFor="maxRetries" className="block text-sm font-medium text-gray-700 mb-2">
                                            Max Retries
                                        </label>
                                        <input
                                            type="number"
                                            id="maxRetries"
                                            name="maxRetries"
                                            value={formData.maxRetries}
                                            onChange={handleInputChange}
                                            min="1"
                                            max="10"
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        />
                                    </div>

                                    <div>
                                        <label htmlFor="retryDelay" className="block text-sm font-medium text-gray-700 mb-2">
                                            Retry Delay (ms)
                                        </label>
                                        <input
                                            type="number"
                                            id="retryDelay"
                                            name="retryDelay"
                                            value={formData.retryDelay}
                                            onChange={handleInputChange}
                                            min="100"
                                            max="10000"
                                            step="100"
                                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* Submit Button */}
                            <div className="pt-6">
                                <button
                                    type="submit"
                                    disabled={isSubmitting}
                                    className="w-full bg-blue-600 text-white py-4 px-6 rounded-lg font-semibold text-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                >
                                    {isSubmitting ? 'Submitting...' : 'Submit Application Registration'}
                                </button>
                            </div>
                        </form>
                    </div>
                )}

                {/* Registration Result */}
                {registrationResult && (
                    <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
                        {registrationResult.success ? (
                            <div className="text-center">
                                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
                                    <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>

                                <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                                    Application Submitted Successfully!
                                </h2>

                                <div className="bg-gray-50 rounded-lg p-6 mb-6">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                                        <div>
                                            <span className="font-medium text-gray-700">App ID:</span>
                                            <span className="ml-2 font-mono text-gray-900">{registrationResult.data?.appId}</span>
                                        </div>
                                        <div>
                                            <span className="font-medium text-gray-700">Status:</span>
                                            <span className="ml-2 text-gray-900">{registrationResult.data?.status}</span>
                                        </div>
                                        <div className="md:col-span-2">
                                            <span className="font-medium text-gray-700">Message:</span>
                                            <span className="ml-2 text-gray-900">{registrationResult.data?.message}</span>
                                        </div>
                                        <div className="md:col-span-2">
                                            <span className="font-medium text-gray-700">Estimated Review Time:</span>
                                            <span className="ml-2 text-gray-900">{registrationResult.data?.estimatedReviewTime}</span>
                                        </div>
                                    </div>
                                </div>

                                <p className="text-gray-600 mb-6">
                                    We've sent a confirmation email to <strong>{formData.contactEmail}</strong>.
                                    You can also wait here for real-time approval updates.
                                </p>

                                {statusCheckInterval && (
                                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                                        <div className="flex items-center justify-center space-x-2">
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                                            <span className="text-blue-800">Checking approval status every 5 seconds...</span>
                                        </div>
                                        <button
                                            onClick={handleStopChecking}
                                            className="mt-2 text-blue-600 hover:text-blue-800 text-sm underline"
                                        >
                                            Stop checking
                                        </button>
                                    </div>
                                )}

                                <button
                                    onClick={resetForm}
                                    className="bg-gray-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-700 focus:ring-4 focus:ring-gray-300 transition-colors"
                                >
                                    Register Another Application
                                </button>
                            </div>
                        ) : (
                            <div className="text-center">
                                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
                                    <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </div>

                                <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                                    Registration Failed
                                </h2>

                                <p className="text-red-600 mb-6">
                                    {registrationResult.error || 'An unexpected error occurred. Please try again.'}
                                </p>

                                <button
                                    onClick={() => setRegistrationResult(null)}
                                    className="bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 transition-colors"
                                >
                                    Try Again
                                </button>
                            </div>
                        )}
                    </div>
                )}

                {/* Approval Status */}
                {approvalStatus && (
                    <div className="bg-white rounded-2xl shadow-xl p-8">
                        <div className="text-center">
                            {approvalStatus.status === 'APPROVED' ? (
                                <>
                                    <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
                                        <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>

                                    <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                                        🎉 Application Approved!
                                    </h2>

                                    <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                                        <h3 className="text-lg font-medium text-green-800 mb-4">Your API Credentials</h3>

                                        <div className="space-y-4">
                                            <div>
                                                <label className="block text-sm font-medium text-green-700 mb-2">API Key</label>
                                                <div className="flex items-center space-x-2">
                                                    <input
                                                        type="text"
                                                        value={approvalStatus.apiKey || ''}
                                                        readOnly
                                                        className="flex-1 px-4 py-3 bg-white border border-green-300 rounded-lg font-mono text-sm text-green-900"
                                                    />
                                                    <button
                                                        onClick={() => navigator.clipboard.writeText(approvalStatus.apiKey || '')}
                                                        className="px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-4 focus:ring-green-300 transition-colors"
                                                    >
                                                        Copy
                                                    </button>
                                                </div>
                                                <p className="text-sm text-green-600 mt-1">
                                                    Keep this API key secure and use it in all your API requests
                                                </p>
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-green-700 mb-2">App ID</label>
                                                <input
                                                    type="text"
                                                    value={approvalStatus.appId}
                                                    readOnly
                                                    className="w-full px-4 py-3 bg-white border border-green-300 rounded-lg font-mono text-sm text-green-900"
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                                        <h3 className="text-lg font-medium text-blue-800 mb-4">Next Steps</h3>
                                        <div className="text-left space-y-2 text-blue-700">
                                            <p>1. <strong>Copy your API key</strong> above</p>
                                            <p>2. <strong>Use it in your API requests</strong> like this:</p>
                                            <div className="bg-blue-100 p-3 rounded font-mono text-sm">
                                                curl -H "Authorization: Bearer YOUR_API_KEY" \<br />
                                                &nbsp;&nbsp;&nbsp;&nbsp;https://your-domain.com/api/notifications
                                            </div>
                                            <p>3. <strong>Check our API documentation</strong> for integration details</p>
                                        </div>
                                    </div>

                                    <p className="text-gray-600 mb-6">
                                        We've also sent these details to <strong>{formData.contactEmail}</strong> for your records.
                                    </p>
                                </>
                            ) : approvalStatus.status === 'REJECTED' ? (
                                <>
                                    <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
                                        <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </div>

                                    <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                                        Application Rejected
                                    </h2>

                                    {approvalStatus.adminNotes && (
                                        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                                            <h3 className="text-lg font-medium text-red-800 mb-2">Reason for Rejection</h3>
                                            <p className="text-red-700">{approvalStatus.adminNotes}</p>
                                        </div>
                                    )}

                                    <p className="text-gray-600 mb-6">
                                        Your application has been rejected. You can submit a new application with updated information.
                                    </p>
                                </>
                            ) : null}
                        </div>
                    </div>
                )}

                {/* Footer */}
                <div className="text-center text-gray-500 mt-12">
                    <p>
                        Need help? Contact us at{' '}
                        <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800">
                            <EMAIL>
                        </a>
                    </p>
                </div>
            </div>
        </div>
    );
} 