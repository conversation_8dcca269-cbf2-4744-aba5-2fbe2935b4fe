'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function DemoPage() {
  const [notifications, setNotifications] = useState<any[]>([]);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    type: 'INFO',
    priority: 'NORMAL',
    channels: {
      inApp: true,
      email: false,
      sms: false,
      push: false,
    }
  });
  const [isRegistered, setIsRegistered] = useState(false);
  const [registrationStatus, setRegistrationStatus] = useState('');
  const [apiKey, setApiKey] = useState('');
  const [appId, setAppId] = useState('');

  const registerDemoApp = async () => {
    try {
      setRegistrationStatus('Submitting app registration request...');

      // Submit app registration request (no API key required)
      const response = await fetch('/api/applications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          appId: 'demo-app',
          name: 'Demo Application',
          description: 'Demo app for testing the secure notification system',
          contactEmail: '<EMAIL>',
          contactName: 'Demo Developer',
          website: 'https://demo.example.com',
          businessType: 'Demo Application',
          expectedVolume: 100,
          providers: {
            novu: {
              apiKey: 'demo-novu-key',
              appId: 'demo-novu-app',
              priority: 1,
              supportedChannels: ['email', 'sms', 'push', 'inApp']
            },
            resend: {
              apiKey: 'demo-resend-key',
              fromEmail: '<EMAIL>',
              priority: 2,
              supportedChannels: ['email']
            }
          },
          fallbackStrategy: 'failover',
          maxRetries: 3,
          retryDelay: 1000,
          rateLimits: {
            requestsPerMinute: 10,
            requestsPerHour: 100,
            requestsPerDay: 1000
          }
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setRegistrationStatus(`App registration submitted! Status: ${result.data.status}. Waiting for admin approval...`);

        // For demo purposes, simulate admin approval after 3 seconds
        setTimeout(() => {
          simulateAdminApproval();
        }, 3000);
      } else {
        const error = await response.json();
        setRegistrationStatus(`Registration failed: ${error.error}`);
      }
    } catch (error) {
      setRegistrationStatus('Error submitting registration');
      console.error('Error registering demo app:', error);
    }
  };

  const simulateAdminApproval = async () => {
    try {
      setRegistrationStatus('Simulating admin approval...');

      // Simulate admin approval (in real system, this would be done by admin)
      const response = await fetch('/api/applications/approve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          appId: 'demo-app',
          action: 'APPROVE',
          reason: 'Demo application approved for testing',
          adminNotes: 'Approved for demonstration purposes',
          adminSecret: process.env.NEXT_PUBLIC_ADMIN_SECRET || 'demo-secret'
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setApiKey(result.apiKey);
        setAppId('demo-app');
        setIsRegistered(true);
        setRegistrationStatus(`Demo app approved! API Key: ${result.apiKey}`);
      } else {
        const error = await response.json();
        setRegistrationStatus(`Approval failed: ${error.error}`);
      }
    } catch (error) {
      setRegistrationStatus('Error during approval');
      console.error('Error during approval:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isRegistered || !apiKey) {
      alert('Please complete app registration first');
      return;
    }

    try {
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'X-App-ID': appId
        },
        body: JSON.stringify({
          appId: appId,
          userId: 'demo-user',
          title: formData.title,
          message: formData.content,
          type: formData.type,
          priority: formData.priority,
          channels: formData.channels,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`Failed to create notification: ${error.error}`);
      }

      const result = await response.json();

      if (result.success) {
        setNotifications(prev => [{
          id: result.data.id,
          title: formData.title,
          content: formData.content,
          type: formData.type,
          priority: formData.priority,
          status: result.data.status,
          isRead: false,
          channels: formData.channels,
          createdAt: new Date().toISOString()
        }, ...prev]);

        setFormData({
          title: '',
          content: '',
          type: 'INFO',
          priority: 'NORMAL',
          channels: {
            inApp: true,
            email: false,
            sms: false,
            push: false,
          }
        });
      } else {
        console.error('Failed to create notification:', result.error);
      }
    } catch (error) {
      console.error('Error creating notification:', error);
      alert(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(n =>
        n.id === id ? { ...n, isRead: true, status: 'READ' } : n
      )
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'SENT': return 'bg-blue-100 text-blue-800';
      case 'READ': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW': return 'bg-gray-100 text-gray-800';
      case 'NORMAL': return 'bg-blue-100 text-blue-800';
      case 'HIGH': return 'bg-orange-100 text-orange-800';
      case 'URGENT': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Secure Notification System Demo
          </h1>
          <p className="text-xl text-gray-600">
            Experience the new secure app registration system with admin approval workflow and API key authentication.
          </p>
        </div>

        {!isRegistered && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Secure App Registration</h2>
            <p className="text-gray-600 mb-6">
              This demo shows the new secure registration flow. Your app registration will be submitted for admin approval,
              and once approved, you'll receive an API key to authenticate all future requests.
            </p>
            <button
              onClick={registerDemoApp}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              Submit App Registration
            </button>
            {registrationStatus && (
              <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-800">{registrationStatus}</p>
              </div>
            )}
          </div>
        )}

        {isRegistered && apiKey && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">App Registration Complete</h2>
            <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
              <h3 className="text-lg font-medium text-green-800 mb-2">✅ Application Approved</h3>
              <p className="text-green-700 mb-3">
                Your demo app has been approved and is now ready to send notifications using the secure API key.
              </p>
              <div className="bg-gray-900 rounded-lg p-3 text-green-400 font-mono text-sm">
                <div>App ID: {appId}</div>
                <div>API Key: {apiKey}</div>
              </div>
            </div>
            <p className="text-sm text-gray-600">
              <strong>Note:</strong> In a real application, you would store this API key securely in your environment variables
              and never expose it in client-side code.
            </p>
          </div>
        )}

        {isRegistered && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">Send Secure Notification</h2>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Title
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Notification title"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Type
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="INFO">Info</option>
                    <option value="WARNING">Warning</option>
                    <option value="ERROR">Error</option>
                    <option value="WELCOME">Welcome</option>
                    <option value="REMINDER">Reminder</option>
                  </select>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Content
                </label>
                <textarea
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Notification content"
                  required
                />
              </div>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Priority
                  </label>
                  <select
                    value={formData.priority}
                    onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="LOW">Low</option>
                    <option value="NORMAL">Normal</option>
                    <option value="HIGH">High</option>
                    <option value="URGENT">Urgent</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Delivery Channels
                  </label>
                  <div className="space-y-2">
                    {Object.entries(formData.channels).map(([channel, enabled]) => (
                      <label key={channel} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={enabled}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            channels: { ...prev.channels, [channel]: e.target.checked }
                          }))}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-700 capitalize">{channel}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
              <button
                type="submit"
                className="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Send Secure Notification
              </button>
            </form>
          </div>
        )}

        {notifications.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">Notifications Sent</h2>
            <div className="space-y-4">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 rounded-lg border ${notification.isRead ? 'bg-gray-50 border-gray-200' : 'bg-white border-blue-200'
                    }`}
                >
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-medium text-gray-900">
                      {notification.title}
                    </h3>
                    <div className="flex space-x-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(notification.priority)}`}>
                        {notification.priority}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(notification.status)}`}>
                        {notification.status}
                      </span>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-3">{notification.content}</p>
                  <div className="flex justify-between items-center text-sm text-gray-500">
                    <span>Type: {notification.type}</span>
                    <span>Channels: {Object.keys(notification.channels || {}).filter(k => notification.channels[k]).join(', ')}</span>
                  </div>
                  <div className="flex justify-end space-x-2 mt-3">
                    {!notification.isRead && (
                      <button
                        onClick={() => markAsRead(notification.id)}
                        className="px-3 py-1 text-sm bg-green-100 text-green-800 rounded hover:bg-green-200"
                      >
                        Mark as Read
                      </button>
                    )}
                    <button
                      onClick={() => deleteNotification(notification.id)}
                      className="px-3 py-1 text-sm bg-red-100 text-red-800 rounded hover:bg-red-200"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mt-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">How the Secure System Works</h2>
          <div className="space-y-4 text-gray-600">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-blue-600 text-sm font-medium">1</span>
              </div>
              <div>
                <strong>App Registration:</strong> Submit your app details with provider credentials. No API key required at this stage.
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-blue-600 text-sm font-medium">2</span>
              </div>
              <div>
                <strong>Admin Review:</strong> System administrators review your application and business details.
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-blue-600 text-sm font-medium">3</span>
              </div>
              <div>
                <strong>Approval & API Key:</strong> Once approved, you receive a secure API key for authentication.
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-blue-600 text-sm font-medium">4</span>
              </div>
              <div>
                <strong>Secure Usage:</strong> Use your API key in all future requests. The system validates your key and enforces rate limits.
              </div>
            </div>
          </div>
        </div>

        <div className="text-center mt-12">
          <Link
            href="/docs"
            className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            ← Back to Documentation
          </Link>
        </div>
      </div>
    </div>
  );
}

