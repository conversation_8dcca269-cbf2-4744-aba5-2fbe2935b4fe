export default function ReactDocs() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">React Package Documentation</h1>
        <p className="text-lg text-gray-600">
          React components and hooks for integrating notifications into your React applications with ease.
          This package provides seamless integration with the unified notification service.
        </p>
      </div>

      {/* Installation */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Installation</h2>
        <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
          <code className="text-green-400 font-mono text-sm">
            npm install @sparkstrand/notifications-react
          </code>
        </pre>
        <p className="text-gray-600 mt-3">
          This package requires @sparkstrand/notifications-core as a peer dependency and integrates with the unified notification service.
        </p>
      </section>

      {/* App Setup */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Setting Up Your App</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">App Registration</h3>
          <p className="text-gray-600 mb-4">
            Before using notifications, your app must register with the notification service using your own provider API keys.
            This ensures complete isolation from other applications.
          </p>
          <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <code className="text-green-400 font-mono text-sm whitespace-pre">
{`// In your app initialization
import {notificationService} from '@sparkstrand/notifications-core';

await notificationService.registerApp({
  appId: 'sporty-expats',
  name: 'SportyExpats',
  providers: {
    novu: {
      apiKey: process.env.SPORTY_EXPATS_NOVU_API_KEY,
      appId: process.env.SPORTY_EXPATS_NOVU_APP_ID,
      priority: 1
    },
    resend: {
      apiKey: process.env.SPORTY_EXPATS_RESEND_API_KEY,
      fromEmail: '<EMAIL>',
      priority: 2
    }
  }
});`}
            </code>
          </pre>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Provider Configuration</h3>
          <p className="text-gray-600 mb-3">
            Each app manages its own provider credentials:
          </p>
          <ul className="text-gray-600 space-y-2">
            <li>• <strong>Novu:</strong> For push notifications, in-app notifications, and multi-channel delivery</li>
            <li>• <strong>Resend:</strong> For direct email delivery with custom templates</li>
            <li>• <strong>Twilio:</strong> For SMS notifications (when implemented)</li>
            <li>• <strong>Custom providers:</strong> Add your own notification providers</li>
          </ul>
        </div>
      </section>

      {/* Features */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Key Features</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <ul className="space-y-3 text-gray-700">
            <li>• <strong>Unified Service Integration:</strong> Seamless integration with the notification service</li>
            <li>• <strong>React Hooks:</strong> Easy-to-use hooks for notification management</li>
            <li>• <strong>Real-time Updates:</strong> Automatic notification updates and real-time delivery status</li>
            <li>• <strong>TypeScript Support:</strong> Full type safety and IntelliSense</li>
            <li>• <strong>Customizable Components:</strong> Flexible notification components with theming support</li>
            <li>• <strong>Provider Fallback:</strong> Automatic fallback to alternative providers if primary fails</li>
            <li>• <strong>Performance Optimized:</strong> Efficient rendering and minimal re-renders</li>
          </ul>
        </div>
      </section>

      {/* Core Components */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Core Components</h2>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">NotificationCenter</h3>
            <p className="text-gray-600 mb-3">
              Main notification display component that shows all notifications for the current user.
            </p>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`<NotificationCenter
  maxNotifications={5}
  showUnreadBadge
  onNotificationClick={handleClick}
/>`}
              </code>
            </pre>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">NotificationBadge</h3>
            <p className="text-gray-600 mb-3">
              Unread notification counter that can be placed anywhere in your app.
            </p>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`<NotificationBadge
  count={unreadCount}
  maxCount={99}
  variant="dot"
/>`}
              </code>
            </pre>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">NotificationItem</h3>
            <p className="text-gray-600 mb-3">
              Individual notification display component with customizable styling.
            </p>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`<NotificationItem
  notification={notification}
  onMarkAsRead={handleMarkAsRead}
  onDelete={handleDelete}
/>`}
              </code>
            </pre>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">NotificationList</h3>
            <p className="text-gray-600 mb-3">
              Scrollable list of notifications with pagination and filtering support.
            </p>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`<NotificationList
  notifications={notifications}
  pageSize={20}
  onLoadMore={loadMore}
/>`}
              </code>
            </pre>
          </div>
        </div>
      </section>

      {/* Hooks */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">React Hooks</h2>

        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">useNotifications</h3>
            <p className="text-gray-600 mb-3">
              Main hook for managing notifications in your React components.
            </p>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`const {
  notifications,
  unreadCount,
  markAsRead,
  deleteNotification,
  refreshNotifications
} = useNotifications();`}
              </code>
            </pre>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">useNotificationPreferences</h3>
            <p className="text-gray-600 mb-3">
              Hook for managing user notification preferences and settings.
            </p>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`const {
  preferences,
  updatePreferences,
  isLoading
} = useNotificationPreferences();`}
              </code>
            </pre>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">useNotificationService</h3>
            <p className="text-gray-600 mb-3">
              Direct access to the notification service for advanced operations.
            </p>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`const {
  sendNotification,
  sendBulkNotifications,
  getNotificationStats
} = useNotificationService();`}
              </code>
            </pre>
          </div>
        </div>
      </section>

      {/* Usage Examples */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Usage Examples</h2>

        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Basic Setup</h3>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`import {useNotifications, NotificationCenter} from
  '@sparkstrand/notifications-react';

function App() {
  const {notifications, unreadCount} = useNotifications();

  return (
    <div>
      <NotificationBadge count={unreadCount} />
      <NotificationCenter />
      <YourApp />
    </div>
  );
}`}
              </code>
            </pre>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Sending Notifications</h3>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`import {useNotificationService} from '@sparkstrand/notifications-react';

function NotificationSender() {
  const {sendNotification} = useNotificationService();

  const handleSend = async () => {
    await sendNotification(
      'user-123',
      'INFO',
      'Welcome!',
      'Thanks for joining our platform',
      { email: '<EMAIL>' },
      { appId: 'sporty-expats' }
    );
  };

  return <button onClick={handleSend}>Send Notification</button>;
}`}
              </code>
            </pre>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Custom Notification Component</h3>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`function CustomNotification({notification}) {
  const {markAsRead, deleteNotification} = useNotifications();

  return (
    <div className="custom-notification">
      <h3>{notification.title}</h3>
      <p>{notification.content}</p>
      <div className="actions">
        <button onClick={() => markAsRead(notification.id)}>
          Mark Read
        </button>
        <button onClick={() => deleteNotification(notification.id)}>
          Delete
        </button>
      </div>
    </div>
  );
}`}
              </code>
            </pre>
          </div>
        </div>
      </section>

      {/* Customization */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Customization</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Theming & Styling</h3>
          <p className="text-gray-600 mb-3">
            All components support extensive customization through CSS classes and theme props:
          </p>
          <ul className="text-gray-600 space-y-2">
            <li>• <strong>CSS Custom Properties:</strong> Customize colors, spacing, and typography</li>
            <li>• <strong>Theme Props:</strong> Pass custom themes to components</li>
            <li>• <strong>CSS Modules:</strong> Use CSS modules for component-specific styling</li>
            <li>• <strong>Styled Components:</strong> Integrate with styled-components or emotion</li>
            <li>• <strong>Tailwind CSS:</strong> Full Tailwind CSS support with custom classes</li>
          </ul>
        </div>
      </section>

      {/* Best Practices */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Best Practices</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <ul className="space-y-3 text-gray-700">
            <li>• <strong>Register early:</strong> Register your app during application startup</li>
            <li>• <strong>Use hooks appropriately:</strong> Choose the right hook for your use case</li>
            <li>• <strong>Handle loading states:</strong> Show loading indicators while notifications are being fetched</li>
            <li>• <strong>Error boundaries:</strong> Wrap notification components in error boundaries</li>
            <li>• <strong>Performance optimization:</strong> Use React.memo for notification components when appropriate</li>
            <li>• <strong>Accessibility:</strong> Ensure notification components are accessible to screen readers</li>
            <li>• <strong>Testing:</strong> Test notification components with different data and states</li>
          </ul>
        </div>
      </section>
    </div>
  );
}
