export default function CoreDocs() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Core Package Documentation</h1>
        <p className="text-lg text-gray-600">
          The foundation of the Spark Strand notifications system. This package provides a unified API for
          notification management with automatic provider routing and fallback strategies.
        </p>
      </div>

      {/* Installation */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Installation</h2>
        <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
          <code className="text-green-400 font-mono text-sm">
            npm install @sparkstrand/notifications-core
          </code>
        </pre>
        <p className="text-gray-600 mt-3">
          The core package provides the unified notification service and is a peer dependency for all other notification packages.
        </p>
      </section>

      {/* Features */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Key Features</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <ul className="space-y-3 text-gray-700">
            <li>• <strong>Unified API:</strong> Single service interface for all notification operations</li>
            <li>• <strong>App Management:</strong> Support for multiple independent applications</li>
            <li>• <strong>Automatic provider routing:</strong> Smart selection of best available provider</li>
            <li>• <strong>Fallback strategies:</strong> Failover, parallel, and hybrid delivery approaches</li>
            <li>• <strong>Provider abstraction:</strong> Switch between Novu, Resend, Twilio, or custom providers</li>
            <li>• <strong>TypeScript support:</strong> Full type safety and IntelliSense</li>
            <li>• <strong>Batch operations:</strong> Send multiple notifications efficiently</li>
            <li>• <strong>Real-time monitoring:</strong> Provider status, metrics, and delivery analytics</li>
          </ul>
        </div>
      </section>

      {/* App Setup */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Setting Up Your App</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">How It Works</h3>
          <p className="text-gray-600 mb-4">
            Your app registers with the notification service using your own provider API keys.
            The system creates isolated provider instances for your app and automatically routes notifications
            through the best available provider for each channel.
          </p>
          <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <code className="text-green-400 font-mono text-sm whitespace-pre">
{`// App registration with provider keys
await notificationService.registerApp({
  appId: 'sporty-expats',
  name: 'SportyExpats',
  providers: {
    novu: { apiKey: 'novu-key', appId: 'novu-app' },
    resend: { apiKey: 'resend-key', fromEmail: '<EMAIL>' }
  }
});`}
            </code>
          </pre>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Provider Isolation</h3>
          <p className="text-gray-600 mb-3">
            Each registered app gets its own isolated provider instances:
          </p>
          <ul className="text-gray-600 space-y-2">
            <li>• <strong>sporty-expats-novu:</strong> Novu provider with SportyExpats' API keys</li>
            <li>• <strong>sporty-expats-resend:</strong> Resend provider with SportyExpats' API keys</li>
            <li>• <strong>tax-done-novu:</strong> Novu provider with TaxDone's API keys</li>
            <li>• <strong>venture-direction-twilio:</strong> Twilio provider with VentureDirection's credentials</li>
          </ul>
        </div>
      </section>

      {/* Supported Providers */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Supported Providers</h2>

        <div className="grid md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Novu</h3>
            <p className="text-gray-600 mb-3">Full-featured notification platform</p>
            <ul className="text-gray-600 space-y-2">
              <li>• Email notifications</li>
              <li>• SMS via Twilio</li>
              <li>• Push notifications</li>
              <li>• In-app notifications</li>
              <li>• Chat integrations</li>
            </ul>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Resend</h3>
            <p className="text-gray-600 mb-3">Modern email API for developers</p>
            <ul className="text-gray-600 space-y-2">
              <li>• Transactional emails</li>
              <li>• HTML email templates</li>
              <li>• Email analytics</li>
              <li>• Domain verification</li>
              <li>• High deliverability</li>
            </ul>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Twilio</h3>
            <p className="text-gray-600 mb-3">SMS and voice communications</p>
            <ul className="text-gray-600 space-y-2">
              <li>• SMS delivery</li>
              <li>• Voice calls</li>
              <li>• WhatsApp Business</li>
              <li>• Global coverage</li>
              <li>• Delivery receipts</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Provider Routing */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Provider Routing & Fallbacks</h2>

        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Automatic Routing</h3>
            <p className="text-gray-600 mb-3">
              The system automatically selects the best provider for each notification based on:
            </p>
            <ul className="text-gray-600 space-y-2">
              <li>• <strong>Channel support:</strong> Provider must support the requested delivery channel</li>
              <li>• <strong>Priority ranking:</strong> Lower priority numbers = higher priority</li>
              <li>• <strong>Provider status:</strong> Only enabled and healthy providers are considered</li>
              <li>• <strong>Rate limits:</strong> Providers within their rate limits are preferred</li>
            </ul>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Fallback Strategies</h3>
            <div className="grid md:grid-cols-3 gap-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Failover</h4>
                <p className="text-gray-600 text-sm">Try primary provider, fall back to secondary if it fails</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Parallel</h4>
                <p className="text-gray-600 text-sm">Send via multiple providers simultaneously for redundancy</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Hybrid</h4>
                <p className="text-gray-600 text-sm">Combine strategies based on notification priority and channel</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Usage Examples */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Usage Examples</h2>

        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Basic Notification</h3>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`// Send a simple notification
const result = await notificationService.sendNotification(
  'user-123',
  'INFO',
  'Welcome!',
  'Thanks for joining our platform',
  { email: '<EMAIL>' }
);`}
              </code>
            </pre>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Bulk Notifications</h3>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`// Send multiple notifications efficiently
const results = await notificationService.sendBulkNotifications({
  appId: 'sporty-expats',
  notifications: [
    { userId: 'user-1', type: 'INFO', title: 'Event Reminder', content: '...' },
    { userId: 'user-2', type: 'INFO', title: 'Event Reminder', content: '...' }
  ],
  globalOptions: { channels: { email: true, inApp: true } }
});`}
              </code>
            </pre>
          </div>
        </div>
      </section>

      {/* Best Practices */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Best Practices</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <ul className="space-y-3 text-gray-700">
            <li>• <strong>Register early:</strong> Register your app during application startup</li>
            <li>• <strong>Provider priority:</strong> Set appropriate priority levels for your providers</li>
            <li>• <strong>Fallback strategy:</strong> Choose the right fallback strategy for your use case</li>
            <li>• <strong>Rate limits:</strong> Configure appropriate rate limits per app and provider</li>
            <li>• <strong>Error handling:</strong> Always wrap notification calls in try-catch blocks</li>
            <li>• <strong>Monitoring:</strong> Use the built-in metrics and status endpoints</li>
            <li>• <strong>Testing:</strong> Test with multiple providers to ensure fallback works</li>
          </ul>
        </div>
      </section>
    </div>
  );
}
