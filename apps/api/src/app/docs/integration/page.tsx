export default function IntegrationGuide() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Integration Guide</h1>
        <p className="text-lg text-gray-600">
          Step-by-step guides for integrating the Spark Strand notifications system into your applications.
          The system uses a self-service architecture where each app manages its own provider credentials.
        </p>
      </div>

      {/* Quick Start */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Quick Start</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">1. Install Core Package</h3>
          <p className="text-gray-600 mb-3">
            Start by installing the core notification package in your application.
          </p>
          <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <code className="text-green-400 font-mono text-sm">
              npm install @sparkstrand/notifications-core
            </code>
          </pre>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6 mt-4">
          <h3 className="text-lg font-medium text-gray-900 mb-3">2. Configure Your Providers</h3>
          <p className="text-gray-600 mb-3">
            Set up your own provider API keys in your application's environment variables.
          </p>
          <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <code className="text-green-400 font-mono text-sm whitespace-pre">
{`# Your app's .env file
SPORTY_EXPATS_NOVU_API_KEY=sk_123...
SPORTY_EXPATS_NOVU_APP_ID=novu-app-id
SPORTY_EXPATS_RESEND_API_KEY=re_456...
SPORTY_EXPATS_FROM_EMAIL=<EMAIL>`}
            </code>
          </pre>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6 mt-4">
          <h3 className="text-lg font-medium text-gray-900 mb-3">3. Register Your App</h3>
          <p className="text-gray-600 mb-3">
            Register your application with the notification service during startup.
          </p>
          <div className="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm">
            <div>import {notificationService} from '@sparkstrand/notifications-core';</div>
            <div></div>
            <div>await notificationService.registerApp({`{`}</div>
            <div>  appId: 'sporty-expats',</div>
            <div>  name: 'SportyExpats',</div>
            <div>  providers: {`{`}</div>
            <div>    novu: {`{`} apiKey: process.env.SPORTY_EXPATS_NOVU_API_KEY, appId: process.env.SPORTY_EXPATS_NOVU_APP_ID {`}`},</div>
            <div>    resend: {`{`} apiKey: process.env.SPORTY_EXPATS_RESEND_API_KEY, fromEmail: process.env.SPORTY_EXPATS_FROM_EMAIL {`}`}</div>
            <div>  {`}`}</div>
            <div>{`}`});</div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">How It Works</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">App Registration</h3>
          <p className="text-gray-600 mb-4">
            Each application registers with the notification service using its own provider API keys.
            The system creates isolated provider instances per app and automatically routes notifications
            through the best available provider for each channel.
          </p>
          <div className="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm">
            <div>// App A: SportyExpats</div>
            <div>sporty-expats-novu (with SportyExpats' Novu keys)</div>
            <div>sporty-expats-resend (with SportyExpats' Resend keys)</div>
            <div></div>
            <div>// App B: TaxDone</div>
            <div>tax-done-novu (with TaxDone's Novu keys)</div>
            <div>tax-done-twilio (with TaxDone's Twilio keys)</div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Benefits</h3>
          <ul className="text-gray-600 space-y-2">
            <li>• <strong>Complete isolation:</strong> Each app's data and credentials are completely separate</li>
            <li>• <strong>Independent deployment:</strong> Apps can be deployed and scaled independently</li>
            <li>• <strong>Provider flexibility:</strong> Each app can use different providers and configurations</li>
            <li>• <strong>Security:</strong> No shared credentials or cross-app data access</li>
            <li>• <strong>Scalability:</strong> Apps can scale their notification usage independently</li>
          </ul>
        </div>
      </section>

      {/* Platform Integration */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Platform Integration</h2>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Web Applications</h3>
            <ul className="text-gray-600 space-y-2">
              <li>• React applications</li>
              <li>• Vue.js applications</li>
              <li>• Angular applications</li>
              <li>• Vanilla JavaScript</li>
            </ul>
            <div className="mt-3">
              <div className="bg-gray-900 rounded-lg p-3 text-green-400 font-mono text-sm">
                npm install @sparkstrand/notifications-react
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Mobile Applications</h3>
            <ul className="text-gray-600 space-y-2">
              <li>• React Native</li>
              <li>• Native iOS (Swift)</li>
              <li>• Native Android (Kotlin)</li>
              <li>• Flutter</li>
            </ul>
            <div className="mt-3">
              <div className="bg-gray-900 rounded-lg p-3 text-green-400 font-mono text-sm">
                npm install @sparkstrand/notifications-react-native
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Desktop Applications</h3>
            <ul className="text-gray-600 space-y-2">
              <li>• Electron applications</li>
              <li>• Native macOS apps</li>
              <li>• Native Windows apps</li>
              <li>• Cross-platform frameworks</li>
            </ul>
            <div className="mt-3">
              <div className="bg-gray-900 rounded-lg p-3 text-green-400 font-mono text-sm">
                npm install @sparkstrand/notifications-electron
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Backend Services</h3>
            <ul className="text-gray-600 space-y-2">
              <li>• Node.js/Express</li>
              <li>• Python/Django</li>
              <li>• Go applications</li>
              <li>• Java/Spring Boot</li>
            </ul>
            <div className="mt-3">
              <div className="bg-gray-900 rounded-lg p-3 text-green-400 font-mono text-sm">
                npm install @sparkstrand/notifications-core
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Provider Configuration */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Provider Configuration</h2>

        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Novu Provider</h3>
            <p className="text-gray-600 mb-3">
              Full-featured notification platform supporting multiple channels.
            </p>
            <div className="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm">
              <div>novu: {`{`}</div>
              <div>  apiKey: 'your-novu-api-key',</div>
              <div>  appId: 'your-novu-app-id',</div>
              <div>  priority: 1, // Lower number = higher priority</div>
              <div>  supportedChannels: ['email', 'sms', 'push', 'inApp']</div>
              <div>{`}`}</div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Resend Provider</h3>
            <p className="text-gray-600 mb-3">
              Modern email API for transactional emails with high deliverability.
            </p>
            <div className="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm">
              <div>resend: {`{`}</div>
              <div>  apiKey: 'your-resend-api-key',</div>
              <div>  fromEmail: '<EMAIL>',</div>
              <div>  priority: 2,</div>
              <div>  supportedChannels: ['email']</div>
              <div>{`}`}</div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Twilio Provider</h3>
            <p className="text-gray-600 mb-3">
              SMS and voice communications with global coverage.
            </p>
            <div className="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm">
              <div>twilio: {`{`}</div>
              <div>  accountSid: 'your-twilio-account-sid',</div>
              <div>  authToken: 'your-twilio-auth-token',</div>
              <div>  fromPhoneNumber: '+**********',</div>
              <div>  priority: 3,</div>
              <div>  supportedChannels: ['sms']</div>
              <div>{`}`}</div>
            </div>
          </div>
        </div>
      </section>

      {/* Usage Examples */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Usage Examples</h2>

        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Basic Notification</h3>
            <div className="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm">
              <div>// Send a simple notification</div>
              <div>const result = await notificationService.sendNotification(</div>
              <div>  'user-123',</div>
              <div>  'INFO',</div>
              <div>  'Welcome!',</div>
              <div>  'Thanks for joining our platform',</div>
              <div>  {`{`} email: '<EMAIL>' {`}`},</div>
              <div>  {`{`} appId: 'sporty-expats' {`}`}</div>
              <div>);</div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Bulk Notifications</h3>
            <div className="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm">
              <div>// Send multiple notifications efficiently</div>
              <div>const results = await notificationService.sendBulkNotifications({`{`}</div>
              <div>  appId: 'sporty-expats',</div>
              <div>  notifications: [</div>
              <div>    {`{`} userId: 'user-1', type: 'INFO', title: 'Event Reminder', content: '...' {`}`},</div>
              <div>    {`{`} userId: 'user-2', type: 'INFO', title: 'Event Reminder', content: '...' {`}`}</div>
              <div>  ],</div>
              <div>  globalOptions: {`{`} channels: {`{`} email: true, inApp: true {`}`} {`}`}</div>
              <div>{`}`});</div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">User Preferences</h3>
            <div className="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm">
              <div>// Update user notification preferences</div>
              <div>await notificationService.updateUserPreferences(</div>
              <div>  'sporty-expats',</div>
              <div>  'user-123',</div>
              <div>  {`{`}</div>
              <div>    email: true,</div>
              <div>    sms: false,</div>
              <div>    push: true,</div>
              <div>    inApp: true</div>
              <div>  {`}`}</div>
              <div>);</div>
            </div>
          </div>
        </div>
      </section>

      {/* Testing */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Testing</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Test Environment</h3>
          <p className="text-gray-600 mb-3">
            Test your integration using the demo environment:
          </p>
          <ul className="text-gray-600 space-y-2">
            <li>• <strong>Demo API:</strong> https://demo.notifications.sparkstrand.com/api</li>
            <li>• <strong>Test credentials:</strong> Use your own provider test keys</li>
            <li>• <strong>Sandbox mode:</strong> All notifications are logged but not delivered</li>
            <li>• <strong>Rate limits:</strong> Higher limits for testing purposes</li>
          </ul>
        </div>
      </section>

      {/* Best Practices */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Best Practices</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <ul className="space-y-3 text-gray-700">
            <li>• <strong>Register early:</strong> Register your app during application startup</li>
            <li>• <strong>Provider priority:</strong> Set appropriate priority levels for your providers</li>
            <li>• <strong>Fallback strategy:</strong> Choose the right fallback strategy for your use case</li>
            <li>• <strong>Rate limits:</strong> Configure appropriate rate limits per app and provider</li>
            <li>• <strong>Error handling:</strong> Always wrap notification calls in try-catch blocks</li>
            <li>• <strong>Testing:</strong> Test with multiple providers to ensure fallback works</li>
            <li>• <strong>Monitoring:</strong> Use the built-in metrics and status endpoints</li>
          </ul>
        </div>
      </section>
    </div>
  );
}
