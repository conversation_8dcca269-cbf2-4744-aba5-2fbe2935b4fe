import Link from 'next/link';

export default function DocsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-semibold text-gray-900">
                ← Back to Home
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/docs/core" className="text-gray-600 hover:text-gray-900">
                Core
              </Link>
              <Link href="/docs/react" className="text-gray-600 hover:text-gray-900">
                React
              </Link>
              <Link href="/docs/electron" className="text-gray-600 hover:text-gray-900">
                Electron
              </Link>
              <Link href="/docs/react-native" className="text-gray-600 hover:text-gray-900">
                React Native
              </Link>
              <Link href="/docs/api" className="text-gray-600 hover:text-gray-900">
                API
              </Link>
              <Link href="/docs/integration" className="text-gray-600 hover:text-gray-900">
                Integration
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Documentation Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </div>
    </div>
  );
}

