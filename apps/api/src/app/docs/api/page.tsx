export default function APIDocs() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">API Reference</h1>
        <p className="text-lg text-gray-600">
          Complete API documentation for the Spark Strand notifications service. The API supports independent applications
          where each app manages its own provider credentials and gets isolated endpoints.
        </p>
      </div>

      {/* Base URL */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Base URL</h2>
        <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
          <code className="text-green-400 font-mono text-sm">
            https://notifications.sparkstrand.com/api
          </code>
        </pre>
      </section>

      {/* How It Works */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">How It Works</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">App Registration</h3>
          <p className="text-gray-600 mb-4">
            Each application registers with the notification service using its own provider API keys.
            The system creates isolated provider instances and automatically routes notifications through
            the best available provider for each channel.
          </p>
          <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <code className="text-green-400 font-mono text-sm whitespace-pre">
{`// App registration flow
POST /api/applications/register
→ Creates isolated provider instances
→ Routes notifications per app
→ Provides app-specific endpoints`}
            </code>
          </pre>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Provider Isolation</h3>
          <p className="text-gray-600 mb-3">
            Each registered app gets its own isolated provider instances:
          </p>
          <ul className="text-gray-600 space-y-2">
            <li>• <strong>sporty-expats-novu:</strong> Novu provider with SportyExpats' API keys</li>
            <li>• <strong>sporty-expats-resend:</strong> Resend provider with SportyExpats' API keys</li>
            <li>• <strong>tax-done-novu:</strong> Novu provider with TaxDone's API keys</li>
            <li>• <strong>venture-direction-twilio:</strong> Twilio provider with VentureDirection's credentials</li>
          </ul>
        </div>
      </section>

      {/* Authentication */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Authentication</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">API Key</h3>
          <p className="text-gray-600 mb-3">
            All API requests require an API key to be included in the Authorization header.
          </p>
          <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <code className="text-green-400 font-mono text-sm">
              Authorization: Bearer YOUR_API_KEY
            </code>
          </pre>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Rate Limiting</h3>
          <p className="text-gray-600 mb-3">
            API requests are rate limited per application to prevent abuse.
          </p>
          <ul className="text-gray-600 space-y-2">
            <li>• <strong>Standard:</strong> 100 requests per minute per app</li>
            <li>• <strong>Bulk operations:</strong> 10 requests per minute per app</li>
            <li>• <strong>Health checks:</strong> 1000 requests per minute per app</li>
            <li>• <strong>App registration:</strong> 5 registrations per hour</li>
          </ul>
        </div>
      </section>

      {/* Endpoints */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">API Endpoints</h2>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Health Check</h3>
            <p className="text-gray-600 mb-2">GET /health</p>
            <p className="text-gray-600 text-sm">Check service health, system resources, and provider status</p>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">App Registration</h3>
            <p className="text-gray-600 mb-2">POST /applications/register</p>
            <p className="text-gray-600 text-sm">Register a new application with provider credentials</p>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">App Status</h3>
            <p className="text-gray-600 mb-2">GET /applications/{'{appId}'}/status</p>
            <p className="text-gray-600 text-sm">Get application status and provider health</p>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Send Notification</h3>
            <p className="text-gray-600 mb-2">POST /notifications</p>
            <p className="text-gray-600 text-sm">Send a notification via the unified service</p>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Get Notification</h3>
            <p className="text-gray-600 mb-2">GET /notifications/{'{id}'}</p>
            <p className="text-gray-600 text-sm">Get notification status and delivery details</p>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">List Notifications</h3>
            <p className="text-gray-600 mb-2">GET /notifications</p>
            <p className="text-gray-600 text-sm">List notifications with pagination and filtering</p>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Provider Status</h3>
            <p className="text-gray-600 mb-2">GET /providers/status</p>
            <p className="text-gray-600 text-sm">Get status of all registered providers</p>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Metrics</h3>
            <p className="text-gray-600 mb-2">GET /metrics</p>
            <p className="text-gray-600 text-sm">Get system metrics and delivery statistics</p>
          </div>
        </div>
      </section>

      {/* Request/Response Examples */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Request/Response Examples</h2>

        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">App Registration</h3>
            <div className="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm">
              <div>POST /api/applications/register</div>
              <div></div>
              <div>{`{`}</div>
              <div>  "appId": "sporty-expats",</div>
              <div>  "name": "SportyExpats",</div>
              <div>  "description": "Sports community platform",</div>
              <div>  "providers": {`{`}</div>
              <div>    "novu": {`{`}</div>
              <div>      "apiKey": "novu-api-key",</div>
              <div>      "appId": "novu-app-id",</div>
              <div>      "priority": 1</div>
              <div>    {`}`},</div>
              <div>    "resend": {`{`}</div>
              <div>      "apiKey": "resend-api-key",</div>
              <div>      "fromEmail": "<EMAIL>",</div>
              <div>      "priority": 2</div>
              <div>    {`}`}</div>
              <div>  {`}`}</div>
              <div>{`}`}</div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Send Notification</h3>
            <div className="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm">
              <div>POST /api/notifications</div>
              <div></div>
              <div>{`{`}</div>
              <div>  "appId": "sporty-expats",</div>
              <div>  "userId": "user-123",</div>
              <div>  "type": "WELCOME",</div>
              <div>  "title": "Welcome to SportyExpats!",</div>
              <div>  "content": "We're excited to have you on board.",</div>
              <div>  "channels": {`{`} "email": true, "inApp": true {`}`},</div>
              <div>  "metadata": {`{`}</div>
              <div>    "email": "<EMAIL>",</div>
              <div>    "firstName": "John"</div>
              <div>  {`}`}</div>
              <div>{`}`}</div>
            </div>
          </div>
        </div>
      </section>

      {/* Error Handling */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Error Handling</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Common Error Codes</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="font-medium text-gray-900">VALIDATION_ERROR</span>
              <span className="text-gray-600">Request validation failed</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium text-gray-900">AUTHENTICATION_ERROR</span>
              <span className="text-gray-600">Invalid or missing API key</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium text-gray-900">RATE_LIMIT_EXCEEDED</span>
              <span className="text-gray-600">Rate limit exceeded for this app</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium text-gray-900">APP_NOT_REGISTERED</span>
              <span className="text-gray-600">Application not registered with the service</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium text-gray-900">PROVIDER_UNAVAILABLE</span>
              <span className="text-gray-600">Requested provider is not available</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium text-gray-900">CHANNEL_NOT_SUPPORTED</span>
              <span className="text-gray-600">Delivery channel not supported by app's providers</span>
            </div>
          </div>
        </div>
      </section>

      {/* Monitoring & Analytics */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Monitoring & Analytics</h2>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Provider Status</h3>
            <p className="text-gray-600 mb-3">
              Monitor the health and availability of all registered providers:
            </p>
            <ul className="text-gray-600 space-y-2">
              <li>• Provider health status</li>
              <li>• Rate limit usage</li>
              <li>• Error rates and types</li>
              <li>• Response times</li>
            </ul>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Delivery Metrics</h3>
            <p className="text-gray-600 mb-3">
              Track notification delivery performance:
            </p>
            <ul className="text-gray-600 space-y-2">
              <li>• Delivery success rates</li>
              <li>• Channel performance</li>
              <li>• App-specific statistics</li>
              <li>• Fallback usage</li>
            </ul>
          </div>
        </div>
      </section>
    </div>
  );
}
