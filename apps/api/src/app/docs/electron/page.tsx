import React from 'react';

export default function ElectronDocs() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Electron Package Documentation</h1>
        <p className="text-lg text-gray-600">
          Desktop notification support for Electron applications with system notifications and unified service integration.
          This package provides comprehensive desktop notification capabilities for Windows, macOS, and Linux.
        </p>
      </div>

      {/* Installation */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Installation</h2>
        <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
          <code className="text-green-400 font-mono text-sm">
            npm install @sparkstrand/notifications-electron
          </code>
        </pre>
        <p className="text-gray-600 mt-3">
          This package requires @sparkstrand/notifications-core as a peer dependency and integrates with the unified notification service.
        </p>
      </section>

      {/* App Setup */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Setting Up Your App</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">App Registration</h3>
          <p className="text-gray-600 mb-4">
            Before using notifications, your Electron app must register with the notification service using your own provider API keys.
            This ensures complete isolation from other applications.
          </p>
          <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <code className="text-green-400 font-mono text-sm whitespace-pre">
{`// In your main process initialization
import {notificationService} from '@sparkstrand/notifications-core';

await notificationService.registerApp({
  appId: 'venture-direction',
  name: 'VentureDirection',
  providers: {
    novu: {
      apiKey: process.env.VENTURE_DIRECTION_NOVU_API_KEY,
      appId: process.env.VENTURE_DIRECTION_NOVU_APP_ID,
      priority: 1
    },
    resend: {
      apiKey: process.env.VENTURE_DIRECTION_RESEND_API_KEY,
      fromEmail: '<EMAIL>',
      priority: 2
    }
  }
});`}
            </code>
          </pre>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Provider Configuration</h3>
          <p className="text-gray-600 mb-3">
            Each app manages its own provider credentials:
          </p>
          <ul className="text-gray-600 space-y-2">
            <li>• <strong>Novu:</strong> For push notifications, in-app notifications, and multi-channel delivery</li>
            <li>• <strong>Resend:</strong> For direct email delivery with custom templates</li>
            <li>• <strong>Twilio:</strong> For SMS notifications (when implemented)</li>
            <li>• <strong>Custom providers:</strong> Add your own notification providers</li>
          </ul>
        </div>
      </section>

      {/* Features */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Key Features</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <ul className="space-y-3 text-gray-700">
            <li>• <strong>System Notifications:</strong> Native OS notifications for Windows, macOS, and Linux</li>
            <li>• <strong>Cross-Platform Support:</strong> Consistent API across all operating systems</li>
            <li>• <strong>Notification Actions:</strong> Custom action buttons and quick replies</li>
            <li>• <strong>Sound & Vibration:</strong> Custom notification sounds and haptic feedback</li>
            <li>• <strong>Rich Content:</strong> Support for images, HTML content, and custom styling</li>
            <li>• <strong>Notification Center Integration:</strong> Seamless integration with OS notification centers</li>
            <li>• <strong>Unified Service Integration:</strong> Seamless integration with the notification service</li>
            <li>• <strong>TypeScript Support:</strong> Full type safety and IntelliSense</li>
          </ul>
        </div>
      </section>

      {/* Core Functions */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Core Functions</h2>

        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">setupNotifications</h3>
            <p className="text-gray-600 mb-3">
              Initialize the notification system in your Electron app's main process.
            </p>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`import {setupNotifications} from '@sparkstrand/notifications-electron';

// In your main process
app.whenReady().then(() => {
  setupNotifications({
    appName: 'VentureDirection',
    icon: path.join(__dirname, 'assets/icon.png'),
    defaultSound: true
  });
});`}
              </code>
            </pre>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">showSystemNotification</h3>
            <p className="text-gray-600 mb-3">
              Display a system notification with custom content and actions.
            </p>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`import {showSystemNotification} from '@sparkstrand/notifications-electron';

await showSystemNotification({
  title: 'New Message',
  body: 'You have a new message from John Doe',
  icon: path.join(__dirname, 'assets/message-icon.png'),
  actions: [
    { text: 'Reply', type: 'button' },
    { text: 'Mark as Read', type: 'button' }
  ],
  onClick: () => { /* Handle click */ },
  onAction: (action) => { /* Handle action */ }
});`}
              </code>
            </pre>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">useNotifications</h3>
            <p className="text-gray-600 mb-3">
              React hook for managing notifications in your renderer process.
            </p>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`import {useNotifications} from '@sparkstrand/notifications-electron';

function NotificationComponent() {
  const {
    notifications,
    unreadCount,
    markAsRead,
    deleteNotification
  } = useNotifications();

  return (
    <div>Unread: {unreadCount}</div>
  );
}`}
              </code>
            </pre>
          </div>
        </div>
      </section>

      {/* System Notifications */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">System Notifications</h2>

        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Platform-Specific Features</h3>
            <div className="grid md:grid-cols-3 gap-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Windows</h4>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>• Toast notifications</li>
                  <li>• Action center integration</li>
                  <li>• Custom sounds</li>
                  <li>• Rich content support</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">macOS</h4>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>• Notification Center</li>
                  <li>• Banner and alert styles</li>
                  <li>• Action button support</li>
                  <li>• Sound customization</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Linux</h4>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>• Desktop notifications</li>
                  <li>• Multiple backends</li>
                  <li>• Custom actions</li>
                  <li>• Sound support</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Notification Options</h3>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  tag?: string;
  silent?: boolean;
  sound?: string;
  actions?: NotificationAction[];
  onClick?: () => void;
  onClose?: () => void;
  onAction?: (action: NotificationAction) => void;
}`}
              </code>
            </pre>
          </div>
        </div>
      </section>

      {/* Usage Examples */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Usage Examples</h2>

        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Basic Setup</h3>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`// main.js
import {app} from 'electron';
import {setupNotifications} from '@sparkstrand/notifications-electron';

app.whenReady().then(() => {
  setupNotifications({
    appName: 'MyApp',
    icon: path.join(__dirname, 'assets/icon.png')
  });
});`}
              </code>
            </pre>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Sending Notifications</h3>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`// In your main process
import {showSystemNotification} from '@sparkstrand/notifications-electron';

// Send via unified service
await notificationService.sendNotification(
  'user-123',
  'INFO',
  'New Update Available',
  'Version 2.0 is ready to download',
  { email: '<EMAIL>' },
  { appId: 'venture-direction', channels: { inApp: true } }
);

// Show system notification
await showSystemNotification({
  title: 'New Update Available',
  body: 'Version 2.0 is ready to download',
  actions: [
    { text: 'Download Now', type: 'button' },
    { text: 'Later', type: 'button' }
  ]
});`}
              </code>
            </pre>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Renderer Process Integration</h3>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`// renderer.js
import {useNotifications} from '@sparkstrand/notifications-electron';

function App() {
  const {notifications, unreadCount} = useNotifications();

  return (
    <div>
      <h1>Notifications ({unreadCount})</h1>
      {notifications.map(n => (
        <div key={n.id}>{n.title}</div>
      ))}
    </div>
  );
}`}
              </code>
            </pre>
          </div>
        </div>
      </section>

      {/* Customization */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Customization</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Styling & Theming</h3>
          <p className="text-gray-600 mb-3">
            Customize the appearance and behavior of notifications:
          </p>
          <ul className="text-gray-600 space-y-2">
            <li>• <strong>Custom Icons:</strong> Set app-specific notification icons</li>
            <li>• <strong>Sound Customization:</strong> Custom notification sounds per notification type</li>
            <li>• <strong>Action Buttons:</strong> Custom action buttons with specific behaviors</li>
            <li>• <strong>Notification Duration:</strong> Control how long notifications are displayed</li>
            <li>• <strong>Position Control:</strong> Customize notification positioning on screen</li>
          </ul>
        </div>
      </section>

      {/* Platform-Specific Features */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Platform-Specific Features</h2>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Windows</h3>
            <ul className="text-gray-600 space-y-2">
              <li>• Toast notification API</li>
              <li>• Action center integration</li>
              <li>• Custom sound files</li>
              <li>• Rich content with images</li>
              <li>• Notification grouping</li>
            </ul>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">macOS</h3>
            <ul className="text-gray-600 space-y-2">
              <li>• Notification Center API</li>
              <li>• Banner and alert styles</li>
              <li>• Action button support</li>
              <li>• Sound customization</li>
              <li>• Do Not Disturb integration</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Testing */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Testing</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Testing Notifications</h3>
          <p className="text-gray-600 mb-3">
            Test your notification system across different platforms:
          </p>
          <ul className="text-gray-600 space-y-2">
            <li>• <strong>Cross-Platform Testing:</strong> Test on Windows, macOS, and Linux</li>
            <li>• <strong>Notification Center:</strong> Verify notifications appear in OS notification centers</li>
            <li>• <strong>Action Testing:</strong> Test custom action buttons and click handlers</li>
            <li>• <strong>Sound Testing:</strong> Verify custom sounds work correctly</li>
            <li>• <strong>Integration Testing:</strong> Test with the unified notification service</li>
          </ul>
        </div>
      </section>

      {/* Best Practices */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Best Practices</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <ul className="space-y-3 text-gray-700">
            <li>• <strong>Register early:</strong> Register your app during Electron app initialization</li>
            <li>• <strong>Platform awareness:</strong> Test notifications on all target platforms</li>
            <li>• <strong>User preferences:</strong> Respect user notification preferences and Do Not Disturb settings</li>
            <li>• <strong>Action handling:</strong> Implement proper handlers for notification actions</li>
            <li>• <strong>Error handling:</strong> Handle notification failures gracefully</li>
            <li>• <strong>Performance:</strong> Avoid showing too many notifications simultaneously</li>
            <li>• <strong>Accessibility:</strong> Ensure notifications are accessible to screen readers</li>
          </ul>
        </div>
      </section>
    </div>
  );
}