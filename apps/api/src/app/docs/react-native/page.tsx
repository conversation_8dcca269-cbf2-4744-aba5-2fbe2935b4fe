export default function ReactNativeDocs() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">React Native Package Documentation</h1>
        <p className="text-lg text-gray-600">
          Mobile notification support for React Native applications with push notification handling and unified service integration.
          This package provides comprehensive mobile notification capabilities for iOS and Android.
        </p>
      </div>

      {/* Installation */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Installation</h2>
        <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
          <code className="text-green-400 font-mono text-sm">
            npm install @sparkstrand/notifications-react-native
          </code>
        </pre>
        <p className="text-gray-600 mt-3">
          This package requires @sparkstrand/notifications-core as a peer dependency and integrates with the unified notification service.
        </p>
      </section>

      {/* App Setup */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Setting Up Your App</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">App Registration</h3>
          <p className="text-gray-600 mb-4">
            Before using notifications, your app must register with the notification service using your own provider API keys.
            This ensures complete isolation from other applications.
          </p>
          <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <code className="text-green-400 font-mono text-sm whitespace-pre">
{`// In your app initialization
import {notificationService} from '@sparkstrand/notifications-core';

await notificationService.registerApp({
  appId: 'sporty-expats',
  name: 'SportyExpats',
  providers: {
    novu: {
      apiKey: process.env.SPORTY_EXPATS_NOVU_API_KEY,
      appId: process.env.SPORTY_EXPATS_NOVU_APP_ID,
      priority: 1
    },
    resend: {
      apiKey: process.env.SPORTY_EXPATS_RESEND_API_KEY,
      fromEmail: '<EMAIL>',
      priority: 2
    }
  }
});`}
            </code>
          </pre>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Provider Configuration</h3>
          <p className="text-gray-600 mb-3">
            Each app manages its own provider credentials:
          </p>
          <ul className="text-gray-600 space-y-2">
            <li>• <strong>Novu:</strong> For push notifications, in-app notifications, and multi-channel delivery</li>
            <li>• <strong>Resend:</strong> For direct email delivery with custom templates</li>
            <li>• <strong>Twilio:</strong> For SMS notifications (when implemented)</li>
            <li>• <strong>Custom providers:</strong> Add your own notification providers</li>
          </ul>
        </div>
      </section>

      {/* Features */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Key Features</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <ul className="space-y-3 text-gray-700">
            <li>• <strong>Push Notifications:</strong> Full support for iOS (APNs) and Android (FCM)</li>
            <li>• <strong>In-App Notifications:</strong> Rich in-app notification components</li>
            <li>• <strong>Permission Handling:</strong> Automatic permission requests and management</li>
            <li>• <strong>Background Processing:</strong> Handle notifications when app is in background</li>
            <li>• <strong>Deep Linking:</strong> Navigate to specific screens from notifications</li>
            <li>• <strong>Notification Actions:</strong> Custom actions and quick replies</li>
            <li>• <strong>Badge Management:</strong> Automatic app badge updates</li>
            <li>• <strong>Cross-Platform:</strong> Single API for both iOS and Android</li>
            <li>• <strong>Unified Service Integration:</strong> Seamless integration with the notification service</li>
          </ul>
        </div>
      </section>

      {/* Setup */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Setup & Configuration</h2>

        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">iOS Setup (APNs)</h3>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`# Add to Info.plist
<key>UIBackgroundModes</key>
<array>
  <string>remote-notification</string>
</array>

# Add capabilities in Xcode
# - Push Notifications
# - Background Modes`}
              </code>
            </pre>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Android Setup (FCM)</h3>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`# Add to android/app/google-services.json
# Download from Firebase Console

# Add to android/build.gradle
classpath 'com.google.gms:google-services:4.3.15'`}
              </code>
            </pre>
          </div>
        </div>
      </section>

      {/* Usage Examples */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Usage Examples</h2>

        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Basic Setup with Unified Service</h3>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`import {notificationService} from '@sparkstrand/notifications-core';
import {useNotifications, usePushNotifications} from
  '@sparkstrand/notifications-react-native';

export default function App() {
  // App registration happens in initialization
  // Use hooks for notification management
  const {notifications, markAsRead} = useNotifications();
  const {requestPermission, token} = usePushNotifications();

  return <YourApp />;
}`}
              </code>
            </pre>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Sending Notifications</h3>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`// Send notification via unified service
const sendWelcomeNotification = async () => {
  try {
    await notificationService.sendNotification(
      'user-123',
      'WELCOME',
      'Welcome to SportyExpats!',
      'We\'re excited to have you on board.',
      { email: '<EMAIL>', firstName: 'John' },
      { appId: 'sporty-expats', channels: { email: true, inApp: true } }
    );
  } catch (error) {
    console.error('Failed to send notification:', error);
  }
};`}
              </code>
            </pre>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Push Notification Setup</h3>
            <pre className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <code className="text-green-400 font-mono text-sm whitespace-pre">
{`// Request push notification permissions
const setupPushNotifications = async () => {
  const {status} = await requestPermission();

  if (status === 'granted') {
    // Register device with backend
    await notificationService.registerDevice(
      'sporty-expats',
      'user-123',
      token
    );
  }
};`}
              </code>
            </pre>
          </div>
        </div>
      </section>

      {/* Components */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Available Components</h2>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Core Components</h3>
            <ul className="text-gray-600 space-y-2">
              <li>• <strong>NotificationCenter:</strong> Main notification display component</li>
              <li>• <strong>NotificationBadge:</strong> Unread notification counter</li>
              <li>• <strong>NotificationItem:</strong> Individual notification display</li>
              <li>• <strong>NotificationList:</strong> Scrollable list of notifications</li>
              <li>• <strong>NotificationPreferences:</strong> User preference management</li>
            </ul>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Hooks</h3>
            <ul className="text-gray-600 space-y-2">
              <li>• <strong>useNotifications:</strong> Manage in-app notifications</li>
              <li>• <strong>useNotificationPreferences:</strong> Handle user preferences</li>
              <li>• <strong>usePushNotifications:</strong> Push notification management</li>
              <li>• <strong>useNotificationService:</strong> Direct service access</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Best Practices */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">Best Practices</h2>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <ul className="space-y-3 text-gray-700">
            <li>• <strong>Register early:</strong> Register your app during app initialization</li>
            <li>• <strong>Permission handling:</strong> Request push permissions at appropriate times</li>
            <li>• <strong>Error handling:</strong> Always wrap notification calls in try-catch blocks</li>
            <li>• <strong>Background processing:</strong> Handle notifications when app is in background</li>
            <li>• <strong>Deep linking:</strong> Implement proper navigation from notifications</li>
            <li>• <strong>Testing:</strong> Test on both iOS and Android devices</li>
            <li>• <strong>Provider fallback:</strong> Ensure your app works even if primary provider fails</li>
          </ul>
        </div>
      </section>
    </div>
  );
}

