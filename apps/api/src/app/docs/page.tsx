'use client';

export default function DocsPage() {
    return (
        <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto">
                {/* Header */}
                <div className="text-center mb-12">
                    <h1 className="text-4xl font-bold text-gray-900 mb-4">
                        📚 Developer Documentation
                    </h1>
                    <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                        Learn how to integrate our notification system into your application
                    </p>
                </div>

                {/* Quick Start */}
                <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                        🚀 Quick Start
                    </h2>

                    <div className="space-y-6">
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                            <h3 className="text-lg font-medium text-blue-900 mb-3">
                                Step 1: Register Your Application
                            </h3>
                            <p className="text-blue-800 mb-4">
                                Fill out the registration form with your application details. Our team will review and approve your request.
                            </p>
                            <a
                                href="/register"
                                className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                            >
                                Register Now
                            </a>
                        </div>

                        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                            <h3 className="text-lg font-medium text-green-900 mb-3">
                                Step 2: Get Your API Credentials
                            </h3>
                            <p className="text-green-800 mb-4">
                                Once approved, you'll receive your API key and App ID. These are required for all API requests.
                            </p>
                            <div className="bg-white p-4 rounded border">
                                <p className="text-sm text-gray-600 mb-2">Your credentials will look like:</p>
                                <div className="space-y-2">
                                    <div>
                                        <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">App ID: my-awesome-app</span>
                                    </div>
                                    <div>
                                        <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">API Key: spark_abc123_def456</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                            <h3 className="text-lg font-medium text-purple-900 mb-3">
                                Step 3: Send Your First Notification
                            </h3>
                            <p className="text-purple-800 mb-4">
                                Use your API credentials to send notifications through our system.
                            </p>
                            <div className="bg-white p-4 rounded border">
                                <p className="text-sm text-gray-600 mb-2">Example API request:</p>
                                <pre className="text-sm bg-gray-900 text-green-400 p-3 rounded overflow-x-auto">
                                    {`curl -X POST https://your-domain.com/api/notifications \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "recipientId": "user123",
    "title": "Welcome!",
    "message": "Your account has been created successfully.",
    "type": "SUCCESS"
  }'`}
                                </pre>
                            </div>
                        </div>
                    </div>
                </div>

                {/* API Reference */}
                <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                        🔌 API Reference
                    </h2>

                    <div className="space-y-8">
                        {/* Authentication */}
                        <div>
                            <h3 className="text-lg font-medium text-gray-900 mb-3">
                                Authentication
                            </h3>
                            <p className="text-gray-600 mb-3">
                                All API requests require authentication using your API key in the Authorization header.
                            </p>
                            <div className="bg-gray-900 text-green-400 p-4 rounded overflow-x-auto">
                                <code>Authorization: Bearer YOUR_API_KEY</code>
                            </div>
                        </div>

                        {/* Send Notification */}
                        <div>
                            <h3 className="text-lg font-medium text-gray-900 mb-3">
                                Send Notification
                            </h3>
                            <div className="space-y-3">
                                <div>
                                    <p className="text-sm font-medium text-gray-700">Endpoint:</p>
                                    <code className="text-sm bg-gray-100 px-2 py-1 rounded">POST /api/notifications</code>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-gray-700">Request Body:</p>
                                    <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto">
                                        {`{
  "recipientId": "string",     // Required: Unique user identifier
  "title": "string",           // Required: Notification title
  "message": "string",         // Required: Notification message
  "type": "string",            // Optional: INFO, SUCCESS, WARNING, ERROR
  "priority": "string",        // Optional: LOW, NORMAL, HIGH, URGENT
  "scheduledFor": "string",    // Optional: ISO 8601 timestamp
  "metadata": {}               // Optional: Additional data
}`}
                                    </pre>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-gray-700">Example Response:</p>
                                    <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto">
                                        {`{
  "success": true,
  "data": {
    "id": "notif_123",
    "status": "PENDING",
    "message": "Notification queued successfully"
  }
}`}
                                    </pre>
                                </div>
                            </div>
                        </div>

                        {/* Get Application Config */}
                        <div>
                            <h3 className="text-lg font-medium text-gray-900 mb-3">
                                Get Application Configuration
                            </h3>
                            <div className="space-y-3">
                                <div>
                                    <p className="text-sm font-medium text-gray-700">Endpoint:</p>
                                    <code className="text-sm bg-gray-100 px-2 py-1 rounded">GET /api/applications?apiKey=YOUR_API_KEY&appId=YOUR_APP_ID</code>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-gray-700">Example Response:</p>
                                    <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto">
                                        {`{
  "success": true,
  "data": {
    "appId": "my-awesome-app",
    "name": "My Awesome App",
    "defaultProvider": "novu",
    "fallbackStrategy": "failover",
    "maxRetries": 3,
    "isActive": true
  }
}`}
                                    </pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Integration Examples */}
                <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                        💻 Integration Examples
                    </h2>

                    <div className="space-y-6">
                        {/* JavaScript/Node.js */}
                        <div>
                            <h3 className="text-lg font-medium text-gray-900 mb-3">
                                JavaScript/Node.js
                            </h3>
                            <pre className="text-sm bg-gray-900 text-green-400 p-4 rounded overflow-x-auto">
                                {`const sendNotification = async (recipientId, title, message) => {
  try {
    const response = await fetch('https://your-domain.com/api/notifications', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer YOUR_API_KEY',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        recipientId,
        title,
        message,
        type: 'INFO'
      })
    });
    
    const result = await response.json();
    if (result.success) {
      console.log('Notification sent:', result.data);
    }
  } catch (error) {
    console.error('Failed to send notification:', error);
  }
};

// Usage
sendNotification('user123', 'Welcome!', 'Your account is ready.');
`}
                            </pre>
                        </div>

                        {/* Python */}
                        <div>
                            <h3 className="text-lg font-medium text-gray-900 mb-3">
                                Python
                            </h3>
                            <pre className="text-sm bg-gray-900 text-green-400 p-4 rounded overflow-x-auto">
                                {`import requests
import json

def send_notification(recipient_id, title, message):
    url = 'https://your-domain.com/api/notifications'
    headers = {
        'Authorization': 'Bearer YOUR_API_KEY',
        'Content-Type': 'application/json'
    }
    data = {
        'recipientId': recipient_id,
        'title': title,
        'message': message,
        'type': 'INFO'
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        result = response.json()
        if result['success']:
            print('Notification sent:', result['data'])
    except Exception as e:
        print('Failed to send notification:', e)

# Usage
send_notification('user123', 'Welcome!', 'Your account is ready.')
`}
                            </pre>
                        </div>

                        {/* cURL */}
                        <div>
                            <h3 className="text-lg font-medium text-gray-900 mb-3">
                                cURL
                            </h3>
                            <pre className="text-sm bg-gray-900 text-green-400 p-4 rounded overflow-x-auto">
                                {`# Send a simple notification
curl -X POST https://your-domain.com/api/notifications \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "recipientId": "user123",
    "title": "Welcome!",
    "message": "Your account has been created successfully.",
    "type": "SUCCESS"
  }'

# Send a scheduled notification
curl -X POST https://your-domain.com/api/notifications \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "recipientId": "user123",
    "title": "Reminder",
    "message": "Don't forget your meeting at 2 PM",
    "scheduledFor": "2024-01-15T14:00:00Z"
  }'`}
                            </pre>
                        </div>
                    </div>
                </div>

                {/* Best Practices */}
                <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                        🎯 Best Practices
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                <h4 className="font-medium text-green-900 mb-2">✅ Do's</h4>
                                <ul className="text-sm text-green-800 space-y-1">
                                    <li>• Use meaningful recipient IDs</li>
                                    <li>• Provide clear, actionable titles</li>
                                    <li>• Handle API errors gracefully</li>
                                    <li>• Store API key securely</li>
                                    <li>• Use appropriate notification types</li>
                                </ul>
                            </div>
                        </div>

                        <div className="space-y-4">
                            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                                <h4 className="font-medium text-red-900 mb-2">❌ Don'ts</h4>
                                <ul className="text-sm text-red-800 space-y-1">
                                    <li>• Don't expose API key in client code</li>
                                    <li>• Don't send too many notifications</li>
                                    <li>• Don't ignore rate limits</li>
                                    <li>• Don't use generic recipient IDs</li>
                                    <li>• Don't forget error handling</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Support */}
                <div className="bg-white rounded-2xl shadow-xl p-8">
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                        🆘 Need Help?
                    </h2>

                    <div className="text-center">
                        <p className="text-gray-600 mb-6">
                            If you need assistance with integration or have questions about the API, we're here to help!
                        </p>

                        <div className="space-y-4">
                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <h3 className="font-medium text-blue-900 mb-2">📧 Email Support</h3>
                                <p className="text-blue-800">
                                    <a href="mailto:<EMAIL>" className="underline">
                                        <EMAIL>
                                    </a>
                                </p>
                            </div>

                            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                <h3 className="font-medium text-green-900 mb-2">📚 Documentation</h3>
                                <p className="text-green-800">
                                    Check our comprehensive API documentation for detailed examples and reference.
                                </p>
                            </div>

                            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                                <h3 className="font-medium text-purple-900 mb-2">🚀 Quick Start</h3>
                                <p className="text-purple-800">
                                    <a href="/register" className="underline">
                                        Register your application
                                    </a>{' '}
                                    to get started with the notification system.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
} 