{"name": "@sparkstrand/notifications", "version": "0.0.0", "description": "A vendor-agnostic notifications system built on top of Novu with custom npm packages", "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "test": "turbo test", "clean": "turbo clean", "type-check": "turbo type-check", "format": "prettier --write \"**/*.{ts,tsx,md,json}\"", "publish-packages": "turbo run build lint test && changeset version && changeset publish"}, "devDependencies": {"@changesets/cli": "^2.27.11", "@typescript-eslint/eslint-plugin": "^8.20.0", "@typescript-eslint/parser": "^8.20.0", "eslint": "^9", "prettier": "^3.4.2", "turbo": "^2.3.3", "typescript": "^5.7.3"}, "engines": {"node": ">=18", "yarn": ">=4.9.1"}, "packageManager": "yarn@4.9.2", "workspaces": ["apps/*", "packages/*"], "repository": {"type": "git", "url": "https://github.com/sparkstrand/notifications.git"}, "keywords": ["notifications", "multi-tenant", "vendor-agnostic", "typescript"], "license": "MIT", "dependenciesMeta": {"@prisma/client@6.14.0": {"unplugged": true}, "prisma@6.14.0": {"unplugged": true}}}