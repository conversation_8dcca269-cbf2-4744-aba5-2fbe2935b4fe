# Changesets

This directory contains changesets for the Spark Strand notifications monorepo.

## What are Changesets?

Changesets are a way to manage versioning and changelogs for monorepos. They allow you to:
- Track changes to packages
- Automatically version packages
- Generate changelogs
- Publish packages to npm

## How to Use Changesets

### 1. Create a Changeset

When you make changes to packages, create a changeset:

```bash
yarn changeset
```

This will prompt you to:
- Select which packages have changed
- Choose the type of change (major, minor, patch)
- Write a description of the changes

### 2. Commit Changesets

Always commit your changesets:

```bash
git add .changeset/
git commit -m "feat: add new notification types"
```

### 3. Version and Publish

When ready to release:

```bash
# Version packages (creates git tags and updates package.json)
yarn changeset version

# Publish to npm
yarn changeset publish
```

## Package Publishing

The following packages are published to GitHub Packages:

- `@sparkstrand/notifications-core` - Core types and utilities
- `@sparkstrand/notifications-react` - React integration
- `@sparkstrand/notifications-react-native` - React Native integration
- `@sparkstrand/notifications-electron` - Electron integration

## Automated Publishing

The GitHub Actions workflow automatically:
1. Builds all packages
2. Checks for changesets
3. Creates release PRs or publishes directly to GitHub Packages

## Using Published Packages

Other applications can install these packages from GitHub Packages:

```bash
npm install @sparkstrand/notifications-core
npm install @sparkstrand/notifications-react
```

Or in package.json:
```json
{
  "dependencies": {
    "@sparkstrand/notifications-core": "^1.0.0",
    "@sparkstrand/notifications-react": "^1.0.0"
  }
}
```

## Authentication

To use these packages, applications need to authenticate with GitHub Packages by:
1. Creating a GitHub Personal Access Token with `read:packages` scope
2. Adding it to `.npmrc`:
   ```
   @sparkstrand:registry=https://npm.pkg.github.com
   //npm.pkg.github.com/:_authToken=${GITHUB_TOKEN}
   ```
