# @sparkstrand/notifications-react-native

React Native components and hooks for the Spark Strand notification system, designed for mobile applications like SportyExpats.

## 🚀 **Quick Start**

```typescript
import { 
    NotificationCenter, 
    NotificationBadge,
    useNotifications,
    useNotificationPreferences 
} from '@sparkstrand/notifications-react-native';

// In your component
function MyComponent() {
    const { notifications, sendNotification } = useNotifications();
    const { preferences, updatePreferences } = useNotificationPreferences();

    return (
        <NotificationCenter>
            <NotificationBadge count={notifications.length} />
            {/* Your app content */}
        </NotificationCenter>
    );
}
```

## 📱 **Features**

- **Push Notifications**: Expo push notification integration
- **In-App Notifications**: Real-time notification display
- **User Preferences**: Channel and frequency controls
- **Notification Center**: Centralized notification management
- **Badge Support**: Unread notification counters
- **Cross-Platform**: Works on iOS and Android

## 🔧 **Installation**

### 1. Install the package

```bash
npm install @sparkstrand/notifications-react-native
```

### 2. Install peer dependencies

```bash
npx expo install expo-notifications
```

### 3. Configure Expo (app.json)

```json
{
  "expo": {
    "plugins": [
      [
        "expo-notifications",
        {
          "icon": "./assets/notification-icon.png",
          "color": "#ffffff",
          "sounds": ["./assets/notification-sound.wav"]
        }
      ]
    ]
  }
}
```

## 🎯 **Core Components**

### **NotificationCenter**

The main wrapper component that provides notification context:

```typescript
import { NotificationCenter } from '@sparkstrand/notifications-react-native';

export default function App() {
    return (
        <NotificationCenter>
            {/* Your app components */}
        </NotificationCenter>
    );
}
```

### **NotificationBadge**

Displays unread notification count:

```typescript
import { NotificationBadge } from '@sparkstrand/notifications-react-native';

function Header() {
    return (
        <View>
            <Text>My App</Text>
            <NotificationBadge count={5} />
        </View>
    );
}
```

### **NotificationItem**

Individual notification display:

```typescript
import { NotificationItem } from '@sparkstrand/notifications-react-native';

function NotificationList() {
    return (
        <View>
            {notifications.map(notification => (
                <NotificationItem
                    key={notification.id}
                    notification={notification}
                    onPress={() => handleNotificationPress(notification)}
                />
            ))}
        </View>
    );
}
```

### **NotificationList**

List of notifications with built-in rendering:

```typescript
import { NotificationList } from '@sparkstrand/notifications-react-native';

function NotificationsScreen() {
    return (
        <NotificationList
            notifications={notifications}
            onNotificationPress={handlePress}
            onNotificationDismiss={handleDismiss}
        />
    );
}
```

### **NotificationPreferences**

User preference management:

```typescript
import { NotificationPreferences } from '@sparkstrand/notifications-react-native';

function SettingsScreen() {
    return (
        <NotificationPreferences
            preferences={preferences}
            onPreferencesChange={updatePreferences}
        />
    );
}
```

## 🪝 **Hooks**

### **useNotifications**

Main hook for notification operations:

```typescript
import { useNotifications } from '@sparkstrand/notifications-react-native';

function MyComponent() {
    const {
        notifications,
        sendNotification,
        markAsRead,
        markAllAsRead,
        deleteNotification,
        isLoading,
        error
    } = useNotifications();

    const handleSendNotification = async () => {
        await sendNotification({
            type: 'EVENT_REMINDER',
            title: 'Event Tomorrow!',
            content: 'Don\'t forget your event',
            metadata: { email: '<EMAIL>' }
        });
    };

    return (
        <View>
            <Text>Notifications: {notifications.length}</Text>
            <Button title="Send Test" onPress={handleSendNotification} />
        </View>
    );
}
```

### **useNotificationPreferences**

Manage user notification preferences:

```typescript
import { useNotificationPreferences } from '@sparkstrand/notifications-react-native';

function PreferencesComponent() {
    const {
        preferences,
        updatePreferences,
        isLoading,
        error
    } = useNotificationPreferences();

    const toggleEmail = () => {
        updatePreferences({ email: !preferences.email });
    };

    return (
        <View>
            <Switch
                value={preferences.email}
                onValueChange={toggleEmail}
            />
            <Text>Email Notifications</Text>
        </View>
    );
}
```

### **usePushNotifications**

Push notification management:

```typescript
import { usePushNotifications } from '@sparkstrand/notifications-react-native';

function PushNotificationSetup() {
    const {
        expoPushToken,
        requestPermission,
        isRegistered,
        registerDevice,
        unregisterDevice
    } = usePushNotifications();

    useEffect(() => {
        requestPermission();
    }, []);

    useEffect(() => {
        if (expoPushToken) {
            registerDevice(expoPushToken);
        }
    }, [expoPushToken]);

    return (
        <View>
            <Text>Push Token: {expoPushToken}</Text>
            <Text>Registered: {isRegistered ? 'Yes' : 'No'}</Text>
        </View>
    );
}
```

## 🔔 **Push Notifications**

### **Request Permission**

```typescript
import { usePushNotifications } from '@sparkstrand/notifications-react-native';

function App() {
    const { requestPermission } = usePushNotifications();

    useEffect(() => {
        requestPermission();
    }, []);

    return <YourApp />;
}
```

### **Handle Notifications**

```typescript
import { usePushNotifications } from '@sparkstrand/notifications-react-native';

function App() {
    const { handleNotificationResponse } = usePushNotifications();

    useEffect(() => {
        const subscription = Notifications.addNotificationResponseReceivedListener(
            handleNotificationResponse
        );

        return () => subscription.remove();
    }, []);

    return <YourApp />;
}
```

## 🎨 **Customization**

### **Styling Components**

All components accept standard React Native style props:

```typescript
<NotificationBadge 
    count={5}
    style={{ backgroundColor: 'red', borderRadius: 10 }}
    textStyle={{ color: 'white', fontWeight: 'bold' }}
/>
```

### **Custom Rendering**

Override default rendering with custom components:

```typescript
<NotificationList
    notifications={notifications}
    renderNotification={(notification) => (
        <CustomNotificationItem notification={notification} />
    )}
/>
```

## 🔧 **Configuration**

### **Environment Variables**

```bash
# Your app's notification service configuration
EXPO_PROJECT_ID=your-expo-project-id
NOTIFICATION_API_URL=https://your-api.com
```

### **App Configuration**

```typescript
// In your app initialization
import { configureNotifications } from '@sparkstrand/notifications-react-native';

configureNotifications({
    apiUrl: 'https://your-api.com',
    appId: 'sporty-expats',
    enablePushNotifications: true,
    enableInAppNotifications: true
});
```

## 📱 **Platform-Specific Features**

### **iOS**

- Rich push notifications
- Silent notifications
- Background app refresh
- Notification actions

### **Android**

- Notification channels
- Priority levels
- Custom sounds
- Heads-up notifications

## 🧪 **Testing**

### **Test Notifications**

```typescript
import { testNotification } from '@sparkstrand/notifications-react-native';

// Send a test notification
await testNotification({
    type: 'TEST',
    title: 'Test Notification',
    content: 'This is a test notification'
});
```

### **Mock Mode**

```typescript
import { enableMockMode } from '@sparkstrand/notifications-react-native';

// Enable mock mode for testing
enableMockMode();
```

## 🚀 **Integration with Core System**

This package integrates seamlessly with the `@sparkstrand/notifications-core` package:

```typescript
// In your backend
import { notificationService } from '@sparkstrand/notifications-core';

// Send notification to mobile app
await notificationService.sendNotification(
    'user-123',
    'EVENT_REMINDER',
    'Event Tomorrow!',
    'Don\'t forget your event',
    { userId: 'user-123' },
    { channels: { push: true, inApp: true } }
);
```

## 📚 **Additional Resources**

- **Core Package**: [@sparkstrand/notifications-core](../core/README.md)
- **API Documentation**: [API Reference](../core/README.md)
- **Examples**: [Usage Examples](../core/examples/)

---

**Built for React Native applications with comprehensive notification support and seamless integration with the Spark Strand notification system.** 