{"name": "@sparkstrand/notifications-react-native", "version": "0.0.0", "description": "React Native client for Spark Strand Notifications System", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "files": ["dist"], "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@sparkstrand/notifications-core": "workspace:*"}, "peerDependencies": {"react": ">=16.8.0", "react-native": ">=0.60.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-native": "^0.72.0", "eslint": "^9", "tsup": "^8.0.0", "typescript": "^5.0.0"}, "publishConfig": {"access": "restricted"}, "repository": {"type": "git", "url": "https://github.com/sparkstrand/notifications.git", "directory": "packages/react-native"}, "keywords": ["notifications", "react-native", "mobile"], "license": "MIT"}