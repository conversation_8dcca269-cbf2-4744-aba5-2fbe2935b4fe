import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert } from 'react-native';
import { useNotifications, useNotificationPreferences } from './hooks';
import {
    BaseNotification,
    NotificationType,
    NotificationPriority,
    NotificationStatus,
} from '@sparkstrand/notifications-core';

// Notification Badge Component
export const NotificationBadge: React.FC<{
    count: number;
    style?: any;
    showZero?: boolean;
    maxCount?: number;
}> = ({ count, style, showZero = false, maxCount = 99 }) => {
    if (count === 0 && !showZero) return null;

    const displayCount = count > maxCount ? `${maxCount}+` : count.toString();

    return (
        <View style={[styles.badge, style]}>
            <Text style={styles.badgeText}>{displayCount}</Text>
        </View>
    );
};

// Notification Item Component
export const NotificationItem: React.FC<{
    notification: BaseNotification;
    onMarkAsRead?: (notificationId: string) => void;
    onDelete?: (notificationId: string) => void;
    onPress?: (notification: BaseNotification) => void;
    style?: any;
    showActions?: boolean;
}> = ({ notification, onMarkAsRead, onDelete, onPress, style, showActions = true }) => {
    const handlePress = () => {
        if (onPress) {
            onPress(notification);
        }
        if (!notification.isRead && onMarkAsRead) {
            onMarkAsRead(notification.id);
        }
    };

    const handleMarkAsRead = () => {
        if (onMarkAsRead) {
            onMarkAsRead(notification.id);
        }
    };

    const handleDelete = () => {
        if (onDelete) {
            Alert.alert(
                'Delete Notification',
                'Are you sure you want to delete this notification?',
                [
                    { text: 'Cancel', style: 'cancel' },
                    { text: 'Delete', style: 'destructive', onPress: () => onDelete(notification.id) },
                ]
            );
        }
    };

    return (
        <TouchableOpacity
            style={[
                styles.notificationItem,
                notification.isRead && styles.notificationRead,
                style,
            ]}
            onPress={handlePress}
        >
            <View style={styles.notificationHeader}>
                <Text style={styles.notificationTitle}>{notification.title}</Text>
                <View style={styles.notificationMeta}>
                    <Text style={[styles.notificationType, styles[`type${notification.type}`]]}>
                        {notification.type}
                    </Text>
                    <Text style={[styles.notificationPriority, styles[`priority${notification.priority}`]]}>
                        {notification.priority}
                    </Text>
                </View>
            </View>

            <Text style={styles.notificationContent}>{notification.content}</Text>

            <View style={styles.notificationFooter}>
                <Text style={styles.notificationTime}>
                    {new Date(notification.createdAt).toLocaleDateString()}
                </Text>

                {showActions && (
                    <View style={styles.notificationActions}>
                        {!notification.isRead && (
                            <TouchableOpacity onPress={handleMarkAsRead} style={styles.actionButton}>
                                <Text style={styles.actionButtonText}>Mark Read</Text>
                            </TouchableOpacity>
                        )}
                        <TouchableOpacity onPress={handleDelete} style={[styles.actionButton, styles.deleteButton]}>
                            <Text style={[styles.actionButtonText, styles.deleteButtonText]}>Delete</Text>
                        </TouchableOpacity>
                    </View>
                )}
            </View>
        </TouchableOpacity>
    );
};

// Notification List Component
export const NotificationList: React.FC<{
    notifications: BaseNotification[];
    onMarkAsRead?: (notificationId: string) => void;
    onDelete?: (notificationId: string) => void;
    onNotificationPress?: (notification: BaseNotification) => void;
    style?: any;
    emptyMessage?: string;
    maxHeight?: number;
}> = ({
    notifications,
    onMarkAsRead,
    onDelete,
    onNotificationPress,
    style,
    emptyMessage = 'No notifications',
    maxHeight
}) => {
        if (notifications.length === 0) {
            return (
                <View style={[styles.emptyContainer, style]}>
                    <Text style={styles.emptyText}>{emptyMessage}</Text>
                </View>
            );
        }

        return (
            <ScrollView
                style={[styles.notificationList, style, maxHeight && { maxHeight }]}
                showsVerticalScrollIndicator={false}
            >
                {notifications.map((notification) => (
                    <NotificationItem
                        key={notification.id}
                        notification={notification}
                        onMarkAsRead={onMarkAsRead}
                        onDelete={onDelete}
                        onPress={onNotificationPress}
                    />
                ))}
            </ScrollView>
        );
    };

// Notification Center Component
export const NotificationCenter: React.FC<{
    config: {
        apiUrl: string;
        appId: string;
        userId: string;
        deviceToken?: string;
        autoConnect?: boolean;
    };
    style?: any;
    showUnreadCount?: boolean;
    maxNotifications?: number;
    onNotificationPress?: (notification: BaseNotification) => void;
    onMarkAllRead?: () => void;
    onPreferencesChange?: (preferences: any) => void;
}> = ({
    config,
    style,
    showUnreadCount = true,
    maxNotifications = 50,
    onNotificationPress,
    onMarkAllRead,
    onPreferencesChange
}) => {
        const {
            notifications,
            unreadCount,
            isLoading,
            error,
            markAsRead,
            markAllAsRead,
            deleteNotification,
            refreshNotifications,
            clearError,
        } = useNotifications(config);

        const {
            preferences,
            updatePreferences,
            updateChannelPreference,
            updateQuietHours,
        } = useNotificationPreferences(config);

        const handleMarkAllRead = async () => {
            await markAllAsRead();
            if (onMarkAllRead) {
                onMarkAllRead();
            }
        };

        const handleNotificationPress = (notification: BaseNotification) => {
            if (onNotificationPress) {
                onNotificationPress(notification);
            }
        };

        const handlePreferencesChange = async (updates: any) => {
            await updatePreferences(updates);
            if (onPreferencesChange) {
                onPreferencesChange(updates);
            }
        };

        if (error) {
            return (
                <View style={[styles.errorContainer, style]}>
                    <Text style={styles.errorText}>{error}</Text>
                    <TouchableOpacity onPress={clearError} style={styles.retryButton}>
                        <Text style={styles.retryButtonText}>Retry</Text>
                    </TouchableOpacity>
                </View>
            );
        }

        return (
            <View style={[styles.notificationCenter, style]}>
                <View style={styles.notificationCenterHeader}>
                    <Text style={styles.notificationCenterTitle}>Notifications</Text>
                    <View style={styles.notificationCenterActions}>
                        {showUnreadCount && unreadCount > 0 && (
                            <NotificationBadge count={unreadCount} />
                        )}
                        {unreadCount > 0 && (
                            <TouchableOpacity onPress={handleMarkAllRead} style={styles.markAllReadButton}>
                                <Text style={styles.markAllReadButtonText}>Mark All Read</Text>
                            </TouchableOpacity>
                        )}
                        <TouchableOpacity onPress={refreshNotifications} style={styles.refreshButton}>
                            <Text style={styles.refreshButtonText}>Refresh</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {isLoading ? (
                    <View style={styles.loadingContainer}>
                        <Text style={styles.loadingText}>Loading notifications...</Text>
                    </View>
                ) : (
                    <NotificationList
                        notifications={notifications.slice(0, maxNotifications)}
                        onMarkAsRead={markAsRead}
                        onDelete={deleteNotification}
                        onNotificationPress={handleNotificationPress}
                    />
                )}
            </View>
        );
    };

// Notification Preferences Component
export const NotificationPreferences: React.FC<{
    preferences: any;
    onPreferencesChange: (preferences: any) => void;
    style?: any;
    showQuietHours?: boolean;
}> = ({ preferences, onPreferencesChange, style, showQuietHours = true }) => {
    const handleChannelToggle = (channel: string, enabled: boolean) => {
        const updatedPreferences = {
            ...preferences,
            [channel]: enabled,
        };
        onPreferencesChange(updatedPreferences);
    };

    const handleQuietHoursToggle = (enabled: boolean) => {
        const updatedPreferences = {
            ...preferences,
            quietHoursEnabled: enabled,
        };
        onPreferencesChange(updatedPreferences);
    };

    return (
        <View style={[styles.preferencesContainer, style]}>
            <Text style={styles.preferencesTitle}>Notification Preferences</Text>

            <View style={styles.channelPreferences}>
                <Text style={styles.channelPreferencesTitle}>Channels</Text>
                {Object.entries(preferences).map(([channel, enabled]) => {
                    if (channel.startsWith('quietHours') || channel === 'timezone') return null;

                    return (
                        <TouchableOpacity
                            key={channel}
                            style={styles.channelToggle}
                            onPress={() => handleChannelToggle(channel, !enabled)}
                        >
                            <Text style={styles.channelName}>{channel}</Text>
                            <View style={[styles.toggle, enabled && styles.toggleEnabled]}>
                                <Text style={styles.toggleText}>{enabled ? 'ON' : 'OFF'}</Text>
                            </View>
                        </TouchableOpacity>
                    );
                })}
            </View>

            {showQuietHours && (
                <View style={styles.quietHoursContainer}>
                    <Text style={styles.quietHoursTitle}>Quiet Hours</Text>
                    <TouchableOpacity
                        style={styles.channelToggle}
                        onPress={() => handleQuietHoursToggle(!preferences.quietHoursEnabled)}
                    >
                        <Text style={styles.channelName}>Enable Quiet Hours</Text>
                        <View style={[styles.toggle, preferences.quietHoursEnabled && styles.toggleEnabled]}>
                            <Text style={styles.toggleText}>
                                {preferences.quietHoursEnabled ? 'ON' : 'OFF'}
                            </Text>
                        </View>
                    </TouchableOpacity>
                </View>
            )}
        </View>
    );
};

// Styles
const styles = StyleSheet.create({
    badge: {
        backgroundColor: '#FF3B30',
        borderRadius: 10,
        minWidth: 20,
        height: 20,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 6,
    },
    badgeText: {
        color: 'white',
        fontSize: 12,
        fontWeight: 'bold',
    },

    notificationItem: {
        backgroundColor: 'white',
        borderRadius: 8,
        padding: 16,
        marginBottom: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    notificationRead: {
        opacity: 0.6,
    },
    notificationHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 8,
    },
    notificationTitle: {
        fontSize: 16,
        fontWeight: '600',
        flex: 1,
        marginRight: 8,
    },
    notificationMeta: {
        alignItems: 'flex-end',
    },
    notificationType: {
        fontSize: 12,
        fontWeight: '500',
        marginBottom: 4,
    },
    notificationPriority: {
        fontSize: 10,
        fontWeight: '400',
    },
    notificationContent: {
        fontSize: 14,
        color: '#666',
        marginBottom: 12,
        lineHeight: 20,
    },
    notificationFooter: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    notificationTime: {
        fontSize: 12,
        color: '#999',
    },
    notificationActions: {
        flexDirection: 'row',
        gap: 8,
    },
    actionButton: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 4,
        backgroundColor: '#007AFF',
    },
    actionButtonText: {
        color: 'white',
        fontSize: 12,
        fontWeight: '500',
    },
    deleteButton: {
        backgroundColor: '#FF3B30',
    },
    deleteButtonText: {
        color: 'white',
    },

    notificationList: {
        flex: 1,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 32,
    },
    emptyText: {
        fontSize: 16,
        color: '#999',
        textAlign: 'center',
    },

    notificationCenter: {
        flex: 1,
        backgroundColor: '#F2F2F7',
    },
    notificationCenterHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#E5E5EA',
    },
    notificationCenterTitle: {
        fontSize: 20,
        fontWeight: '600',
    },
    notificationCenterActions: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
    },
    markAllReadButton: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 4,
        backgroundColor: '#34C759',
    },
    markAllReadButtonText: {
        color: 'white',
        fontSize: 12,
        fontWeight: '500',
    },
    refreshButton: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 4,
        backgroundColor: '#007AFF',
    },
    refreshButtonText: {
        color: 'white',
        fontSize: 12,
        fontWeight: '500',
    },

    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 32,
    },
    loadingText: {
        fontSize: 16,
        color: '#999',
    },

    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 32,
    },
    errorText: {
        fontSize: 16,
        color: '#FF3B30',
        textAlign: 'center',
        marginBottom: 16,
    },
    retryButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 4,
        backgroundColor: '#007AFF',
    },
    retryButtonText: {
        color: 'white',
        fontSize: 14,
        fontWeight: '500',
    },

    preferencesContainer: {
        backgroundColor: 'white',
        borderRadius: 8,
        padding: 16,
        margin: 16,
    },
    preferencesTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 16,
    },
    channelPreferences: {
        marginBottom: 24,
    },
    channelPreferencesTitle: {
        fontSize: 16,
        fontWeight: '500',
        marginBottom: 12,
    },
    channelToggle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#E5E5EA',
    },
    channelName: {
        fontSize: 16,
        color: '#333',
    },
    toggle: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 4,
        backgroundColor: '#E5E5EA',
    },
    toggleEnabled: {
        backgroundColor: '#34C759',
    },
    toggleText: {
        fontSize: 12,
        fontWeight: '500',
        color: 'white',
    },
    quietHoursContainer: {
        borderTopWidth: 1,
        borderTopColor: '#E5E5EA',
        paddingTop: 16,
    },
    quietHoursTitle: {
        fontSize: 16,
        fontWeight: '500',
        marginBottom: 12,
    },

    // Type-specific styles
    typeWELCOME: { color: '#34C759' },
    typeNEW_LOGIN: { color: '#007AFF' },
    typePAYMENT_SUCCESSFUL: { color: '#34C759' },
    typePAYMENT_FAILED: { color: '#FF3B30' },
    typeEVENT_REMINDER: { color: '#FF9500' },
    typeEVENT_CANCELLED: { color: '#FF3B30' },
    typeCUSTOM: { color: '#8E8E93' },

    // Priority-specific styles
    priorityLOW: { color: '#8E8E93' },
    priorityNORMAL: { color: '#007AFF' },
    priorityHIGH: { color: '#FF9500' },
    priorityURGENT: { color: '#FF3B30' },
});
