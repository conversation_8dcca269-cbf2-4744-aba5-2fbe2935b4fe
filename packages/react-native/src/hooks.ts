import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
    CreateNotificationRequest,
    BaseNotification,
    NotificationPreferences,
    NotificationType,
    DeliveryChannels,
    NotificationStatus,
    NotificationPriority,
} from '@sparkstrand/notifications-core';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

export interface NotificationsConfig {
    apiUrl: string;
    appId: string;
    userId: string;
    deviceToken?: string;
    autoConnect?: boolean;
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
    enablePushNotifications?: boolean;
    enableBackgroundRefresh?: boolean;
    expoProjectId?: string;
    appVersion?: string;
    deviceId?: string;
}

export interface UseNotificationsReturn {
    notifications: BaseNotification[];
    unreadCount: number;
    isLoading: boolean;
    error: string | null;
    isConnected: boolean;
    deviceToken: string | null;
    isPushEnabled: boolean;
    sendNotification: (notification: Omit<CreateNotificationRequest, 'appId'>) => Promise<boolean>;
    markAsRead: (notificationId: string) => Promise<boolean>;
    markAllAsRead: () => Promise<boolean>;
    deleteNotification: (notificationId: string) => Promise<boolean>;
    refreshNotifications: () => Promise<void>;
    connect: () => void;
    disconnect: () => void;
    registerDevice: (token: string) => Promise<boolean>;
    unregisterDevice: () => Promise<boolean>;
    enablePushNotifications: () => Promise<boolean>;
    disablePushNotifications: () => Promise<boolean>;
    clearError: () => void;
}

export interface UseNotificationPreferencesReturn {
    preferences: NotificationPreferences | null;
    updatePreferences: (preferences: NotificationPreferences) => Promise<boolean>;
    isLoading: boolean;
    error: string | null;
    updateChannelPreference: (channel: keyof DeliveryChannels, enabled: boolean) => Promise<boolean>;
    updateQuietHours: (enabled: boolean, startTime?: string, endTime?: string) => Promise<boolean>;
}

export interface UseNotificationReturn {
    notification: BaseNotification | null;
    isLoading: boolean;
    error: string | null;
    markAsRead: () => Promise<boolean>;
    delete: () => Promise<boolean>;
    refresh: () => Promise<void>;
}

export interface UseNotificationStatsReturn {
    stats: {
        total: number;
        unread: number;
        byType: Record<string, number>;
        byPriority: Record<string, number>;
        byStatus: Record<string, number>;
    } | null;
    isLoading: boolean;
    error: string | null;
    refresh: () => Promise<void>;
}

export interface UsePushNotificationsReturn {
    isSupported: boolean;
    isEnabled: boolean;
    deviceToken: string | null;
    permissionStatus: 'granted' | 'denied' | 'not-determined' | null;
    requestPermission: () => Promise<boolean>;
    registerDevice: (token: string) => Promise<boolean>;
    unregisterDevice: () => Promise<boolean>;
    onNotificationReceived: (callback: (notification: any) => void) => void;
    onNotificationOpened: (callback: (notification: any) => void) => void;
}

// Main notifications hook for React Native
export function useNotifications(config: NotificationsConfig): UseNotificationsReturn {
    const [notifications, setNotifications] = useState<BaseNotification[]>([]);
    const [unreadCount, setUnreadCount] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isConnected, setIsConnected] = useState(false);
    const [deviceToken, setDeviceToken] = useState<string | null>(config.deviceToken || null);
    const [isPushEnabled, setIsPushEnabled] = useState(config.enablePushNotifications || false);

    const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
    const reconnectAttemptsRef = useRef(0);
    const eventSourceRef = useRef<EventSource>();

    const {
        apiUrl,
        appId,
        userId,
        autoConnect = true,
        reconnectInterval = 5000,
        maxReconnectAttempts = 5,
    } = config;

    const apiCall = useCallback(async <T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<T> => {
        try {
            const response = await fetch(`${apiUrl}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    'X-App-ID': appId,
                    'X-User-ID': userId,
                    ...options.headers,
                },
                ...options,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API call failed:', error);
            throw error;
        }
    }, [apiUrl, appId, userId]);

    const fetchNotifications = useCallback(async () => {
        try {
            setIsLoading(true);
            const data = await apiCall<{ notifications: BaseNotification[]; unreadCount: number }>(
                `/notifications?appId=${appId}&userId=${userId}`
            );
            setNotifications(data.notifications);
            setUnreadCount(data.unreadCount);
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to fetch notifications');
        } finally {
            setIsLoading(false);
        }
    }, [apiCall, appId, userId]);

    const sendNotification = useCallback(async (notification: Omit<CreateNotificationRequest, 'appId'>): Promise<boolean> => {
        try {
            const success = await apiCall<{ success: boolean }>('/notifications', {
                method: 'POST',
                body: JSON.stringify({ ...notification, appId }),
            });

            if (success.success) {
                await fetchNotifications();
                return true;
            }
            return false;
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to send notification');
            return false;
        }
    }, [apiCall, appId, fetchNotifications]);

    const markAsRead = useCallback(async (notificationId: string): Promise<boolean> => {
        try {
            const success = await apiCall<{ success: boolean }>(`/notifications/${notificationId}/read`, {
                method: 'PUT',
                body: JSON.stringify({ appId, userId }),
            });

            if (success.success) {
                await fetchNotifications();
                return true;
            }
            return false;
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to mark notification as read');
            return false;
        }
    }, [apiCall, appId, userId, fetchNotifications]);

    const markAllAsRead = useCallback(async (): Promise<boolean> => {
        try {
            const success = await apiCall<{ success: boolean }>('/notifications/read-all', {
                method: 'PUT',
                body: JSON.stringify({ appId, userId }),
            });

            if (success.success) {
                await fetchNotifications();
                return true;
            }
            return false;
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to mark all notifications as read');
            return false;
        }
    }, [apiCall, appId, userId, fetchNotifications]);

    const deleteNotification = useCallback(async (notificationId: string): Promise<boolean> => {
        try {
            const success = await apiCall<{ success: boolean }>(`/notifications/${notificationId}`, {
                method: 'DELETE',
                body: JSON.stringify({ appId, userId }),
            });

            if (success.success) {
                setNotifications(prev => prev.filter(n => n.id !== notificationId));
                return true;
            }
            return false;
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to delete notification');
            return false;
        }
    }, [apiCall, appId, userId]);

    const refreshNotifications = useCallback(async () => {
        await fetchNotifications();
    }, [fetchNotifications]);

    const registerDevice = useCallback(async (token: string): Promise<boolean> => {
        try {
            const success = await apiCall<{ success: boolean }>('/devices/register', {
                method: 'POST',
                body: JSON.stringify({ appId, userId, deviceToken: token, platform: 'react-native' }),
            });

            if (success.success) {
                setDeviceToken(token);
                return true;
            }
            return false;
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to register device');
            return false;
        }
    }, [apiCall, appId, userId]);

    const unregisterDevice = useCallback(async (): Promise<boolean> => {
        try {
            const success = await apiCall<{ success: boolean }>('/devices/unregister', {
                method: 'POST',
                body: JSON.stringify({ appId, userId }),
            });

            if (success.success) {
                setDeviceToken(null);
                return true;
            }
            return false;
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to unregister device');
            return false;
        }
    }, [apiCall, appId, userId]);

    const enablePushNotifications = useCallback(async (): Promise<boolean> => {
        try {
            const success = await apiCall<{ success: boolean }>('/devices/enable-push', {
                method: 'POST',
                body: JSON.stringify({ appId, userId, enabled: true }),
            });

            if (success.success) {
                setIsPushEnabled(true);
                return true;
            }
            return false;
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to enable push notifications');
            return false;
        }
    }, [apiCall, appId, userId]);

    const disablePushNotifications = useCallback(async (): Promise<boolean> => {
        try {
            const success = await apiCall<{ success: boolean }>('/devices/disable-push', {
                method: 'POST',
                body: JSON.stringify({ appId, userId, enabled: false }),
            });

            if (success.success) {
                setIsPushEnabled(false);
                return true;
            }
            return false;
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to disable push notifications');
            return false;
        }
    }, [apiCall, appId, userId]);

    const connect = useCallback(() => {
        try {
            if (eventSourceRef.current) {
                eventSourceRef.current.close();
            }

            const eventSource = new EventSource(`${apiUrl}/notifications/stream?appId=${appId}&userId=${userId}`);

            eventSource.onopen = () => {
                setIsConnected(true);
                reconnectAttemptsRef.current = 0;
            };

            eventSource.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    if (data.type === 'notification') {
                        setNotifications(prev => [data.notification, ...prev]);
                        if (!data.notification.isRead) {
                            setUnreadCount(prev => prev + 1);
                        }
                    }
                } catch (error) {
                    console.error('Failed to parse SSE message:', error);
                }
            };

            eventSource.onerror = () => {
                setIsConnected(false);
                eventSource.close();

                if (reconnectAttemptsRef.current < maxReconnectAttempts) {
                    reconnectAttemptsRef.current++;
                    reconnectTimeoutRef.current = setTimeout(connect, reconnectInterval);
                }
            };

            eventSourceRef.current = eventSource;
        } catch (error) {
            console.error('Failed to connect to SSE:', error);
            setIsConnected(false);
        }
    }, [apiUrl, appId, userId, reconnectInterval, maxReconnectAttempts]);

    const disconnect = useCallback(() => {
        if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
        }

        if (eventSourceRef.current) {
            eventSourceRef.current.close();
            eventSourceRef.current = undefined;
        }

        setIsConnected(false);
    }, []);

    const clearError = useCallback(() => {
        setError(null);
    }, []);

    useEffect(() => {
        fetchNotifications();
        if (autoConnect) {
            connect();
        }
        return () => {
            disconnect();
        };
    }, [fetchNotifications, autoConnect, connect, disconnect]);

    return {
        notifications,
        unreadCount,
        isLoading,
        error,
        isConnected,
        deviceToken,
        isPushEnabled,
        sendNotification,
        markAsRead,
        markAllAsRead,
        deleteNotification,
        refreshNotifications,
        connect,
        disconnect,
        registerDevice,
        unregisterDevice,
        enablePushNotifications,
        disablePushNotifications,
        clearError,
    };
}

// Hook for managing notification preferences
export function useNotificationPreferences(config: NotificationsConfig): UseNotificationPreferencesReturn {
    const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const { apiUrl, appId, userId } = config;

    const apiCall = useCallback(async <T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<T> => {
        try {
            const response = await fetch(`${apiUrl}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    'X-App-ID': appId,
                    'X-User-ID': userId,
                    ...options.headers,
                },
                ...options,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API call failed:', error);
            throw error;
        }
    }, [apiUrl, appId, userId]);

    const updatePreferences = useCallback(async (newPreferences: NotificationPreferences): Promise<boolean> => {
        try {
            setIsLoading(true);
            const success = await apiCall<{ success: boolean }>('/preferences', {
                method: 'PUT',
                body: JSON.stringify({ appId, userId, preferences: newPreferences }),
            });

            if (success.success) {
                setPreferences(newPreferences);
                return true;
            }
            return false;
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to update preferences');
            return false;
        } finally {
            setIsLoading(false);
        }
    }, [apiCall, appId, userId]);

    const updateChannelPreference = useCallback(async (channel: keyof DeliveryChannels, enabled: boolean): Promise<boolean> => {
        if (!preferences) return false;

        const newPreferences = {
            ...preferences,
            channels: {
                ...preferences.channels,
                [channel]: enabled,
            },
        };

        return updatePreferences(newPreferences);
    }, [preferences, updatePreferences]);

    const updateQuietHours = useCallback(async (enabled: boolean, startTime?: string, endTime?: string): Promise<boolean> => {
        if (!preferences) return false;

        const newPreferences = {
            ...preferences,
            quietHours: {
                ...preferences.quietHours,
                enabled,
                startTime: startTime || preferences.quietHours?.startTime || '22:00',
                endTime: endTime || preferences.quietHours?.endTime || '08:00',
            },
        };

        return updatePreferences(newPreferences);
    }, [preferences, updatePreferences]);

    useEffect(() => {
        const fetchPreferences = async () => {
            try {
                setIsLoading(true);
                const data = await apiCall<{ preferences: NotificationPreferences }>('/preferences');
                setPreferences(data.preferences);
            } catch (error) {
                setError(error instanceof Error ? error.message : 'Failed to fetch preferences');
            } finally {
                setIsLoading(false);
            }
        };

        fetchPreferences();
    }, [apiCall]);

    return {
        preferences,
        updatePreferences,
        isLoading,
        error,
        updateChannelPreference,
        updateQuietHours,
    };
}

// Hook for managing a single notification
export function useNotification(config: NotificationsConfig, notificationId: string): UseNotificationReturn {
    const [notification, setNotification] = useState<BaseNotification | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const { apiUrl, appId, userId } = config;

    const apiCall = useCallback(async <T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<T> => {
        try {
            const response = await fetch(`${apiUrl}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    'X-App-ID': appId,
                    'X-User-ID': userId,
                    ...options.headers,
                },
                ...options,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API call failed:', error);
            throw error;
        }
    }, [apiUrl, appId, userId]);

    const fetchNotification = useCallback(async () => {
        try {
            setIsLoading(true);
            const data = await apiCall<{ notification: BaseNotification }>(`/notifications/${notificationId}`);
            setNotification(data.notification);
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to fetch notification');
        } finally {
            setIsLoading(false);
        }
    }, [apiCall, notificationId]);

    const markAsRead = useCallback(async (): Promise<boolean> => {
        try {
            const success = await apiCall<{ success: boolean }>(`/notifications/${notificationId}/read`, {
                method: 'PUT',
                body: JSON.stringify({ appId, userId }),
            });

            if (success.success && notification) {
                setNotification({ ...notification, isRead: true });
                return true;
            }
            return false;
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to mark notification as read');
            return false;
        }
    }, [apiCall, notificationId, appId, userId, notification]);

    const deleteNotification = useCallback(async (): Promise<boolean> => {
        try {
            const success = await apiCall<{ success: boolean }>(`/notifications/${notificationId}`, {
                method: 'DELETE',
                body: JSON.stringify({ appId, userId }),
            });

            if (success.success) {
                setNotification(null);
                return true;
            }
            return false;
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to delete notification');
            return false;
        }
    }, [apiCall, notificationId, appId, userId]);

    const refresh = useCallback(async () => {
        await fetchNotification();
    }, [fetchNotification]);

    useEffect(() => {
        if (notificationId) {
            fetchNotification();
        }
    }, [notificationId, fetchNotification]);

    return {
        notification,
        isLoading,
        error,
        markAsRead,
        delete: deleteNotification,
        refresh,
    };
}

// Hook for notification statistics
export function useNotificationStats(config: NotificationsConfig): UseNotificationStatsReturn {
    const [stats, setStats] = useState<{
        total: number;
        unread: number;
        byType: Record<string, number>;
        byPriority: Record<string, number>;
        byStatus: Record<string, number>;
    } | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const { apiUrl, appId, userId } = config;

    const apiCall = useCallback(async <T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<T> => {
        try {
            const response = await fetch(`${apiUrl}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    'X-App-ID': appId,
                    'X-User-ID': userId,
                    ...options.headers,
                },
                ...options,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API call failed:', error);
            throw error;
        }
    }, [apiUrl, appId, userId]);

    const fetchStats = useCallback(async () => {
        try {
            setIsLoading(true);
            const data = await apiCall<{
                total: number;
                unread: number;
                byType: Record<string, number>;
                byPriority: Record<string, number>;
                byStatus: Record<string, number>;
            }>('/notifications/stats');
            setStats(data);
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to fetch notification stats');
        } finally {
            setIsLoading(false);
        }
    }, [apiCall]);

    const refresh = useCallback(async () => {
        await fetchStats();
    }, [fetchStats]);

    useEffect(() => {
        fetchStats();
    }, [fetchStats]);

    return {
        stats,
        isLoading,
        error,
        refresh,
    };
}

// Hook for push notification management
export function usePushNotifications(config: NotificationsConfig): UsePushNotificationsReturn {
    const [isSupported, setIsSupported] = useState(false);
    const [isEnabled, setIsEnabled] = useState(false);
    const [deviceToken, setDeviceToken] = useState<string | null>(null);
    const [permissionStatus, setPermissionStatus] = useState<'granted' | 'denied' | 'not-determined' | null>(null);

    const { apiUrl, appId, userId } = config;

    const apiCall = useCallback(async <T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<T> => {
        try {
            const response = await fetch(`${apiUrl}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    'X-App-ID': appId,
                    'X-User-ID': userId,
                    ...options.headers,
                },
                ...options,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API call failed:', error);
            throw error;
        }
    }, [apiUrl, appId, userId]);

    private async requestPushNotificationPermission(): Promise < boolean > {
        try {
            // This integrates with the actual push notification library
            // Implementation for React Native push notifications

            // Check if we have permission
            const authStatus = await Notifications.getPermissionsAsync();
            let finalStatus = authStatus.status;

            // If not determined, ask for permission
            if(finalStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
    }

    // If still not granted, return false
    if (finalStatus !== 'granted') {
        console.log('Push notification permission denied');
        return false;
    }

    // Get push token
    const token = await Notifications.getExpoPushTokenAsync({
        projectId: this.config.expoProjectId,
    });

    // Store token for later use
    this.pushToken = token.data;

    console.log('Push notification permission granted, token:', token.data);
    return true;
} catch (error) {
    console.error('Failed to request push notification permission:', error);
    return false;
}
    }

    private async registerDeviceForPushNotifications(): Promise < boolean > {
    try {
        // This integrates with the actual push notification library
        // Register device with the notification service

        if(!this.pushToken) {
    console.log('No push token available');
    return false;
}

// Register with backend service
const response = await this.apiClient.post('/api/devices/register', {
    token: this.pushToken,
    platform: Platform.OS,
    appVersion: this.config.appVersion,
    deviceId: this.config.deviceId,
});

if (response.success) {
    console.log('Device registered for push notifications');
    return true;
}

return false;
        } catch (error) {
    console.error('Failed to register device:', error);
    return false;
}
    }

    private async unregisterDeviceFromPushNotifications(): Promise < boolean > {
    try {
        // This integrates with the actual push notification library
        // Unregister device from the notification service

        if(!this.pushToken) {
    console.log('No push token available');
    return false;
}

// Unregister from backend service
const response = await this.apiClient.post('/api/devices/unregister', {
    token: this.pushToken,
    platform: Platform.OS,
});

if (response.success) {
    console.log('Device unregistered from push notifications');
    this.pushToken = null;
    return true;
}

return false;
        } catch (error) {
    console.error('Failed to unregister device:', error);
    return false;
}
    }

const requestPermission = useCallback(async (): Promise<boolean> => {
    try {
        const permission = await requestPushNotificationPermission();

        setPermissionStatus(permission ? 'granted' : 'denied');
        setIsEnabled(permission);

        return permission;
    } catch (error) {
        console.error('Failed to request push notification permission:', error);
        return false;
    }
}, []);

const registerDevice = useCallback(async (token: string): Promise<boolean> => {
    try {
        const success = await apiCall<{ success: boolean }>('/devices/register', {
            method: 'POST',
            body: JSON.stringify({ appId, userId, deviceToken: token, platform: 'react-native' }),
        });

        if (success.success) {
            setDeviceToken(token);
            return true;
        }
        return false;
    } catch (error) {
        console.error('Failed to register device:', error);
        return false;
    }
}, [apiCall, appId, userId]);

const unregisterDevice = useCallback(async (): Promise<boolean> => {
    try {
        const success = await apiCall<{ success: boolean }>('/devices/unregister', {
            method: 'POST',
            body: JSON.stringify({ appId, userId }),
        });

        if (success.success) {
            setDeviceToken(null);
            return true;
        }
        return false;
    } catch (error) {
        console.error('Failed to unregister device:', error);
        return false;
    }
}, [apiCall, appId, userId]);

const onNotificationReceived = useCallback((callback: (notification: any) => void) => {
    console.log('Notification received callback registered');
}, []);

const onNotificationOpened = useCallback((callback: (notification: any) => void) => {
    console.log('Notification opened callback registered');
}, []);

useEffect(() => {
    setIsSupported(true);
    setPermissionStatus('not-determined');
}, []);

return {
    isSupported,
    isEnabled,
    deviceToken,
    permissionStatus,
    requestPermission,
    registerDevice,
    unregisterDevice,
    onNotificationReceived,
    onNotificationOpened,
};
}
