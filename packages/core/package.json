{"name": "@sparkstrand/notifications-core", "version": "0.0.0", "description": "Core types, interfaces, and utilities for the Spark Strand notifications system", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint . --max-warnings 0", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"zod": "^3.23.8"}, "devDependencies": {"@types/node": "^22.10.7", "eslint": "^9", "tsup": "^8.3.5", "typescript": "^5.7.3"}, "files": ["dist/**"], "publishConfig": {"access": "restricted"}, "repository": {"type": "git", "url": "https://github.com/sparkstrand/notifications.git", "directory": "packages/core"}, "keywords": ["notifications", "types", "interfaces", "utilities"], "license": "MIT"}