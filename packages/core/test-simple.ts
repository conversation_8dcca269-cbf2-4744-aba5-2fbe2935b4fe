import {
    notificationService,
    NotificationType,
    NotificationPriority
} from './src';

/**
 * Simple test script to verify the multi-tenant notification system
 * Run with: npx ts-node test-simple.ts
 */

async function testBasicFunctionality() {
    console.log('🧪 Testing Multi-Tenant Notification System...\n');

    try {
        // Test 1: Register a simple app
        console.log('1️⃣ Testing app registration...');
        await notificationService.registerApp({
            appId: 'test-app',
            name: 'Test Application',
            description: 'Test app for verification',
            providers: {
                novu: {
                    apiKey: 'test-novu-key',
                    appId: 'test-novu-app',
                    priority: 1,
                    supportedChannels: ['email', 'inApp']
                }
            },
            fallbackStrategy: 'failover',
            maxRetries: 2,
            retryDelay: 1000
        });
        console.log('✅ App registration successful\n');

        // Test 2: Set as default app
        console.log('2️⃣ Testing default app setting...');
        notificationService.setDefaultApp('test-app');
        const defaultApp = notificationService.getDefaultApp();
        console.log(`✅ Default app set to: ${defaultApp}\n`);

        // Test 3: Check if multi-tenant is enabled
        console.log('3️⃣ Testing multi-tenant mode...');
        const isMultiTenant = notificationService.isMultiTenantEnabled();
        console.log(`✅ Multi-tenant mode: ${isMultiTenant}\n`);

        // Test 4: Get registered apps
        console.log('4️⃣ Testing app listing...');
        const apps = notificationService.getRegisteredApps();
        console.log(`✅ Registered apps: ${apps.length}`);
        apps.forEach(app => {
            console.log(`   - ${app.name} (${app.appId})`);
        });
        console.log();

        // Test 5: Get provider status
        console.log('5️⃣ Testing provider status...');
        const providerStatus = notificationService.getProviderStatus();
        console.log(`✅ Active providers: ${Object.keys(providerStatus).length}`);
        Object.entries(providerStatus).forEach(([name, status]) => {
            console.log(`   - ${name}: ${status.type} (${status.isEnabled ? 'enabled' : 'disabled'})`);
        });
        console.log();

        // Test 6: Get app status
        console.log('6️⃣ Testing app status...');
        const appStatus = notificationService.getAppStatus();
        console.log(`✅ App status retrieved for ${Object.keys(appStatus).length} apps\n`);

        // Test 7: Check notification stats
        console.log('7️⃣ Testing notification statistics...');
        const stats = notificationService.getNotificationStats('test-app');
        console.log(`✅ Stats retrieved: ${stats.totalSent} total, ${stats.successRate.toFixed(1)}% success rate\n`);

        console.log('🎉 All basic tests passed! The system is working correctly.\n');

    } catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    }
}

// Run the test
if (require.main === module) {
    testBasicFunctionality().catch(console.error);
}

export { testBasicFunctionality }; 