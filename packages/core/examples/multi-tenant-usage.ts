import {
    notificationService,
    NotificationService,
    NotificationType,
    NotificationPriority,
    NotificationStatus
} from '../src';

/**
 * Multi-Tenant Notification System Usage Examples
 * 
 * This example demonstrates how to:
 * 1. Register multiple applications with different provider configurations
 * 2. Send notifications through different apps
 * 3. Use fallback strategies and provider routing
 * 4. Monitor and manage the system
 */

async function main() {
    console.log('🚀 Starting Multi-Tenant Notification System Demo\n');

    // ============================================================================
    // 1. REGISTER APPLICATIONS
    // ============================================================================

    console.log('📱 Registering Applications...\n');

    // Register SportyExpats (Full provider setup)
    await notificationService.registerApp({
        appId: 'sporty-expats',
        name: 'SportyExpats',
        description: 'Sports event management platform',
        providers: {
            novu: {
                apiKey: process.env.SPORTY_EXPATS_NOVU_API_KEY || 'sk_sporty_novu_key',
                appId: process.env.SPORTY_EXPATS_NOVU_APP_ID || 'sporty_novu_app',
                priority: 1,
                supportedChannels: ['email', 'sms', 'push', 'inApp']
            },
            resend: {
                apiKey: process.env.SPORTY_EXPATS_RESEND_API_KEY || 're_sporty_resend_key',
                fromEmail: '<EMAIL>',
                priority: 2,
                supportedChannels: ['email']
            },
            twilio: {
                accountSid: process.env.SPORTY_EXPATS_TWILIO_ACCOUNT_SID || 'AC_sporty_twilio',
                authToken: process.env.SPORTY_EXPATS_TWILIO_AUTH_TOKEN || 'sporty_twilio_token',
                fromPhoneNumber: '+**********',
                priority: 2,
                supportedChannels: ['sms']
            }
        },
        fallbackStrategy: 'hybrid',
        maxRetries: 3,
        retryDelay: 1000,
        rateLimits: {
            requestsPerMinute: 100,
            requestsPerHour: 1000,
            requestsPerDay: 10000
        }
    });

    // Register TaxDone (Limited provider setup - Novu only)
    await notificationService.registerApp({
        appId: 'tax-done',
        name: 'TaxDone',
        description: 'Tax preparation service',
        providers: {
            novu: {
                apiKey: process.env.TAX_DONE_NOVU_API_KEY || 'sk_tax_novu_key',
                appId: process.env.TAX_DONE_NOVU_APP_ID || 'tax_novu_app',
                priority: 1,
                supportedChannels: ['email', 'sms', 'push', 'inApp']
            }
        },
        fallbackStrategy: 'failover',
        maxRetries: 2,
        retryDelay: 2000,
        rateLimits: {
            requestsPerMinute: 50,
            requestsPerHour: 500,
            requestsPerDay: 5000
        }
    });

    // Register VentureDirection (Medium provider setup)
    await notificationService.registerApp({
        appId: 'venture-direction',
        name: 'VentureDirection',
        description: 'Business consulting platform',
        providers: {
            novu: {
                apiKey: process.env.VENTURE_DIRECTION_NOVU_API_KEY || 'sk_venture_novu_key',
                appId: process.env.VENTURE_DIRECTION_NOVU_APP_ID || 'venture_novu_app',
                priority: 1,
                supportedChannels: ['email', 'sms', 'push', 'inApp']
            },
            resend: {
                apiKey: process.env.VENTURE_DIRECTION_RESEND_API_KEY || 're_venture_resend_key',
                fromEmail: '<EMAIL>',
                priority: 2,
                supportedChannels: ['email']
            }
        },
        fallbackStrategy: 'failover',
        maxRetries: 3,
        retryDelay: 1500,
        rateLimits: {
            requestsPerMinute: 75,
            requestsPerHour: 750,
            requestsPerDay: 7500
        }
    });

    console.log('✅ All applications registered successfully!\n');

    // ============================================================================
    // 2. SET DEFAULT APP AND SEND NOTIFICATIONS
    // ============================================================================

    console.log('📨 Sending Notifications...\n');

    // Set SportyExpats as default app
    notificationService.setDefaultApp('sporty-expats');
    console.log(`Default app set to: ${notificationService.getDefaultApp()}\n`);

    // Send welcome notification to a new user
    try {
        const welcomeNotification = await notificationService.sendWelcomeNotification(
            'user-123',
            {
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                phone: '+**********'
            },
            {
                priority: NotificationPriority.HIGH,
                channels: { email: true, inApp: true, push: true }
            }
        );
        console.log('✅ Welcome notification sent:', welcomeNotification.id);
    } catch (error) {
        console.error('❌ Failed to send welcome notification:', error);
    }

    // Send event reminder
    try {
        const eventReminder = await notificationService.sendEventReminder(
            'user-456',
            'Football Match',
            new Date('2024-02-15T15:00:00Z'),
            {
                email: '<EMAIL>',
                firstName: 'Jane',
                lastName: 'Smith'
            },
            {
                priority: NotificationPriority.NORMAL,
                channels: { email: true, sms: true, inApp: true }
            }
        );
        console.log('✅ Event reminder sent:', eventReminder.id);
    } catch (error) {
        console.error('❌ Failed to send event reminder:', error);
    }

    // Send payment notification
    try {
        const paymentNotification = await notificationService.sendPaymentNotification(
            'user-789',
            99.99,
            'USD',
            true,
            {
                email: '<EMAIL>',
                firstName: 'Bob',
                lastName: 'Wilson'
            },
            {
                priority: NotificationPriority.NORMAL,
                channels: { email: true, inApp: true }
            }
        );
        console.log('✅ Payment notification sent:', paymentNotification.id);
    } catch (error) {
        console.error('❌ Failed to send payment notification:', error);
    }

    // ============================================================================
    // 3. SEND NOTIFICATIONS TO DIFFERENT APPS
    // ============================================================================

    console.log('\n🔄 Sending Notifications to Different Apps...\n');

    // Send notification to TaxDone app
    try {
        const taxNotification = await notificationService.sendNotification(
            'user-tax-123',
            NotificationType.SYSTEM_UPDATE,
            'Tax Season Update',
            'Tax season is now open! Start preparing your documents.',
            {
                email: '<EMAIL>',
                firstName: 'Tax',
                lastName: 'Payer'
            },
            {
                appId: 'tax-done',
                priority: NotificationPriority.HIGH,
                channels: { email: true, inApp: true }
            }
        );
        console.log('✅ TaxDone notification sent:', taxNotification.id);
    } catch (error) {
        console.error('❌ Failed to send TaxDone notification:', error);
    }

    // Send notification to VentureDirection app
    try {
        const ventureNotification = await notificationService.sendNotification(
            'user-venture-456',
            NotificationType.EVENT_REMINDER,
            'Consultation Reminder',
            'Your business consultation is scheduled for tomorrow at 2 PM.',
            {
                email: '<EMAIL>',
                firstName: 'Entre',
                lastName: 'Preneur'
            },
            {
                appId: 'venture-direction',
                priority: NotificationPriority.NORMAL,
                channels: { email: true, inApp: true }
            }
        );
        console.log('✅ VentureDirection notification sent:', ventureNotification.id);
    } catch (error) {
        console.error('❌ Failed to send VentureDirection notification:', error);
    }

    // ============================================================================
    // 4. BULK NOTIFICATIONS
    // ============================================================================

    console.log('\n📬 Sending Bulk Notifications...\n');

    try {
        const bulkNotifications = await notificationService.sendBulkNotifications({
            appId: 'sporty-expats',
            globalOptions: {
                priority: NotificationPriority.NORMAL,
                channels: { email: true, inApp: true }
            },
            notifications: [
                {
                    userId: 'user-bulk-1',
                    type: NotificationType.EVENT_REMINDER,
                    title: 'Basketball Game Tomorrow',
                    content: 'Don\'t forget your basketball game tomorrow!',
                    metadata: { email: '<EMAIL>', firstName: 'Player', lastName: 'One' }
                },
                {
                    userId: 'user-bulk-2',
                    type: NotificationType.EVENT_REMINDER,
                    title: 'Tennis Match This Weekend',
                    content: 'Your tennis match is scheduled for this weekend.',
                    metadata: { email: '<EMAIL>', firstName: 'Player', lastName: 'Two' }
                },
                {
                    userId: 'user-bulk-3',
                    type: NotificationType.EVENT_REMINDER,
                    title: 'Swimming Practice',
                    content: 'Swimming practice starts in 1 hour.',
                    metadata: { email: '<EMAIL>', firstName: 'Player', lastName: 'Three' }
                }
            ]
        });

        const successCount = bulkNotifications.filter(n => n.status === NotificationStatus.DELIVERED).length;
        console.log(`✅ Bulk notifications completed: ${successCount}/${bulkNotifications.length} successful`);
    } catch (error) {
        console.error('❌ Failed to send bulk notifications:', error);
    }

    // ============================================================================
    // 5. USER PREFERENCES
    // ============================================================================

    console.log('\n⚙️ Managing User Preferences...\n');

    try {
        // Update user preferences
        await notificationService.updateUserPreferences('user-123', 'sporty-expats', {
            email: true,
            sms: false,
            push: true,
            inApp: true,
            quietHours: {
                enabled: true,
                start: '22:00',
                end: '08:00',
                timezone: 'America/New_York'
            },
            frequency: {
                email: 'immediate',
                push: 'immediate',
                sms: 'daily'
            }
        });

        // Get user preferences
        const preferences = notificationService.getUserPreferences('user-123', 'sporty-expats');
        console.log('✅ User preferences updated:', preferences);
    } catch (error) {
        console.error('❌ Failed to update user preferences:', error);
    }

    // ============================================================================
    // 6. MONITORING AND STATISTICS
    // ============================================================================

    console.log('\n📊 System Monitoring and Statistics...\n');

    try {
        // Get provider status
        const providerStatus = notificationService.getProviderStatus();
        console.log('🔌 Provider Status:', Object.keys(providerStatus).length, 'providers active');

        // Get app status
        const appStatus = notificationService.getAppStatus();
        console.log('📱 App Status:', Object.keys(appStatus).length, 'apps registered');

        // Get notification statistics for SportyExpats
        const sportyStats = notificationService.getNotificationStats('sporty-expats');
        console.log('📈 SportyExpats Stats:', {
            totalSent: sportyStats.totalSent,
            successRate: `${sportyStats.successRate.toFixed(1)}%`,
            averageResponseTime: `${sportyStats.averageResponseTime.toFixed(0)}ms`
        });

        // Get notification history
        const history = notificationService.getNotificationHistory('sporty-expats', 5);
        console.log('📜 Recent Notifications:', history.length, 'notifications');

        // Get registered apps
        const registeredApps = notificationService.getRegisteredApps();
        console.log('📋 Registered Apps:', registeredApps.map(app => app.name).join(', '));

    } catch (error) {
        console.error('❌ Failed to get system information:', error);
    }

    // ============================================================================
    // 7. DEMONSTRATE FALLBACK STRATEGIES
    // ============================================================================

    console.log('\n🔄 Demonstrating Fallback Strategies...\n');

    console.log('📧 Email Channel Routing:');
    console.log('  • SportyExpats: Resend (priority 2) → Novu (priority 1) if Resend fails');
    console.log('  • TaxDone: Novu only (no fallback)');
    console.log('  • VentureDirection: Resend (priority 2) → Novu (priority 1) if Resend fails\n');

    console.log('📱 SMS Channel Routing:');
    console.log('  • SportyExpats: Twilio (priority 2) → Novu (priority 1) if Twilio fails');
    console.log('  • TaxDone: Novu only (no fallback)');
    console.log('  • VentureDirection: Novu only (no fallback)\n');

    console.log('🔔 Push/In-App Channel Routing:');
    console.log('  • All apps: Novu only (no alternatives available)\n');

    // ============================================================================
    // 8. SYSTEM SUMMARY
    // ============================================================================

    console.log('\n🎯 System Summary\n');
    console.log('✅ Multi-tenant architecture implemented');
    console.log('✅ Provider factory with automatic routing');
    console.log('✅ Fallback strategies (failover, parallel, hybrid)');
    console.log('✅ Rate limiting per application');
    console.log('✅ User preferences and channel filtering');
    console.log('✅ Bulk notification support');
    console.log('✅ Comprehensive monitoring and metrics');
    console.log('✅ Application isolation and security');
    console.log('✅ Vendor independence (Novu, Resend, Twilio)');

    console.log('\n🚀 Multi-Tenant Notification System Demo Completed!\n');
}

// Run the demo
if (require.main === module) {
    main().catch(console.error);
}

export { main as runMultiTenantDemo }; 