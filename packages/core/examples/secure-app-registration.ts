import {
    SecureAppRegistrationService,
    SecureAppRegistrationServiceConfig
} from '../src/services/secureAppRegistrationService';
import { SimpleAdminNotificationService } from '../src/services/adminNotificationService';
import { MultiTenantNotificationService } from '../src/services/multiTenantService';
import { ProviderFactory } from '../src/providers/factory';

// Example: Setting up the secure app registration system
async function setupSecureSystem() {
    // 1. Create admin notification service
    const adminNotificationService = new SimpleAdminNotificationService({
        adminEmail: '<EMAIL>',
        slack: {
            webhookUrl: 'https://hooks.slack.com/services/YOUR_WEBHOOK_URL',
            channel: '#notifications',
            username: 'Notification System'
        }
    });

    // 2. Create the core services
    const providerFactory = new ProviderFactory();
    const multiTenantService = new MultiTenantNotificationService(providerFactory);

    // 3. Configure the secure registration service
    const config: SecureAppRegistrationServiceConfig = {
        adminEmail: '<EMAIL>',
        adminSlackWebhook: 'https://hooks.slack.com/services/YOUR_WEBHOOK_URL',
        approvalRequired: true,
        autoApproveDomains: ['sparkstrand.com', 'trusted-partner.com'], // Auto-approve trusted domains
        defaultRateLimits: {
            requestsPerMinute: 100,
            requestsPerHour: 1000,
            requestsPerDay: 10000
        },
        adminNotificationService
    };

    // 4. Create the secure registration service
    const secureRegistrationService = new SecureAppRegistrationService(
        multiTenantService,
        providerFactory,
        config
    );

    return secureRegistrationService;
}

// Example: App registration flow
async function demonstrateAppRegistration() {
    const secureService = await setupSecureSystem();

    console.log('=== APP REGISTRATION FLOW ===\n');

    // 1. App submits registration request
    console.log('1. App submits registration request...');
    const registrationResult = await secureService.registerApp({
        appId: 'sporty-expats',
        name: 'SportyExpats',
        description: 'Sports community platform for expats',
        contactEmail: '<EMAIL>',
        contactName: 'John Developer',
        website: 'https://sportyexpats.com',
        businessType: 'SaaS Platform',
        expectedVolume: 1000,
        providers: {
            novu: {
                apiKey: 'novu_api_key_here',
                appId: 'novu_app_id_here',
                priority: 1,
                supportedChannels: ['email', 'sms', 'push', 'inApp']
            },
            resend: {
                apiKey: 'resend_api_key_here',
                fromEmail: '<EMAIL>',
                priority: 2,
                supportedChannels: ['email']
            }
        },
        fallbackStrategy: 'failover',
        maxRetries: 3,
        retryDelay: 1000,
        rateLimits: {
            requestsPerMinute: 50,
            requestsPerHour: 500,
            requestsPerDay: 5000
        }
    });

    console.log('Registration Result:', registrationResult);
    console.log('\n--- Admin receives notification ---');
    console.log('Admin checks email/Slack for approval request\n');

    // 2. Admin approves the application
    console.log('2. Admin approves the application...');
    const approvalResult = await secureService.approveApp({
        appId: 'sporty-expats',
        action: 'APPROVE',
        reason: 'Legitimate business application',
        adminNotes: 'Approved after reviewing business details and provider credentials'
    });

    console.log('Approval Result:', approvalResult);
    console.log('\n--- Admin receives API key ---');
    console.log(`API Key: ${approvalResult.apiKey}\n`);

    // 3. App uses API key for authentication
    console.log('3. App uses API key for authentication...');
    const authResult = await secureService.authenticateApp({
        appId: 'sporty-expats',
        apiKey: approvalResult.apiKey!
    });

    console.log('Authentication Result:', authResult);

    // 4. App can now send notifications
    console.log('\n4. App can now send notifications through the system!');
    console.log('The app is fully registered and authenticated.');
}

// Example: Auto-approval for trusted domains
async function demonstrateAutoApproval() {
    const secureService = await setupSecureSystem();

    console.log('\n=== AUTO-APPROVAL FOR TRUSTED DOMAINS ===\n');

    // This app will be auto-approved because it's from a trusted domain
    const autoApprovalResult = await secureService.registerApp({
        appId: 'internal-tool',
        name: 'Internal Development Tool',
        description: 'Internal tool for development team',
        contactEmail: '<EMAIL>', // Trusted domain
        contactName: 'Internal Developer',
        providers: {
            resend: {
                apiKey: 'internal_resend_key',
                fromEmail: '<EMAIL>',
                priority: 1,
                supportedChannels: ['email']
            }
        }
    });

    console.log('Auto-Approval Result:', autoApprovalResult);
    console.log('This app was automatically approved due to trusted domain!');
}

// Example: Admin management functions
async function demonstrateAdminFunctions() {
    const secureService = await setupSecureSystem();

    console.log('\n=== ADMIN MANAGEMENT FUNCTIONS ===\n');

    // Get pending registrations
    const pending = secureService.getPendingRegistrations();
    console.log('Pending Registrations:', pending.length);

    // Get approved apps
    const approved = secureService.getApprovedApps();
    console.log('Approved Apps:', approved.length);

    // Revoke an app (if needed)
    // const revoked = await secureService.revokeApp('sporty-expats', 'Violation of terms');
    // console.log('App revoked:', revoked);
}

// Run the examples
async function runExamples() {
    try {
        await demonstrateAppRegistration();
        await demonstrateAutoApproval();
        await demonstrateAdminFunctions();

        console.log('\n=== SECURE SYSTEM DEMONSTRATION COMPLETE ===');
        console.log('The system now has proper security with:');
        console.log('✅ Admin approval workflow');
        console.log('✅ API key authentication');
        console.log('✅ Admin notifications (email/Slack)');
        console.log('✅ Auto-approval for trusted domains');
        console.log('✅ App revocation capabilities');

    } catch (error) {
        console.error('Example failed:', error);
    }
}

// Export for use in other files
export {
    setupSecureSystem,
    demonstrateAppRegistration,
    demonstrateAutoApproval,
    demonstrateAdminFunctions,
    runExamples
};

// Run if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
    runExamples();
} 