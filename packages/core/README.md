# @sparkstrand/notifications-core

A unified notification service that handles multiple applications with automatic provider routing, fallback strategies, and vendor independence.

## 🚀 **Quick Start**

```typescript
import { notificationService, NotificationType, NotificationPriority } from '@sparkstrand/notifications-core';

// 1. Register your application
await notificationService.registerApp({
    appId: 'sporty-expats',
    name: 'SportyExpats',
    providers: {
        novu: {
            apiKey: 'your-novu-api-key',
            appId: 'your-novu-app-id',
            priority: 1,
            supportedChannels: ['email', 'sms', 'push', 'inApp']
        },
        resend: {
            apiKey: 'your-resend-api-key',
            fromEmail: '<EMAIL>',
            priority: 2,
            supportedChannels: ['email']
        }
    },
    fallbackStrategy: 'hybrid'
});

// 2. Send notifications through the service
await notificationService.sendNotification(
    'user-123',
    NotificationType.EVENT_REMINDER,
    'Football Match Tomorrow!',
    'Don\'t forget your match at 3 PM',
    { email: '<EMAIL>', firstName: 'John' },
    { priority: NotificationPriority.HIGH }
);
```

## 🏗️ **How It Works**

The system automatically handles **multiple applications** with:
- **Isolated configurations** and API keys per application
- **Automatic routing** through the best available providers
- **Fallback strategies** when providers fail
- **Separate rate limits** and quotas per app

### **Simple Flow**
1. **Register your app** with provider configurations
2. **Send notifications** through the unified service
3. **System automatically routes** through the best providers
4. **Fallback handling** ensures delivery even if providers fail

## 📚 **Core API**

### **Application Registration**

```typescript
await notificationService.registerApp({
    appId: 'your-app-id',
    name: 'Your App Name',
    description: 'App description',
    providers: {
        novu: { /* Novu configuration */ },
        resend: { /* Resend configuration */ },
        twilio: { /* Twilio configuration */ }
    },
    fallbackStrategy: 'failover' | 'parallel' | 'hybrid',
    maxRetries: 3,
    retryDelay: 1000,
    rateLimits: {
        requestsPerMinute: 100,
        requestsPerHour: 1000,
        requestsPerDay: 10000
    }
});
```

### **Sending Notifications**

```typescript
// Basic notification
await notificationService.sendNotification(
    userId,
    type,
    title,
    content,
    metadata,
    options
);

// Pre-built notification types
await notificationService.sendWelcomeNotification(userId, metadata, options);
await notificationService.sendEventReminder(userId, eventName, eventTime, metadata, options);
await notificationService.sendPaymentNotification(userId, amount, currency, isSuccess, metadata, options);
await notificationService.sendLoginNotification(userId, deviceInfo, location, metadata, options);
```

### **Bulk Notifications**

```typescript
await notificationService.sendBulkNotifications({
    appId: 'your-app',
    globalOptions: { priority: NotificationPriority.NORMAL },
    notifications: [
        { userId: 'user1', type: NotificationType.EVENT_REMINDER, title: '...', content: '...' },
        { userId: 'user2', type: NotificationType.EVENT_REMINDER, title: '...', content: '...' }
    ]
});
```

### **User Preferences**

```typescript
// Update preferences
await notificationService.updateUserPreferences('user-123', 'app-id', {
    email: true,
    sms: false,
    push: true,
    quietHours: { enabled: true, start: '22:00', end: '08:00' }
});

// Get preferences
const prefs = notificationService.getUserPreferences('user-123', 'app-id');
```

## 🔄 **Provider Configuration**

### **Novu Provider**
```typescript
novu: {
    apiKey: 'your-novu-api-key',
    appId: 'your-novu-app-id',
    priority: 1,  // Lower number = higher priority
    supportedChannels: ['email', 'sms', 'push', 'inApp']
}
```

### **Resend Provider**
```typescript
resend: {
    apiKey: 'your-resend-api-key',
    fromEmail: '<EMAIL>',
    priority: 2,
    supportedChannels: ['email']
}
```

### **Twilio Provider**
```typescript
twilio: {
    accountSid: 'your-twilio-account-sid',
    authToken: 'your-twilio-auth-token',
    fromPhoneNumber: '+**********',
    priority: 2,
    supportedChannels: ['sms']
}
```

## 🎯 **Fallback Strategies**

### **Failover (Sequential)**
```typescript
fallbackStrategy: 'failover'
// Try providers in sequence: Resend → Novu → Twilio
```

### **Parallel (Simultaneous)**
```typescript
fallbackStrategy: 'parallel'
// Try all providers at once, return first success
```

### **Hybrid (Primary + Parallel Fallbacks)**
```typescript
fallbackStrategy: 'hybrid'
// Try primary first, then parallel fallbacks
```

## 📊 **Monitoring & Analytics**

```typescript
// Provider status
const providerStatus = notificationService.getProviderStatus();

// App status
const appStatus = notificationService.getAppStatus();

// Notification statistics
const stats = notificationService.getNotificationStats('app-id');

// Notification history
const history = notificationService.getNotificationHistory('app-id', 100);
```

## 🔧 **Environment Variables**

Each application manages its own configuration and registers with the notifications system:

```bash
# Your App
YOUR_APP_NOVU_API_KEY=sk_123...
YOUR_APP_RESEND_API_KEY=re_456...
YOUR_APP_TWILIO_ACCOUNT_SID=AC789...
YOUR_APP_TWILIO_AUTH_TOKEN=your_twilio_token

# In SportyExpats app (.env)
SPORTY_EXPATS_NOVU_API_KEY=sk_123...
SPORTY_EXPATS_RESEND_API_KEY=re_456...
SPORTY_EXPATS_TWILIO_ACCOUNT_SID=AC789...

# In TaxDone app (.env)
TAX_DONE_NOVU_API_KEY=sk_abc...
TAX_DONE_RESEND_API_KEY=re_def...

# In VentureDirection app (.env)
VENTURE_DIRECTION_NOVU_API_KEY=sk_xyz...
VENTURE_DIRECTION_RESEND_API_KEY=re_uvw...
```

**Each app registers independently:**

```typescript
// In SportyExpats app
await notificationService.registerApp({
    appId: 'sporty-expats',
    name: 'SportyExpats',
    providers: {
        novu: {
            apiKey: process.env.SPORTY_EXPATS_NOVU_API_KEY, // Your app's key
            appId: process.env.SPORTY_EXPATS_NOVU_APP_ID,   // Your app's ID
            priority: 1
        },
        resend: {
            apiKey: process.env.SPORTY_EXPATS_RESEND_API_KEY, // Your app's key
            fromEmail: '<EMAIL>',
            priority: 2
        }
    }
});
```

## 📱 **Real-World Examples**

### **SportyExpats (Full Setup)**
```typescript
await notificationService.registerApp({
    appId: 'sporty-expats',
    name: 'SportyExpats',
    providers: {
        novu: { apiKey: 'sk_sporty_novu', priority: 1 },
        resend: { apiKey: 're_sporty_resend', priority: 2 },
        twilio: { accountSid: 'AC_sporty', priority: 2 }
    },
    fallbackStrategy: 'hybrid'
});
```

### **TaxDone (Novu Only)**
```typescript
await notificationService.registerApp({
    appId: 'tax-done',
    name: 'TaxDone',
    providers: {
        novu: { apiKey: 'sk_tax_novu', priority: 1 }
    },
    fallbackStrategy: 'failover'
});
```

## 🧪 **Testing & Examples**

### **Simple Test**
```bash
npx ts-node test-simple.ts
```

### **Full Demo**
```bash
npx ts-node examples/multi-tenant-usage.ts
```

## 🎉 **Key Benefits**

- **🚀 Unified API**: One service handles all notification operations
- **🏗️ Multi-application support**: Handle unlimited applications with isolation
- **🔄 Smart routing**: Automatic provider selection and fallbacks
- **💰 Cost optimization**: Use cheaper providers when available
- **📊 Monitoring**: Real-time metrics and performance tracking
- **🔒 Security**: App isolation and rate limiting
- **📱 Vendor independence**: Easy to switch between providers

## 📚 **Additional Resources**

- **Implementation Summary**: [IMPLEMENTATION_SUMMARY.md](./IMPLEMENTATION_SUMMARY.md)
- **Usage Examples**: [examples/multi-tenant-usage.ts](./examples/multi-tenant-usage.ts)
- **Simple Test**: [test-simple.ts](./test-simple.ts)

---

**A unified, production-ready solution for handling notifications across multiple applications with vendor independence and intelligent routing.** 