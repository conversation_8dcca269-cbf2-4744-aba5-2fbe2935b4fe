import {
    NotificationProvider,
    CreateNotificationRequest,
    DeliveryChannels,
    NotificationPriority,
    ProviderFactory as IProviderFactory,
} from "./types";

/**
 * Base class for all notification providers
 * Provides common functionality and enforces the provider interface
 */
export abstract class BaseNotificationProvider implements NotificationProvider {
    abstract name: string;
    abstract isConfigured(): boolean;
    abstract isEnabled(): boolean;
    abstract getPriority(): number;
    abstract send(notification: CreateNotificationRequest): Promise<boolean>;
    abstract getSupportedChannels(): (keyof DeliveryChannels)[];

    /**
     * Default validation for notifications
     * Can be overridden by specific providers
     */
    validate(notification: CreateNotificationRequest): boolean {
        return !!(
            notification.appId &&
            notification.userId &&
            notification.type &&
            notification.title &&
            notification.content
        );
    }

    /**
     * Get default delivery channels for this provider
     * Can be overridden by specific providers
     */
    protected getDefaultChannels(): Partial<DeliveryChannels> {
        return {
            inApp: true,
            email: false,
            sms: false,
            push: false,
            webhook: false,
            slack: false,
            discord: false,
            telegram: false,
        };
    }

    /**
     * Merge user-specified channels with default channels
     */
    protected mergeChannels(
        defaultChannels: Partial<DeliveryChannels>,
        userChannels?: Partial<DeliveryChannels>
    ): DeliveryChannels {
        return {
            inApp: false,
            email: false,
            sms: false,
            push: false,
            webhook: false,
            slack: false,
            discord: false,
            telegram: false,
            ...defaultChannels,
            ...userChannels,
        };
    }

    /**
     * Get the priority level for this notification
     */
    protected getNotificationPriority(notification: CreateNotificationRequest): NotificationPriority {
        return notification.priority || NotificationPriority.NORMAL;
    }

    /**
     * Check if the notification should be sent based on quiet hours
     * This is a basic implementation - can be enhanced
     */
    protected shouldSendNow(preferences?: any): boolean {
        if (!preferences?.quietHoursEnabled) {
            return true;
        }

        const now = new Date();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();
        const currentTime = currentHour * 60 + currentMinute;

        const startTime = this.parseTimeString(preferences.quietHoursStart);
        const endTime = this.parseTimeString(preferences.quietHoursEnd);

        if (startTime <= endTime) {
            // Same day (e.g., 22:00 to 08:00)
            return currentTime < startTime || currentTime > endTime;
        } else {
            // Overnight (e.g., 22:00 to 08:00)
            return currentTime < startTime && currentTime > endTime;
        }
    }

    /**
     * Parse time string (HH:MM) to minutes since midnight
     */
    private parseTimeString(timeString: string): number {
        const [hours, minutes] = timeString.split(":").map(Number);
        return hours * 60 + minutes;
    }

    /**
     * Log provider activity for debugging and monitoring
     */
    protected log(level: "info" | "warn" | "error", message: string, data?: any): void {
        const timestamp = new Date().toISOString();
        const logData = {
            timestamp,
            provider: this.name,
            level,
            message,
            data,
        };

        switch (level) {
            case "info":
                console.log(`[${timestamp}] [${this.name}] INFO: ${message}`, data || "");
                break;
            case "warn":
                console.warn(`[${timestamp}] [${this.name}] WARN: ${message}`, data || "");
                break;
            case "error":
                console.error(`[${timestamp}] [${this.name}] ERROR: ${message}`, data || "");
                break;
        }
    }
}

/**
 * Factory for managing notification providers
 * Handles provider registration, retrieval, and channel routing
 */
export class ProviderFactory implements IProviderFactory {
    private static instance: ProviderFactory;
    private providers = new Map<string, new () => NotificationProvider>();
    private providerInstances = new Map<string, NotificationProvider>();

    private constructor() { }

    /**
     * Get singleton instance
     */
    static getInstance(): ProviderFactory {
        if (!ProviderFactory.instance) {
            ProviderFactory.instance = new ProviderFactory();
        }
        return ProviderFactory.instance;
    }

    /**
     * Register a new notification provider
     */
    register(name: string, provider: new () => NotificationProvider): void {
        this.providers.set(name, provider);
        this.log("info", `Provider registered: ${name}`);
    }

    /**
     * Get a provider by name
     */
    getProvider(name: string): NotificationProvider | null {
        // Check if we already have an instance
        if (this.providerInstances.has(name)) {
            return this.providerInstances.get(name)!;
        }

        // Create new instance if not exists
        const ProviderClass = this.providers.get(name);
        if (!ProviderClass) {
            this.log("warn", `Provider not found: ${name}`);
            return null;
        }

        try {
            const instance = new ProviderClass();
            this.providerInstances.set(name, instance);
            this.log("info", `Provider instance created: ${name}`);
            return instance;
        } catch (error) {
            this.log("error", `Failed to create provider instance: ${name}`, error);
            return null;
        }
    }

    /**
     * Get all registered providers
     */
    getProviders(): NotificationProvider[] {
        const instances: NotificationProvider[] = [];

        for (const [name] of this.providers) {
            const instance = this.getProvider(name);
            if (instance) {
                instances.push(instance);
            }
        }

        return instances.sort((a, b) => a.getPriority() - b.getPriority());
    }

    /**
     * Get the best provider for a specific channel
     * Returns the provider with the highest priority that supports the channel
     */
    getProviderForChannel(channel: keyof DeliveryChannels): NotificationProvider | null {
        const providers = this.getProviders();

        for (const provider of providers) {
            if (provider.isEnabled() && provider.isConfigured()) {
                const supportedChannels = provider.getSupportedChannels();
                if (supportedChannels.includes(channel)) {
                    return provider;
                }
            }
        }

        this.log("warn", `No provider found for channel: ${channel}`);
        return null;
    }

    /**
     * Get all providers that support a specific channel
     */
    getProvidersForChannel(channel: keyof DeliveryChannels): NotificationProvider[] {
        const providers = this.getProviders();

        return providers.filter(provider =>
            provider.isEnabled() &&
            provider.isConfigured() &&
            provider.getSupportedChannels().includes(channel)
        ).sort((a, b) => a.getPriority() - b.getPriority());
    }

    /**
     * Check if a provider is available
     */
    isProviderAvailable(name: string): boolean {
        const provider = this.getProvider(name);
        return provider !== null && provider.isEnabled() && provider.isConfigured();
    }

    /**
     * Get provider status information
     */
    getProviderStatus(): Array<{
        name: string;
        enabled: boolean;
        configured: boolean;
        priority: number;
        supportedChannels: string[];
    }> {
        const providers = this.getProviders();

        return providers.map(provider => ({
            name: provider.name,
            enabled: provider.isEnabled(),
            configured: provider.isConfigured(),
            priority: provider.getPriority(),
            supportedChannels: provider.getSupportedChannels(),
        }));
    }

    /**
     * Clear all providers (useful for testing)
     */
    clear(): void {
        this.providers.clear();
        this.providerInstances.clear();
        this.log("info", "All providers cleared");
    }

    /**
     * Log factory activity
     */
    private log(level: "info" | "warn" | "error", message: string, data?: any): void {
        const timestamp = new Date().toISOString();
        const logData = {
            timestamp,
            component: "ProviderFactory",
            level,
            message,
            data,
        };

        switch (level) {
            case "info":
                console.log(`[${timestamp}] [ProviderFactory] INFO: ${message}`, data || "");
                break;
            case "warn":
                console.warn(`[${timestamp}] [ProviderFactory] WARN: ${message}`, data || "");
                break;
            case "error":
                console.error(`[${timestamp}] [ProviderFactory] ERROR: ${message}`, data || "");
                break;
        }
    }
}

// Export singleton instance
export const providerFactory = ProviderFactory.getInstance();
