import { z } from "zod";

// Notification Types - Extensible enum for different notification categories
export enum NotificationType {
    // User Management
    WELCOME = "WELCOME",
    NEW_LOGIN = "NEW_LOGIN",
    PASSWORD_CHANGED = "PASSWORD_CHANGED",
    ACCOUNT_VERIFIED = "ACCOUNT_VERIFIED",

    // Events
    NEW_EVENT_CREATED = "NEW_EVENT_CREATED",
    EVENT_REMINDER = "EVENT_REMINDER",
    EVENT_CANCELLED = "EVENT_CANCELLED",
    EVENT_JOINED = "EVENT_JOINED",
    EVENT_UPDATED = "EVENT_UPDATED",
    EVENT_FULL_WAITLIST = "EVENT_FULL_WAITLIST",
    EVENT_CREATED_BY_USER = "EVENT_CREATED_BY_USER",

    // Payments
    PAYMENT_SUCCESSFUL = "PAYMENT_SUCCESSFUL",
    PAYMENT_RECEIVED = "PAYMENT_RECEIVED",
    PAYMENT_PENDING = "PAYMENT_PENDING",
    PAYMENT_FAILED = "PAYMENT_FAILED",
    PAYMENT_REFUNDED = "PAYMENT_REFUNDED",
    PAYMENT_FOR_OTHERS = "PAYMENT_FOR_OTHERS",
    PAYMENT_CONFIRMATION = "PAYMENT_CONFIRMATION",

    // Waitlist
    WAITLIST_SPOT_AVAILABLE = "WAITLIST_SPOT_AVAILABLE",
    WAITLIST_TIME_EXPIRED = "WAITLIST_TIME_EXPIRED",
    WAITLIST_MOVED_UP = "WAITLIST_MOVED_UP",

    // Reviews & Feedback
    EVENT_REVIEW_REQUEST = "EVENT_REVIEW_REQUEST",
    REVIEW_RECEIVED = "REVIEW_RECEIVED",

    // Social
    PARTICIPANT_CANCELLED = "PARTICIPANT_CANCELLED",
    PRIVATE_EVENT_INVITATION = "PRIVATE_EVENT_INVITATION",
    PRIVATE_EVENT_ACCEPTED = "PRIVATE_EVENT_ACCEPTED",
    PRIVATE_EVENT_DECLINED = "PRIVATE_EVENT_DECLINED",
    FRIEND_REQUEST = "FRIEND_REQUEST",
    FRIEND_REQUEST_ACCEPTED = "FRIEND_REQUEST_ACCEPTED",

    // Communication
    CHAT_NEW_MESSAGE = "CHAT_NEW_MESSAGE",
    CHAT_MENTIONED = "CHAT_MENTIONED",
    CHAT_GROUP_ADDED = "CHAT_GROUP_ADDED",

    // Activity & Updates
    ACTIVITY_UPDATE = "ACTIVITY_UPDATE",

    // Subscriptions
    SUBSCRIPTION_RENEWAL = "SUBSCRIPTION_RENEWAL",

    // System & Reminders
    SYSTEM_MAINTENANCE = "SYSTEM_MAINTENANCE",
    SYSTEM_UPDATE = "SYSTEM_UPDATE",
    SYSTEM_ALERT = "SYSTEM_ALERT",
    INACTIVE_USER_REMINDER = "INACTIVE_USER_REMINDER",
    EVENT_AUTO_CANCEL_WARNING = "EVENT_AUTO_CANCEL_WARNING",

    // Custom - For app-specific notifications
    CUSTOM = "CUSTOM"
}

// Delivery Channels - What methods can be used to send notifications
export interface DeliveryChannels {
    email?: boolean;
    sms?: boolean;
    push?: boolean;
    inApp?: boolean;
    webhook?: boolean;
    slack?: boolean;
    discord?: boolean;
    telegram?: boolean;
}

// Notification Priority Levels
export enum NotificationPriority {
    LOW = "LOW",
    NORMAL = "NORMAL",
    HIGH = "HIGH",
    URGENT = "URGENT"
}

// Notification Status
export enum NotificationStatus {
    PENDING = "PENDING",
    SENT = "SENT",
    DELIVERED = "DELIVERED",
    READ = "READ",
    FAILED = "FAILED",
    CANCELLED = "CANCELLED"
}

// Base Notification Interface
export interface BaseNotification {
    id: string;
    appId: string;           // Multi-tenant app identifier
    userId: string;          // User who should receive the notification
    type: NotificationType;
    title: string;
    content: string;
    metadata?: Record<string, any>;  // Additional data
    priority: NotificationPriority;
    status: NotificationStatus;
    channels: DeliveryChannels;
    scheduledFor?: Date;     // For scheduled notifications
    expiresAt?: Date;        // When notification expires
    isRead: boolean;
    readAt?: Date;
    createdAt: Date;
    updatedAt: Date;
}

export interface NotificationMetadata {
    email?: string;
    phone?: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
    locale?: string;
    timezone?: string;
    preferences?: Record<string, any>;
    [key: string]: any;
}

// Create Notification Request
export interface CreateNotificationRequest {
    id?: string;
    appId: string;
    userId: string;
    type: NotificationType;
    title: string;
    content: string;
    priority?: NotificationPriority;
    channels?: DeliveryChannels;
    metadata?: NotificationMetadata;
    scheduledAt?: Date;
    expiresAt?: Date;
    tags?: string[];
}

// Update Notification Request
export interface UpdateNotificationRequest {
    title?: string;
    content?: string;
    metadata?: Record<string, any>;
    priority?: NotificationPriority;
    channels?: Partial<DeliveryChannels>;
    status?: NotificationStatus;
    scheduledFor?: Date;
    expiresAt?: Date;
}

// Notification Response
export interface NotificationResponse {
    id: string;
    appId: string;
    userId: string;
    type: NotificationType;
    title: string;
    content: string;
    priority: NotificationPriority;
    channels: DeliveryChannels;
    metadata?: NotificationMetadata;
    status: NotificationStatus;
    createdAt: Date;
    updatedAt: Date;
    sentAt?: Date;
    readAt?: Date;
    tags?: string[];
}

// Notifications List Response
export interface NotificationsListResponse {
    success: boolean;
    data?: {
        notifications: BaseNotification[];
        total: number;
        page: number;
        limit: number;
        hasMore: boolean;
    };
    error?: string;
    message?: string;
}

// User Preferences for Notifications
export interface NotificationPreferences {
    userId: string;
    appId: string;
    email: boolean;
    sms: boolean;
    push: boolean;
    inApp: boolean;
    webhook: boolean;
    slack: boolean;
    discord: boolean;
    telegram: boolean;
    quietHours?: {
        enabled: boolean;
        start: string; // HH:MM format
        end: string;   // HH:MM format
        timezone: string;
    };
    frequency?: {
        email: 'immediate' | 'daily' | 'weekly';
        sms: 'immediate' | 'daily' | 'weekly';
        push: 'immediate' | 'daily' | 'weekly';
    };
    updatedAt: Date;
}

// Multi-Tenant Types
export interface AppConfig {
    appId: string;
    name: string;
    description?: string;
    providers: ProviderConfig[];
    fallbackStrategy: FallbackStrategy;
    maxRetries: number;
    retryDelay: number;
    rateLimits: RateLimits;
    createdAt: Date;
    updatedAt: Date;
}

export interface ProviderConfig {
    type: 'novu' | 'resend' | 'twilio' | 'custom';
    name: string;
    isEnabled: boolean;
    priority: number;
    config: Record<string, any>;
    fallbackProvider?: string;
    rateLimits?: RateLimits;
}

export interface RateLimits {
    requestsPerMinute?: number;
    requestsPerHour?: number;
    requestsPerDay?: number;
    burstLimit?: number;
}

export type FallbackStrategy = 'failover' | 'parallel' | 'hybrid';

export interface RoutingResult {
    appId: string;
    notificationId: string;
    channels: Record<keyof DeliveryChannels, ChannelResult>;
    success: boolean;
    errors: RoutingError[];
    timing: {
        startTime: number;
        endTime: number;
        duration: number;
    };
}

export interface ChannelResult {
    success: boolean;
    provider?: string;
    error?: string;
    attempts: number;
    fallbackUsed: boolean;
    responseTime?: number;
}

export interface RoutingError {
    channel: string;
    error: string;
    timestamp: string;
    provider?: string;
    attempt?: number;
}

// Zod Schemas for validation
export const CreateNotificationSchema = z.object({
    appId: z.string().min(1, "App ID is required"),
    userId: z.string().min(1, "User ID is required"),
    type: z.nativeEnum(NotificationType),
    title: z.string().min(1, "Title is required").max(200, "Title too long"),
    content: z.string().min(1, "Content is required").max(1000, "Content too long"),
    metadata: z.record(z.any()).optional(),
    priority: z.nativeEnum(NotificationPriority).default(NotificationPriority.NORMAL),
    channels: z.object({
        inApp: z.boolean().default(true),
        email: z.boolean().default(false),
        sms: z.boolean().default(false),
        push: z.boolean().default(false),
        webhook: z.boolean().default(false),
        slack: z.boolean().default(false),
        discord: z.boolean().default(false),
        telegram: z.boolean().default(false),
    }).optional(),
    scheduledFor: z.date().optional(),
    expiresAt: z.date().optional(),
});

export const UpdateNotificationSchema = z.object({
    title: z.string().min(1, "Title is required").max(200, "Title too long").optional(),
    content: z.string().min(1, "Content is required").max(1000, "Content too long").optional(),
    metadata: z.record(z.any()).optional(),
    priority: z.nativeEnum(NotificationPriority).optional(),
    channels: z.object({
        inApp: z.boolean().optional(),
        email: z.boolean().optional(),
        sms: z.boolean().optional(),
        push: z.boolean().optional(),
        webhook: z.boolean().optional(),
        slack: z.boolean().optional(),
        discord: z.boolean().optional(),
        telegram: z.boolean().optional(),
    }).optional(),
    status: z.nativeEnum(NotificationStatus).optional(),
    scheduledFor: z.date().optional(),
    expiresAt: z.date().optional(),
});

export const NotificationPreferencesSchema = z.object({
    userId: z.string().min(1, "User ID is required"),
    appId: z.string().min(1, "App ID is required"),
    emailNotifications: z.boolean().default(true),
    pushNotifications: z.boolean().default(true),
    inAppNotifications: z.boolean().default(true),
    smsNotifications: z.boolean().default(false),
    webhookNotifications: z.boolean().default(false),
    slackNotifications: z.boolean().default(false),
    discordNotifications: z.boolean().default(false),
    telegramNotifications: z.boolean().default(false),
    eventReminders: z.boolean().default(true),
    paymentNotifications: z.boolean().default(true),
    waitlistNotifications: z.boolean().default(true),
    chatNotifications: z.boolean().default(true),
    systemNotifications: z.boolean().default(true),
    quietHoursEnabled: z.boolean().default(false),
    quietHoursStart: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
    quietHoursEnd: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
    timezone: z.string().default("UTC"),
});

// Type exports for convenience
export type CreateNotificationInput = z.infer<typeof CreateNotificationSchema>;
export type UpdateNotificationInput = z.infer<typeof UpdateNotificationSchema>;
export type NotificationPreferencesInput = z.infer<typeof NotificationPreferencesSchema>;

export interface AppRegistrationRequest {
    appId: string;
    name: string;
    description?: string;
    contactEmail: string;
    contactName: string;
    website?: string;
    businessType?: string;
    expectedVolume?: number;
    providers: {
        novu?: {
            apiKey: string;
            appId: string;
            priority?: number;
            supportedChannels?: string[];
        };
        resend?: {
            apiKey: string;
            fromEmail: string;
            priority?: number;
            supportedChannels?: string[];
        };
        twilio?: {
            accountSid: string;
            authToken: string;
            fromPhoneNumber: string;
            priority?: number;
            supportedChannels?: string[];
        };
    };
    fallbackStrategy?: FallbackStrategy;
    maxRetries?: number;
    retryDelay?: number;
    rateLimits?: RateLimits;
}

export interface AppRegistrationResponse {
    success: boolean;
    appId: string;
    status: 'PENDING_APPROVAL' | 'APPROVED' | 'REJECTED';
    message: string;
    estimatedReviewTime?: string;
}

export interface AppApprovalRequest {
    appId: string;
    action: 'APPROVE' | 'REJECT';
    reason?: string;
    adminNotes?: string;
    apiKey?: string; // Generated if approved
}

export interface AppApprovalResponse {
    success: boolean;
    appId: string;
    status: 'APPROVED' | 'REJECTED';
    apiKey?: string;
    message: string;
}

export interface AppAuthRequest {
    appId: string;
    apiKey: string;
}

export interface AppAuthResponse {
    success: boolean;
    appId: string;
    isAuthenticated: boolean;
    permissions: string[];
    rateLimits: RateLimits;
}

export interface AdminNotification {
    type: 'APP_REGISTRATION_REQUEST' | 'APP_APPROVED' | 'APP_REJECTED' | 'APP_REVOKED' | 'APP_APPROVAL_NEEDED' | 'APP_USAGE_ALERT';
    appId: string;
    appName: string;
    contactEmail: string;
    contactName: string;
    website?: string;
    message: string;
    actionUrl?: string;
    timestamp: Date;
}
