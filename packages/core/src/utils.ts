import { CreateNotificationRequest, DeliveryChannels, NotificationType } from "./types";

/**
 * Utility functions for the notifications system
 */

/**
 * Generate a unique ID for notifications
 */
export function generateNotificationId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Format a date for display
 */
export function formatDate(date: Date, format: "short" | "long" | "relative" = "short"): string {
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (format === "relative") {
        if (diffInMinutes < 1) return "Just now";
        if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes > 1 ? "s" : ""} ago`;
        if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? "s" : ""} ago`;
        if (diffInDays < 7) return `${diffInDays} day${diffInDays > 1 ? "s" : ""} ago`;
        return date.toLocaleDateString();
    }

    if (format === "long") {
        return date.toLocaleString();
    }

    // Short format
    return date.toLocaleDateString();
}

/**
 * Check if a notification is expired
 */
export function isNotificationExpired(notification: { expiresAt?: Date }): boolean {
    if (!notification.expiresAt) return false;
    return new Date() > notification.expiresAt;
}

/**
 * Check if a notification should be sent now (not scheduled for future)
 */
export function shouldSendNotificationNow(notification: { scheduledFor?: Date }): boolean {
    if (!notification.scheduledFor) return true;
    return new Date() >= notification.scheduledFor;
}

/**
 * Get the next scheduled notification time
 */
export function getNextScheduledTime(notifications: Array<{ scheduledFor?: Date }>): Date | null {
    const futureNotifications = notifications
        .filter(n => n.scheduledFor && n.scheduledFor > new Date())
        .sort((a, b) => (a.scheduledFor?.getTime() || 0) - (b.scheduledFor?.getTime() || 0));

    return futureNotifications[0]?.scheduledFor || null;
}

/**
 * Validate notification data
 */
export function validateNotification(notification: CreateNotificationRequest): {
    isValid: boolean;
    errors: string[];
} {
    const errors: string[] = [];

    if (!notification.appId) errors.push("App ID is required");
    if (!notification.userId) errors.push("User ID is required");
    if (!notification.type) errors.push("Notification type is required");
    if (!notification.title) errors.push("Title is required");
    if (!notification.content) errors.push("Content is required");

    if (notification.title && notification.title.length > 200) {
        errors.push("Title must be 200 characters or less");
    }

    if (notification.content && notification.content.length > 1000) {
        errors.push("Content must be 1000 characters or less");
    }

    if (notification.scheduledFor && notification.scheduledFor < new Date()) {
        errors.push("Scheduled time must be in the future");
    }

    if (notification.expiresAt && notification.expiresAt < new Date()) {
        errors.push("Expiration time must be in the future");
    }

    if (notification.scheduledFor && notification.expiresAt &&
        notification.scheduledFor >= notification.expiresAt) {
        errors.push("Expiration time must be after scheduled time");
    }

    return {
        isValid: errors.length === 0,
        errors,
    };
}

/**
 * Get notification type category
 */
export function getNotificationTypeCategory(type: NotificationType): string {
    if (type.includes("EVENT")) return "events";
    if (type.includes("PAYMENT")) return "payments";
    if (type.includes("WAITLIST")) return "waitlist";
    if (type.includes("CHAT")) return "communication";
    if (type.includes("SYSTEM")) return "system";
    if (type.includes("WELCOME") || type.includes("LOGIN") || type.includes("PASSWORD")) return "account";
    return "general";
}

/**
 * Check if notification type is urgent
 */
export function isUrgentNotificationType(type: NotificationType): boolean {
    const urgentTypes = [
        NotificationType.PAYMENT_FAILED,
        NotificationType.EVENT_CANCELLED,
        NotificationType.SYSTEM_MAINTENANCE,
        NotificationType.WAITLIST_SPOT_AVAILABLE,
    ];
    return urgentTypes.includes(type);
}

/**
 * Get default channels for notification type
 */
export function getDefaultChannelsForType(type: NotificationType): Partial<DeliveryChannels> {
    const category = getNotificationTypeCategory(type);
    const isUrgent = isUrgentNotificationType(type);

    const baseChannels: Partial<DeliveryChannels> = {
        inApp: true,
        email: false,
        push: false,
    };

    switch (category) {
        case "payments":
            return {
                ...baseChannels,
                email: true,
                push: true,
            };
        case "events":
            return {
                ...baseChannels,
                email: true,
                push: type === NotificationType.EVENT_REMINDER,
            };
        case "system":
            return {
                ...baseChannels,
                email: true,
                push: true,
            };
        case "account":
            return {
                ...baseChannels,
                email: true,
            };
        default:
            return baseChannels;
    }
}

/**
 * Merge delivery channels with smart defaults
 */
export function mergeDeliveryChannels(
    userChannels?: Partial<DeliveryChannels>,
    type?: NotificationType
): DeliveryChannels {
    const defaultChannels = type ? getDefaultChannelsForType(type) : {
        inApp: true,
        email: false,
        sms: false,
        push: false,
        webhook: false,
        slack: false,
        discord: false,
        telegram: false,
    };

    return {
        inApp: false,
        email: false,
        sms: false,
        push: false,
        webhook: false,
        slack: false,
        discord: false,
        telegram: false,
        ...defaultChannels,
        ...userChannels,
    };
}

/**
 * Check if quiet hours are active
 */
export function isQuietHoursActive(preferences: {
    quietHoursEnabled: boolean;
    quietHoursStart?: string;
    quietHoursEnd?: string;
    timezone?: string;
}): boolean {
    if (!preferences.quietHoursEnabled || !preferences.quietHoursStart || !preferences.quietHoursEnd) {
        return false;
    }

    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTime = currentHour * 60 + currentMinute;

    const startTime = parseTimeString(preferences.quietHoursStart);
    const endTime = parseTimeString(preferences.quietHoursEnd);

    if (startTime <= endTime) {
        // Same day (e.g., 22:00 to 08:00)
        return currentTime >= startTime && currentTime <= endTime;
    } else {
        // Overnight (e.g., 22:00 to 08:00)
        return currentTime >= startTime || currentTime <= endTime;
    }
}

/**
 * Parse time string (HH:MM) to minutes since midnight
 */
function parseTimeString(timeString: string): number {
    const [hours, minutes] = timeString.split(":").map(Number);
    return hours * 60 + minutes;
}

/**
 * Debounce function for API calls
 */
export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
    };
}

/**
 * Throttle function for API calls
 */
export function throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(() => (inThrottle = false), limit);
        }
    };
}

/**
 * Retry function with exponential backoff
 */
export async function retryWithBackoff<T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
): Promise<T> {
    let lastError: Error;

    for (let i = 0; i < maxRetries; i++) {
        try {
            return await fn();
        } catch (error) {
            lastError = error as Error;

            if (i === maxRetries - 1) {
                throw lastError;
            }

            const delay = baseDelay * Math.pow(2, i);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    throw lastError!;
}

/**
 * Generate a human-readable notification summary
 */
export function generateNotificationSummary(
    notifications: Array<{ type: NotificationType; title: string }>,
    maxItems: number = 3
): string {
    if (notifications.length === 0) return "No notifications";

    const items = notifications.slice(0, maxItems);
    const summary = items.map(n => n.title).join(", ");

    if (notifications.length > maxItems) {
        return `${summary} and ${notifications.length - maxItems} more`;
    }

    return summary;
}

/**
 * Check if a notification should be grouped with others
 */
export function shouldGroupNotifications(
    notification1: { type: NotificationType; userId: string; appId: string },
    notification2: { type: NotificationType; userId: string; appId: string }
): boolean {
    return (
        notification1.type === notification2.type &&
        notification1.userId === notification2.userId &&
        notification1.appId === notification2.appId
    );
}
