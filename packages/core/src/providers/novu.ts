import { BaseNotificationProvider } from './base';
import { CreateNotificationRequest, DeliveryChannels } from '../types';
import { ProviderResult } from './base';

export interface NovuProviderConfig {
    apiKey: string;
    appId: string;
    isEnabled?: boolean;
    priority?: number;
    supportedChannels?: (keyof DeliveryChannels)[];
}

export class NovuProvider extends BaseNotificationProvider {
    private apiKey: string;
    private appId: string;

    constructor(config: NovuProviderConfig) {
        super('novu', {
            isEnabled: config.isEnabled ?? true,
            priority: config.priority ?? 1,
            supportedChannels: config.supportedChannels ?? ['email', 'push', 'inApp', 'sms']
        });

        this.apiKey = config.apiKey;
        this.appId = config.appId;
    }

    async send(notification: CreateNotificationRequest): Promise<ProviderResult> {
        const startTime = Date.now();

        try {
            // Validate notification
            const validation = await this.validate(notification);
            if (!validation.isValid) {
                return {
                    success: false,
                    error: `Validation failed: ${validation.errors.join(', ')}`,
                    responseTime: Date.now() - startTime
                };
            }

            // Simulate Novu API call
            // In a real implementation, you would make an actual API call to Novu
            const result = await this.sendToNovu(notification);

            return {
                success: true,
                messageId: result.messageId,
                responseTime: Date.now() - startTime,
                metadata: result.metadata
            };

        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                responseTime: Date.now() - startTime
            };
        }
    }

    private async sendToNovu(notification: CreateNotificationRequest): Promise<{
        messageId: string;
        metadata: Record<string, any>;
    }> {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 100));

        // Generate mock message ID
        const messageId = `novu_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        return {
            messageId,
            metadata: {
                provider: 'novu',
                appId: this.appId,
                channel: 'novu',
                timestamp: new Date().toISOString()
            }
        };
    }

    protected async performHealthCheck(): Promise<boolean> {
        try {
            // Simulate health check
            // In a real implementation, you would ping the Novu API
            await new Promise(resolve => setTimeout(resolve, 50));
            return true;
        } catch {
            return false;
        }
    }
} 