import { BaseNotificationProvider } from './base';
import { CreateNotificationRequest, DeliveryChannels } from '../types';
import { ProviderResult } from './base';

export interface ResendProviderConfig {
    apiKey: string;
    fromEmail: string;
    isEnabled?: boolean;
    priority?: number;
    supportedChannels?: (keyof DeliveryChannels)[];
}

export class ResendProvider extends BaseNotificationProvider {
    private apiKey: string;
    private fromEmail: string;

    constructor(config: ResendProviderConfig) {
        super('resend', {
            isEnabled: config.isEnabled ?? true,
            priority: config.priority ?? 2,
            supportedChannels: config.supportedChannels ?? ['email']
        });

        this.apiKey = config.apiKey;
        this.fromEmail = config.fromEmail;
    }

    async send(notification: CreateNotificationRequest): Promise<ProviderResult> {
        const startTime = Date.now();

        try {
            // Validate notification
            const validation = await this.validate(notification);
            if (!validation.isValid) {
                return {
                    success: false,
                    error: `Validation failed: ${validation.errors.join(', ')}`,
                    responseTime: Date.now() - startTime
                };
            }

            // Check if email is supported and requested
            if (!this.supportedChannels.includes('email')) {
                return {
                    success: false,
                    error: 'Email channel not supported by Resend provider',
                    responseTime: Date.now() - startTime
                };
            }

            // Simulate Resend API call
            const result = await this.sendViaResend(notification);

            return {
                success: true,
                messageId: result.messageId,
                responseTime: Date.now() - startTime,
                metadata: result.metadata
            };

        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                responseTime: Date.now() - startTime
            };
        }
    }

    private async sendViaResend(notification: CreateNotificationRequest): Promise<{
        messageId: string;
        metadata: Record<string, any>;
    }> {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 150));

        // Generate mock message ID
        const messageId = `resend_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        return {
            messageId,
            metadata: {
                provider: 'resend',
                fromEmail: this.fromEmail,
                channel: 'email',
                timestamp: new Date().toISOString()
            }
        };
    }

    protected async performHealthCheck(): Promise<boolean> {
        try {
            // Simulate health check
            await new Promise(resolve => setTimeout(resolve, 50));
            return true;
        } catch {
            return false;
        }
    }
} 