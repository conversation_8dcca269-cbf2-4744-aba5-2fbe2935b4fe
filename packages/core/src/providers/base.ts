import { CreateNotificationRequest, DeliveryChannels, NotificationPriority } from '../types';

export interface NotificationProvider {
    name: string;
    isEnabled: boolean;
    priority: number;
    supportedChannels: (keyof DeliveryChannels)[];

    send(notification: CreateNotificationRequest): Promise<ProviderResult>;
    validate(notification: CreateNotificationRequest): Promise<ValidationResult>;
    getHealth(): Promise<HealthStatus>;
}

export interface ProviderResult {
    success: boolean;
    messageId?: string;
    error?: string;
    responseTime: number;
    metadata?: Record<string, any>;
}

export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}

export interface HealthStatus {
    status: 'healthy' | 'degraded' | 'unhealthy';
    message: string;
    lastCheck: Date;
    responseTime?: number;
    error?: string;
}

export abstract class BaseNotificationProvider implements NotificationProvider {
    public name: string;
    public isEnabled: boolean;
    public priority: number;
    public supportedChannels: (keyof DeliveryChannels)[];

    constructor(name: string, config: {
        isEnabled?: boolean;
        priority?: number;
        supportedChannels?: (keyof DeliveryChannels)[];
    } = {}) {
        this.name = name;
        this.isEnabled = config.isEnabled ?? true;
        this.priority = config.priority ?? 1;
        this.supportedChannels = config.supportedChannels ?? ['inApp'];
    }

    abstract send(notification: CreateNotificationRequest): Promise<ProviderResult>;

    async validate(notification: CreateNotificationRequest): Promise<ValidationResult> {
        const errors: string[] = [];
        const warnings: string[] = [];

        // Basic validation
        if (!notification.title || notification.title.trim().length === 0) {
            errors.push('Notification title is required');
        }

        if (!notification.content || notification.content.trim().length === 0) {
            errors.push('Notification content is required');
        }

        if (!notification.userId) {
            errors.push('User ID is required');
        }

        if (!notification.appId) {
            errors.push('App ID is required');
        }

        // Check if provider supports requested channels
        const requestedChannels = Object.entries(notification.channels || {})
            .filter(([_, enabled]) => enabled)
            .map(([channel, _]) => channel as keyof DeliveryChannels);

        const unsupportedChannels = requestedChannels.filter(
            channel => !this.supportedChannels.includes(channel)
        );

        if (unsupportedChannels.length > 0) {
            warnings.push(`Provider ${this.name} does not support channels: ${unsupportedChannels.join(', ')}`);
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    async getHealth(): Promise<HealthStatus> {
        try {
            const startTime = Date.now();

            // Basic health check - can be overridden by subclasses
            const healthCheck = await this.performHealthCheck();
            const responseTime = Date.now() - startTime;

            return {
                status: healthCheck ? 'healthy' : 'degraded',
                message: healthCheck ? 'Provider is responding' : 'Provider health check failed',
                lastCheck: new Date(),
                responseTime
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                message: 'Provider health check failed',
                lastCheck: new Date(),
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    protected async performHealthCheck(): Promise<boolean> {
        // Default implementation - always return true
        // Subclasses should override this with actual health checks
        return true;
    }
} 