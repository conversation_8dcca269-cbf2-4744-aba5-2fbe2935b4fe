import {
    NotificationProvider,
    CreateNotificationRequest,
    DeliveryChannels,
    NotificationPriority,
    ProviderConfig,
    AppConfig,
    RoutingR<PERSON>ult,
    FallbackStrategy
} from '../types';
import { NovuProvider, NovuProviderConfig } from './novu';
import { ResendProvider, ResendProviderConfig } from './resend';
import { TwilioProvider, TwilioProviderConfig } from './twilio';

export interface ProviderRegistry {
    [providerName: string]: NotificationProvider;
}

export interface AppProviderConfig {
    novu?: NovuProviderConfig;
    resend?: ResendProviderConfig;
    twilio?: TwilioProviderConfig;
    fallbackStrategy?: FallbackStrategy;
    maxRetries?: number;
    retryDelay?: number;
}

export interface RoutingConfig {
    defaultFallbackStrategy: FallbackStrategy;
    globalMaxRetries: number;
    globalRetryDelay: number;
    enableMetrics: boolean;
    enableLogging: boolean;
}

export class ProviderFactory {
    private providers: ProviderRegistry = {};
    private appConfigs: Map<string, AppProviderConfig> = new Map();
    private routingConfig: RoutingConfig;
    private metrics: Map<string, any> = new Map();

    constructor(config: Partial<RoutingConfig> = {}) {
        this.routingConfig = {
            defaultFallbackStrategy: 'failover',
            globalMaxRetries: 3,
            globalRetryDelay: 1000,
            enableMetrics: true,
            enableLogging: true,
            ...config
        };
    }

    /**
     * Register a notification provider
     */
    register(name: string, provider: NotificationProvider): void {
        if (!provider || typeof provider.send !== 'function') {
            throw new Error(`Invalid provider: ${name}`);
        }

        this.providers[name] = provider;

        if (this.routingConfig.enableLogging) {
            console.log(`Provider registered: ${name} (${provider.constructor.name})`);
        }
    }

    /**
     * Register an application with its provider configuration
     */
    registerApp(appId: string, config: AppProviderConfig): void {
        // Validate configuration
        if (!config || Object.keys(config).length === 0) {
            throw new Error(`Invalid app configuration for ${appId}`);
        }

        // Create and register providers for this app
        const appProviders: ProviderRegistry = {};

        if (config.novu) {
            const novuProvider = new NovuProvider(config.novu);
            appProviders.novu = novuProvider;
            this.register(`${appId}-novu`, novuProvider);
        }

        if (config.resend) {
            const resendProvider = new ResendProvider(config.resend);
            appProviders.resend = resendProvider;
            this.register(`${appId}-resend`, resendProvider);
        }

        if (config.twilio) {
            const twilioProvider = new TwilioProvider(config.twilio);
            appProviders.twilio = twilioProvider;
            this.register(`${appId}-twilio`, twilioProvider);
        }

        // Store app configuration
        this.appConfigs.set(appId, {
            ...config,
            maxRetries: config.maxRetries ?? this.routingConfig.globalMaxRetries,
            retryDelay: config.retryDelay ?? this.routingConfig.globalRetryDelay
        });

        if (this.routingConfig.enableLogging) {
            console.log(`App registered: ${appId} with ${Object.keys(appProviders).length} providers`);
        }
    }

    /**
     * Get the best provider for a specific channel and app
     */
    getProviderForChannel(appId: string, channel: keyof DeliveryChannels): NotificationProvider | null {
        const appConfig = this.appConfigs.get(appId);
        if (!appConfig) {
            return null;
        }

        const availableProviders: Array<{ name: string; provider: NotificationProvider; priority: number }> = [];

        // Check each provider type for the app
        if (appConfig.novu && this.providers[`${appId}-novu`]) {
            const provider = this.providers[`${appId}-novu`];
            if (provider.getSupportedChannels().includes(channel)) {
                availableProviders.push({
                    name: 'novu',
                    provider,
                    priority: appConfig.novu.priority
                });
            }
        }

        if (appConfig.resend && this.providers[`${appId}-resend`]) {
            const provider = this.providers[`${appId}-resend`];
            if (provider.getSupportedChannels().includes(channel)) {
                availableProviders.push({
                    name: 'resend',
                    provider,
                    priority: appConfig.resend.priority
                });
            }
        }

        if (appConfig.twilio && this.providers[`${appId}-twilio`]) {
            const provider = this.providers[`${appId}-twilio`];
            if (provider.getSupportedChannels().includes(channel)) {
                availableProviders.push({
                    name: 'twilio',
                    provider,
                    priority: appConfig.twilio.priority
                });
            }
        }

        // Sort by priority (lower number = higher priority)
        availableProviders.sort((a, b) => a.priority - b.priority);

        return availableProviders.length > 0 ? availableProviders[0].provider : null;
    }

    /**
     * Get all providers for a specific channel and app
     */
    getProvidersForChannel(appId: string, channel: keyof DeliveryChannels): NotificationProvider[] {
        const appConfig = this.appConfigs.get(appId);
        if (!appConfig) {
            return [];
        }

        const providers: NotificationProvider[] = [];

        if (appConfig.novu && this.providers[`${appId}-novu`]) {
            const provider = this.providers[`${appId}-novu`];
            if (provider.getSupportedChannels().includes(channel)) {
                providers.push(provider);
            }
        }

        if (appConfig.resend && this.providers[`${appId}-resend`]) {
            const provider = this.providers[`${appId}-resend`];
            if (provider.getSupportedChannels().includes(channel)) {
                providers.push(provider);
            }
        }

        if (appConfig.twilio && this.providers[`${appId}-twilio`]) {
            const provider = this.providers[`${appId}-twilio`];
            if (provider.getSupportedChannels().includes(channel)) {
                providers.push(provider);
            }
        }

        // Sort by priority
        return providers.sort((a, b) => a.getPriority() - b.getPriority());
    }

    /**
     * Send a notification using multi-tenant routing
     */
    async sendNotification(notification: CreateNotificationRequest): Promise<RoutingResult> {
        const startTime = Date.now();
        const appId = notification.appId;
        const appConfig = this.appConfigs.get(appId);

        if (!appConfig) {
            throw new Error(`App not registered: ${appId}`);
        }

        const results: RoutingResult = {
            appId,
            notificationId: notification.id || `notification-${Date.now()}`,
            channels: {},
            success: false,
            errors: [],
            timing: { startTime, endTime: 0, duration: 0 }
        };

        try {
            // Process each requested channel
            for (const [channel, enabled] of Object.entries(notification.channels || {})) {
                if (enabled) {
                    const channelKey = channel as keyof DeliveryChannels;
                    const channelResult = await this.sendToChannel(
                        appId,
                        channelKey,
                        notification,
                        appConfig
                    );
                    results.channels[channelKey] = channelResult;
                }
            }

            // Determine overall success
            const channelResults = Object.values(results.channels);
            results.success = channelResults.length > 0 && channelResults.every(r => r.success);
            results.timing.endTime = Date.now();
            results.timing.duration = results.timing.endTime - results.timing.startTime;

            // Update metrics
            if (this.routingConfig.enableMetrics) {
                this.updateMetrics(appId, results);
            }

            return results;

        } catch (error) {
            results.timing.endTime = Date.now();
            results.timing.duration = results.timing.endTime - results.timing.startTime;
            results.errors.push({
                channel: 'system',
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString()
            });
            return results;
        }
    }

    /**
     * Send notification to a specific channel with fallback strategy
     */
    private async sendToChannel(
        appId: string,
        channel: keyof DeliveryChannels,
        notification: CreateNotificationRequest,
        appConfig: AppProviderConfig
    ): Promise<any> {
        const providers = this.getProvidersForChannel(appId, channel);

        if (providers.length === 0) {
            return {
                success: false,
                error: `No providers available for channel: ${channel}`,
                provider: null,
                attempts: 0
            };
        }

        const fallbackStrategy = appConfig.fallbackStrategy || this.routingConfig.defaultFallbackStrategy;
        const maxRetries = appConfig.maxRetries || this.routingConfig.globalMaxRetries;
        const retryDelay = appConfig.retryDelay || this.routingConfig.globalRetryDelay;

        switch (fallbackStrategy) {
            case 'failover':
                return this.executeFailoverStrategy(providers, notification, maxRetries, retryDelay);
            case 'parallel':
                return this.executeParallelStrategy(providers, notification, maxRetries);
            case 'hybrid':
                return this.executeHybridStrategy(providers, notification, maxRetries, retryDelay);
            default:
                return this.executeFailoverStrategy(providers, notification, maxRetries, retryDelay);
        }
    }

    /**
     * Failover strategy: try providers in sequence until one succeeds
     */
    private async executeFailoverStrategy(
        providers: NotificationProvider[],
        notification: CreateNotificationRequest,
        maxRetries: number,
        retryDelay: number
    ): Promise<any> {
        let lastError: Error | null = null;
        let attempts = 0;

        for (const provider of providers) {
            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                attempts++;
                try {
                    const success = await provider.send(notification);
                    if (success) {
                        return {
                            success: true,
                            provider: provider.name,
                            attempts,
                            fallbackUsed: providers.indexOf(provider) > 0
                        };
                    }
                } catch (error) {
                    lastError = error instanceof Error ? error : new Error(String(error));

                    if (this.routingConfig.enableLogging) {
                        console.warn(`Provider ${provider.name} attempt ${attempt} failed:`, lastError.message);
                    }

                    if (attempt < maxRetries) {
                        await this.delay(retryDelay);
                    }
                }
            }
        }

        return {
            success: false,
            error: lastError?.message || 'All providers failed',
            provider: null,
            attempts,
            fallbackUsed: false
        };
    }

    /**
     * Parallel strategy: try all providers simultaneously, return first success
     */
    private async executeParallelStrategy(
        providers: NotificationProvider[],
        notification: CreateNotificationRequest,
        maxRetries: number
    ): Promise<any> {
        const promises = providers.map(async (provider) => {
            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    const success = await provider.send(notification);
                    if (success) {
                        return { success: true, provider: provider.name, attempts: attempt };
                    }
                } catch (error) {
                    if (attempt < maxRetries) {
                        await this.delay(100); // Short delay between retries
                    }
                }
            }
            return { success: false, provider: provider.name, attempts: maxRetries };
        });

        try {
            const results = await Promise.race(promises);
            if (results.success) {
                return results;
            }
        } catch (error) {
            // Continue to wait for other results
        }

        // Wait for all to complete to get final status
        const allResults = await Promise.allSettled(promises);
        const successfulResult = allResults.find(result =>
            result.status === 'fulfilled' && result.value.success
        );

        if (successfulResult && successfulResult.status === 'fulfilled') {
            return successfulResult.value;
        }

        return {
            success: false,
            error: 'All providers failed in parallel execution',
            provider: null,
            attempts: maxRetries
        };
    }

    /**
     * Hybrid strategy: try primary provider first, then parallel fallbacks
     */
    private async executeHybridStrategy(
        providers: NotificationProvider[],
        notification: CreateNotificationRequest,
        maxRetries: number,
        retryDelay: number
    ): Promise<any> {
        if (providers.length === 1) {
            return this.executeFailoverStrategy(providers, notification, maxRetries, retryDelay);
        }

        // Try primary provider first
        const primaryProvider = providers[0];
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const success = await primaryProvider.send(notification);
                if (success) {
                    return {
                        success: true,
                        provider: primaryProvider.name,
                        attempts: attempt,
                        fallbackUsed: false
                    };
                }
            } catch (error) {
                if (attempt < maxRetries) {
                    await this.delay(retryDelay);
                }
            }
        }

        // If primary fails, try fallbacks in parallel
        const fallbackProviders = providers.slice(1);
        return this.executeParallelStrategy(fallbackProviders, notification, maxRetries);
    }

    /**
     * Get provider status for monitoring
     */
    getProviderStatus(): Record<string, any> {
        const status: Record<string, any> = {};

        for (const [name, provider] of Object.entries(this.providers)) {
            status[name] = {
                name: provider.name,
                isConfigured: provider.isConfigured(),
                isEnabled: provider.isEnabled(),
                priority: provider.getPriority(),
                supportedChannels: provider.getSupportedChannels(),
                type: provider.constructor.name
            };
        }

        return status;
    }

    /**
     * Get app configuration status
     */
    getAppStatus(): Record<string, any> {
        const status: Record<string, any> = {};

        for (const [appId, config] of this.appConfigs.entries()) {
            status[appId] = {
                appId,
                providers: {
                    novu: !!config.novu,
                    resend: !!config.resend,
                    twilio: !!config.twilio
                },
                fallbackStrategy: config.fallbackStrategy || this.routingConfig.defaultFallbackStrategy,
                maxRetries: config.maxRetries || this.routingConfig.globalMaxRetries,
                retryDelay: config.retryDelay || this.routingConfig.globalRetryDelay
            };
        }

        return status;
    }

    /**
     * Get routing metrics
     */
    getMetrics(): Record<string, any> {
        if (!this.routingConfig.enableMetrics) {
            return { metricsDisabled: true };
        }

        const metrics: Record<string, any> = {};

        for (const [key, value] of this.metrics.entries()) {
            metrics[key] = value;
        }

        return metrics;
    }

    /**
     * Update metrics for an app
     */
    private updateMetrics(appId: string, result: RoutingResult): void {
        const appMetrics = this.metrics.get(appId) || {
            totalNotifications: 0,
            successfulNotifications: 0,
            failedNotifications: 0,
            totalChannels: 0,
            successfulChannels: 0,
            failedChannels: 0,
            averageResponseTime: 0,
            lastNotification: null
        };

        appMetrics.totalNotifications++;
        appMetrics.totalChannels += Object.keys(result.channels).length;
        appMetrics.lastNotification = new Date().toISOString();

        if (result.success) {
            appMetrics.successfulNotifications++;
        } else {
            appMetrics.failedNotifications++;
        }

        for (const channelResult of Object.values(result.channels)) {
            if (channelResult.success) {
                appMetrics.successfulChannels++;
            } else {
                appMetrics.failedChannels++;
            }
        }

        // Update average response time
        const currentAvg = appMetrics.averageResponseTime;
        const currentCount = appMetrics.totalNotifications;
        appMetrics.averageResponseTime = (currentAvg * (currentCount - 1) + result.timing.duration) / currentCount;

        this.metrics.set(appId, appMetrics);
    }

    /**
     * Clear metrics for an app or all apps
     */
    clearMetrics(appId?: string): void {
        if (appId) {
            this.metrics.delete(appId);
        } else {
            this.metrics.clear();
        }
    }

    /**
     * Utility method for delays
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Get all registered apps
     */
    getRegisteredApps(): string[] {
        return Array.from(this.appConfigs.keys());
    }

    /**
     * Check if an app is registered
     */
    isAppRegistered(appId: string): boolean {
        return this.appConfigs.has(appId);
    }

    /**
     * Remove an app and its providers
     */
    unregisterApp(appId: string): boolean {
        const appConfig = this.appConfigs.get(appId);
        if (!appConfig) {
            return false;
        }

        // Remove app-specific providers
        if (appConfig.novu) {
            delete this.providers[`${appId}-novu`];
        }
        if (appConfig.resend) {
            delete this.providers[`${appId}-resend`];
        }
        if (appConfig.twilio) {
            delete this.providers[`${appId}-twilio`];
        }

        // Remove app configuration
        this.appConfigs.delete(appId);
        this.metrics.delete(appId);

        if (this.routingConfig.enableLogging) {
            console.log(`App unregistered: ${appId}`);
        }

        return true;
    }
}

// Export a default instance
export const providerFactory = new ProviderFactory();