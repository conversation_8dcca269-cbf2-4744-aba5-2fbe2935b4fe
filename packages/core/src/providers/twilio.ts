import { BaseNotificationProvider } from './base';
import { CreateNotificationRequest, DeliveryChannels } from '../types';
import { ProviderResult } from './base';

export interface TwilioProviderConfig {
    accountSid: string;
    authToken: string;
    fromPhoneNumber: string;
    isEnabled?: boolean;
    priority?: number;
    supportedChannels?: (keyof DeliveryChannels)[];
}

export class TwilioProvider extends BaseNotificationProvider {
    private accountSid: string;
    private authToken: string;
    private fromPhoneNumber: string;

    constructor(config: TwilioProviderConfig) {
        super('twilio', {
            isEnabled: config.isEnabled ?? true,
            priority: config.priority ?? 3,
            supportedChannels: config.supportedChannels ?? ['sms']
        });

        this.accountSid = config.accountSid;
        this.authToken = config.authToken;
        this.fromPhoneNumber = config.fromPhoneNumber;
    }

    async send(notification: CreateNotificationRequest): Promise<ProviderResult> {
        const startTime = Date.now();

        try {
            // Validate notification
            const validation = await this.validate(notification);
            if (!validation.isValid) {
                return {
                    success: false,
                    error: `Validation failed: ${validation.errors.join(', ')}`,
                    responseTime: Date.now() - startTime
                };
            }

            // Check if SMS is supported and requested
            if (!this.supportedChannels.includes('sms')) {
                return {
                    success: false,
                    error: 'SMS channel not supported by Twilio provider',
                    responseTime: Date.now() - startTime
                };
            }

            // Check if phone number is provided
            if (!notification.metadata?.phone) {
                return {
                    success: false,
                    error: 'Phone number is required for SMS notifications',
                    responseTime: Date.now() - startTime
                };
            }

            // Simulate Twilio API call
            const result = await this.sendViaTwilio(notification);

            return {
                success: true,
                messageId: result.messageId,
                responseTime: Date.now() - startTime,
                metadata: result.metadata
            };

        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                responseTime: Date.now() - startTime
            };
        }
    }

    private async sendViaTwilio(notification: CreateNotificationRequest): Promise<{
        messageId: string;
        metadata: Record<string, any>;
    }> {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 200));

        // Generate mock message ID (Twilio SID format)
        const messageId = `SM${Date.now()}${Math.random().toString(36).substr(2, 9).toUpperCase()}`;

        return {
            messageId,
            metadata: {
                provider: 'twilio',
                fromPhone: this.fromPhoneNumber,
                toPhone: notification.metadata?.phone || 'unknown',
                channel: 'sms',
                timestamp: new Date().toISOString()
            }
        };
    }

    protected async performHealthCheck(): Promise<boolean> {
        try {
            // Simulate health check
            await new Promise(resolve => setTimeout(resolve, 50));
            return true;
        } catch {
            return false;
        }
    }
} 