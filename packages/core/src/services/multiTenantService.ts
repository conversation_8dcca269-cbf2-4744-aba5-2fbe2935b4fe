import {
    CreateNotificationRequest,
    NotificationResponse,
    NotificationPreferences,
    AppConfig,
    RoutingResult,
    FallbackStrategy,
    RateLimits
} from '../types';
import { ProviderFactory, AppProviderConfig } from '../providers/factory';

export interface AppRegistrationData {
    appId: string;
    name: string;
    description?: string;
    providers: {
        novu?: {
            apiKey: string;
            appId: string;
            priority?: number;
            supportedChannels?: string[];
        };
        resend?: {
            apiKey: string;
            fromEmail: string;
            priority?: number;
            supportedChannels?: string[];
        };
        twilio?: {
            accountSid: string;
            authToken: string;
            fromPhoneNumber: string;
            priority?: number;
            supportedChannels?: string[];
        };
    };
    fallbackStrategy?: FallbackStrategy;
    maxRetries?: number;
    retryDelay?: number;
    rateLimits?: RateLimits;
}

export interface NotificationStats {
    appId: string;
    totalSent: number;
    totalDelivered: number;
    totalFailed: number;
    successRate: number;
    averageResponseTime: number;
    lastNotification: Date | null;
    channelStats: Record<string, {
        sent: number;
        delivered: number;
        failed: number;
        successRate: number;
    }>;
}

export class MultiTenantNotificationService {
    private providerFactory: ProviderFactory;
    private appConfigs: Map<string, AppConfig> = new Map();
    private notificationHistory: Map<string, NotificationResponse[]> = new Map();
    private userPreferences: Map<string, NotificationPreferences> = new Map();
    private rateLimitCounters: Map<string, Map<string, number[]>> = new Map();

    constructor(providerFactory?: ProviderFactory) {
        this.providerFactory = providerFactory || new ProviderFactory();
    }

    /**
     * Register a new application with the notification system
     */
    async registerApp(data: AppRegistrationData): Promise<AppConfig> {
        // Validate app data
        if (!data.appId || !data.name) {
            throw new Error('App ID and name are required');
        }

        if (Object.keys(data.providers).length === 0) {
            throw new Error('At least one provider must be configured');
        }

        // Check if app is already registered
        if (this.appConfigs.has(data.appId)) {
            throw new Error(`App ${data.appId} is already registered`);
        }

        // Create app configuration
        const appConfig: AppConfig = {
            appId: data.appId,
            name: data.name,
            description: data.description,
            providers: [],
            fallbackStrategy: data.fallbackStrategy || 'failover',
            maxRetries: data.maxRetries || 3,
            retryDelay: data.retryDelay || 1000,
            rateLimits: data.rateLimits || {},
            createdAt: new Date(),
            updatedAt: new Date()
        };

        // Convert to provider factory format
        const providerConfig: AppProviderConfig = {
            fallbackStrategy: appConfig.fallbackStrategy,
            maxRetries: appConfig.maxRetries,
            retryDelay: appConfig.retryDelay
        };

        // Configure Novu provider
        if (data.providers.novu) {
            providerConfig.novu = {
                apiKey: data.providers.novu.apiKey,
                appId: data.providers.novu.appId,
                isEnabled: true,
                priority: data.providers.novu.priority || 1,
                supportedChannels: (data.providers.novu.supportedChannels as any[]) || ['email', 'sms', 'push', 'inApp'],
                fallbackProvider: undefined
            };

            appConfig.providers.push({
                type: 'novu',
                name: 'Novu',
                isEnabled: true,
                priority: providerConfig.novu.priority,
                config: { apiKey: data.providers.novu.apiKey, appId: data.providers.novu.appId },
                fallbackProvider: undefined,
                rateLimits: appConfig.rateLimits
            });
        }

        // Configure Resend provider
        if (data.providers.resend) {
            providerConfig.resend = {
                apiKey: data.providers.resend.apiKey,
                fromEmail: data.providers.resend.fromEmail,
                isEnabled: true,
                priority: data.providers.resend.priority || 2,
                supportedChannels: (data.providers.resend.supportedChannels as any[]) || ['email'],
                fallbackProvider: undefined
            };

            appConfig.providers.push({
                type: 'resend',
                name: 'Resend',
                isEnabled: true,
                priority: providerConfig.resend.priority,
                config: { apiKey: data.providers.resend.apiKey, fromEmail: data.providers.resend.fromEmail },
                fallbackProvider: undefined,
                rateLimits: appConfig.rateLimits
            });
        }

        // Configure Twilio provider
        if (data.providers.twilio) {
            providerConfig.twilio = {
                accountSid: data.providers.twilio.accountSid,
                authToken: data.providers.twilio.authToken,
                fromPhoneNumber: data.providers.twilio.fromPhoneNumber,
                isEnabled: true,
                priority: data.providers.twilio.priority || 2,
                supportedChannels: (data.providers.twilio.supportedChannels as any[]) || ['sms'],
                fallbackProvider: undefined
            };

            appConfig.providers.push({
                type: 'twilio',
                name: 'Twilio',
                isEnabled: true,
                priority: providerConfig.twilio.priority,
                config: {
                    accountSid: data.providers.twilio.accountSid,
                    authToken: data.providers.twilio.authToken,
                    fromPhoneNumber: data.providers.twilio.fromPhoneNumber
                },
                fallbackProvider: undefined,
                rateLimits: appConfig.rateLimits
            });
        }

        // Register with provider factory
        this.providerFactory.registerApp(data.appId, providerConfig);

        // Store app configuration
        this.appConfigs.set(data.appId, appConfig);

        // Initialize rate limit counters
        this.rateLimitCounters.set(data.appId, new Map());

        // Initialize notification history
        this.notificationHistory.set(data.appId, []);

        console.log(`App registered successfully: ${data.appId} with ${appConfig.providers.length} providers`);

        return appConfig;
    }

    /**
     * Send a notification using multi-tenant routing
     */
    async sendNotification(notification: CreateNotificationRequest): Promise<RoutingResult> {
        const appId = notification.appId;

        // Check if app is registered
        if (!this.appConfigs.has(appId)) {
            throw new Error(`App ${appId} is not registered`);
        }

        // Check rate limits
        if (!this.checkRateLimits(appId, notification)) {
            throw new Error(`Rate limit exceeded for app ${appId}`);
        }

        // Validate notification
        if (!this.validateNotification(notification)) {
            throw new Error('Invalid notification data');
        }

        // Check user preferences
        const userPrefs = this.getUserPreferences(notification.userId, appId);
        if (userPrefs) {
            notification.channels = this.filterChannelsByPreferences(notification.channels || {}, userPrefs);
        }

        // Send notification through provider factory
        const result = await this.providerFactory.sendNotification(notification);

        // Store in history
        this.storeNotificationHistory(appId, notification, result);

        // Update rate limit counters
        this.updateRateLimitCounters(appId, notification);

        return result;
    }

    /**
     * Get notification history for an app
     */
    getNotificationHistory(appId: string, limit: number = 100): NotificationResponse[] {
        const history = this.notificationHistory.get(appId) || [];
        return history.slice(-limit);
    }

    /**
     * Get notification history for a specific user
     */
    getUserNotificationHistory(appId: string, userId: string, limit: number = 100): NotificationResponse[] {
        const history = this.notificationHistory.get(appId) || [];
        return history
            .filter(n => n.userId === userId)
            .slice(-limit);
    }

    /**
     * Update user preferences
     */
    async updateUserPreferences(
        userId: string,
        appId: string,
        preferences: Partial<NotificationPreferences>
    ): Promise<NotificationPreferences> {
        const key = `${appId}:${userId}`;
        const existing = this.userPreferences.get(key);

        const updated: NotificationPreferences = {
            userId,
            appId,
            email: true,
            sms: true,
            push: true,
            inApp: true,
            webhook: true,
            slack: true,
            discord: true,
            telegram: true,
            updatedAt: new Date(),
            ...existing,
            ...preferences
        };

        this.userPreferences.set(key, updated);
        return updated;
    }

    /**
     * Get user preferences
     */
    getUserPreferences(userId: string, appId: string): NotificationPreferences | null {
        const key = `${appId}:${userId}`;
        return this.userPreferences.get(key) || null;
    }

    /**
     * Get notification statistics for an app
     */
    getNotificationStats(appId: string): NotificationStats {
        const history = this.notificationHistory.get(appId) || [];
        const providerStats = this.providerFactory.getMetrics();
        const appMetrics = providerStats[appId] || {};

        const totalSent = history.length;
        const totalDelivered = history.filter(n => n.status === 'delivered').length;
        const totalFailed = history.filter(n => n.status === 'failed').length;
        const successRate = totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0;

        const channelStats: Record<string, any> = {};
        const channels = ['email', 'sms', 'push', 'inApp', 'webhook', 'slack', 'discord', 'telegram'];

        channels.forEach(channel => {
            const channelNotifications = history.filter(n => n.channels[channel as keyof typeof n.channels]);
            const sent = channelNotifications.length;
            const delivered = channelNotifications.filter(n => n.status === 'delivered').length;
            const failed = channelNotifications.filter(n => n.status === 'failed').length;
            const channelSuccessRate = sent > 0 ? (delivered / sent) * 100 : 0;

            channelStats[channel] = {
                sent,
                delivered,
                failed,
                successRate: channelSuccessRate
            };
        });

        return {
            appId,
            totalSent,
            totalDelivered,
            totalFailed,
            successRate,
            averageResponseTime: appMetrics.averageResponseTime || 0,
            lastNotification: appMetrics.lastNotification ? new Date(appMetrics.lastNotification) : null,
            channelStats
        };
    }

    /**
     * Get all registered apps
     */
    getRegisteredApps(): AppConfig[] {
        return Array.from(this.appConfigs.values());
    }

    /**
     * Get app configuration
     */
    getAppConfig(appId: string): AppConfig | null {
        return this.appConfigs.get(appId) || null;
    }

    /**
     * Update app configuration
     */
    async updateAppConfig(appId: string, updates: Partial<AppConfig>): Promise<AppConfig> {
        const existing = this.appConfigs.get(appId);
        if (!existing) {
            throw new Error(`App ${appId} not found`);
        }

        const updated: AppConfig = {
            ...existing,
            ...updates,
            updatedAt: new Date()
        };

        this.appConfigs.set(appId, updated);
        return updated;
    }

    /**
     * Unregister an app
     */
    async unregisterApp(appId: string): Promise<boolean> {
        // Remove from provider factory
        const removed = this.providerFactory.unregisterApp(appId);

        if (removed) {
            // Clean up local data
            this.appConfigs.delete(appId);
            this.notificationHistory.delete(appId);
            this.rateLimitCounters.delete(appId);

            // Clean up user preferences for this app
            for (const [key] of this.userPreferences.entries()) {
                if (key.startsWith(`${appId}:`)) {
                    this.userPreferences.delete(key);
                }
            }

            console.log(`App unregistered: ${appId}`);
        }

        return removed;
    }

    /**
     * Check if an app is registered
     */
    isAppRegistered(appId: string): boolean {
        return this.appConfigs.has(appId);
    }

    /**
     * Get provider status for monitoring
     */
    getProviderStatus(): Record<string, any> {
        return this.providerFactory.getProviderStatus();
    }

    /**
     * Get app status for monitoring
     */
    getAppStatus(): Record<string, any> {
        return this.providerFactory.getAppStatus();
    }

    /**
     * Check rate limits for an app
     */
    private checkRateLimits(appId: string, notification: CreateNotificationRequest): boolean {
        const appConfig = this.appConfigs.get(appId);
        if (!appConfig || !appConfig.rateLimits) {
            return true; // No rate limits configured
        }

        const counters = this.rateLimitCounters.get(appId);
        if (!counters) {
            return true;
        }

        const now = Date.now();
        const { rateLimits } = appConfig;

        // Check per-minute limit
        if (rateLimits.requestsPerMinute) {
            const minuteKey = `minute:${Math.floor(now / 60000)}`;
            const minuteCount = counters.get(minuteKey) || 0;
            if (minuteCount >= rateLimits.requestsPerMinute) {
                return false;
            }
        }

        // Check per-hour limit
        if (rateLimits.requestsPerHour) {
            const hourKey = `hour:${Math.floor(now / 3600000)}`;
            const hourCount = counters.get(hourKey) || 0;
            if (hourCount >= rateLimits.requestsPerHour) {
                return false;
            }
        }

        // Check per-day limit
        if (rateLimits.requestsPerDay) {
            const dayKey = `day:${Math.floor(now / 86400000)}`;
            const dayCount = counters.get(dayKey) || 0;
            if (dayCount >= rateLimits.requestsPerDay) {
                return false;
            }
        }

        return true;
    }

    /**
     * Update rate limit counters
     */
    private updateRateLimitCounters(appId: string, notification: CreateNotificationRequest): void {
        const appConfig = this.appConfigs.get(appId);
        if (!appConfig || !appConfig.rateLimits) {
            return;
        }

        const counters = this.rateLimitCounters.get(appId);
        if (!counters) {
            return;
        }

        const now = Date.now();
        const { rateLimits } = appConfig;

        // Update per-minute counter
        if (rateLimits.requestsPerMinute) {
            const minuteKey = `minute:${Math.floor(now / 60000)}`;
            const current = counters.get(minuteKey) || 0;
            counters.set(minuteKey, current + 1);
        }

        // Update per-hour counter
        if (rateLimits.requestsPerHour) {
            const hourKey = `hour:${Math.floor(now / 3600000)}`;
            const current = counters.get(hourKey) || 0;
            counters.set(hourKey, current + 1);
        }

        // Update per-day counter
        if (rateLimits.requestsPerDay) {
            const dayKey = `day:${Math.floor(now / 86400000)}`;
            const current = counters.get(dayKey) || 0;
            counters.set(dayKey, current + 1);
        }
    }

    /**
     * Validate notification data
     */
    private validateNotification(notification: CreateNotificationRequest): boolean {
        if (!notification.appId || !notification.userId || !notification.title || !notification.content) {
            return false;
        }

        if (!notification.channels || Object.keys(notification.channels).length === 0) {
            return false;
        }

        // Check if at least one channel is enabled
        const hasEnabledChannel = Object.values(notification.channels).some(enabled => enabled);
        if (!hasEnabledChannel) {
            return false;
        }

        return true;
    }

    /**
     * Filter channels based on user preferences
     */
    private filterChannelsByPreferences(
        channels: Record<string, boolean>,
        preferences: NotificationPreferences
    ): Record<string, boolean> {
        const filtered: Record<string, boolean> = {};

        for (const [channel, enabled] of Object.entries(channels)) {
            if (enabled && preferences[channel as keyof NotificationPreferences]) {
                filtered[channel] = true;
            }
        }

        return filtered;
    }

    /**
     * Store notification in history
     */
    private storeNotificationHistory(
        appId: string,
        notification: CreateNotificationRequest,
        result: RoutingResult
    ): void {
        const history = this.notificationHistory.get(appId) || [];

        const notificationResponse: NotificationResponse = {
            id: result.notificationId,
            appId: notification.appId,
            userId: notification.userId,
            type: notification.type,
            title: notification.title,
            content: notification.content,
            priority: notification.priority || 'NORMAL',
            channels: notification.channels || {},
            metadata: notification.metadata,
            status: result.success ? 'delivered' : 'failed',
            createdAt: new Date(),
            updatedAt: new Date(),
            sentAt: new Date(),
            tags: notification.tags
        };

        history.push(notificationResponse);

        // Keep only last 1000 notifications per app
        if (history.length > 1000) {
            history.splice(0, history.length - 1000);
        }

        this.notificationHistory.set(appId, history);
    }

    /**
     * Clean up old rate limit counters
     */
    private cleanupOldCounters(): void {
        const now = Date.now();
        const oneDayAgo = now - 86400000;

        for (const [appId, counters] of this.rateLimitCounters.entries()) {
            for (const [key] of counters.entries()) {
                if (key.startsWith('minute:') || key.startsWith('hour:') || key.startsWith('day:')) {
                    const timestamp = parseInt(key.split(':')[1]);
                    if (timestamp < oneDayAgo) {
                        counters.delete(key);
                    }
                }
            }
        }
    }

    /**
     * Start cleanup interval
     */
    startCleanupInterval(intervalMs: number = 3600000): void { // Default: 1 hour
        setInterval(() => {
            this.cleanupOldCounters();
        }, intervalMs);
    }
}

// Export a default instance
export const multiTenantService = new MultiTenantNotificationService();

// Export the class for custom instances
export { MultiTenantNotificationService }; 