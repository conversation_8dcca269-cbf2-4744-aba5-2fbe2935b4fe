import {
    CreateNotificationRequest,
    NotificationResponse,
    NotificationPreferences,
    NotificationType,
    NotificationPriority,
    NotificationStatus,
    DeliveryChannels,
    NotificationMetadata
} from '../types';
import { MultiTenantNotificationService, AppRegistrationData } from './multiTenantService';
import { ProviderFactory } from '../providers/factory';

export interface NotificationServiceConfig {
    enableMultiTenant: boolean;
    defaultAppId?: string;
    enableMetrics: boolean;
    enableLogging: boolean;
    maxRetries: number;
    retryDelay: number;
}

export interface SendNotificationOptions {
    appId?: string;
    priority?: NotificationPriority;
    channels?: DeliveryChannels;
    scheduledAt?: Date;
    expiresAt?: Date;
    tags?: string[];
}

export interface BulkNotificationRequest {
    notifications: Array<{
        userId: string;
        type: NotificationType;
        title: string;
        content: string;
        metadata?: NotificationMetadata;
        options?: SendNotificationOptions;
    }>;
    appId: string;
    globalOptions?: SendNotificationOptions;
}

export class NotificationService {
    private multiTenantService: MultiTenantNotificationService;
    private providerFactory: ProviderFactory;
    private config: NotificationServiceConfig;
    private defaultAppId: string | null = null;

    constructor(config: Partial<NotificationServiceConfig> = {}) {
        this.config = {
            enableMultiTenant: true,
            defaultAppId: undefined,
            enableMetrics: true,
            enableLogging: true,
            maxRetries: 3,
            retryDelay: 1000,
            ...config
        };

        this.providerFactory = new ProviderFactory({
            enableMetrics: this.config.enableMetrics,
            enableLogging: this.config.enableLogging,
            globalMaxRetries: this.config.maxRetries,
            globalRetryDelay: this.config.retryDelay
        });

        this.multiTenantService = new MultiTenantNotificationService(this.providerFactory);

        if (this.config.defaultAppId) {
            this.defaultAppId = this.config.defaultAppId;
        }
    }

    /**
     * Register a new application
     */
    async registerApp(data: AppRegistrationData): Promise<void> {
        if (!this.config.enableMultiTenant) {
            throw new Error('Multi-tenant mode is disabled');
        }

        await this.multiTenantService.registerApp(data);

        if (this.config.enableLogging) {
            console.log(`Application registered: ${data.appId}`);
        }
    }

    /**
     * Send a single notification
     */
    async sendNotification(
        userId: string,
        type: NotificationType,
        title: string,
        content: string,
        metadata?: NotificationMetadata,
        options?: SendNotificationOptions
    ): Promise<NotificationResponse> {
        const appId = options?.appId || this.defaultAppId;
        if (!appId) {
            throw new Error('App ID is required. Either provide it in options or set a default app.');
        }

        const notification: CreateNotificationRequest = {
            appId,
            userId,
            type,
            title,
            content,
            priority: options?.priority || NotificationPriority.NORMAL,
            channels: options?.channels || { email: true, inApp: true },
            metadata,
            scheduledAt: options?.scheduledAt,
            expiresAt: options?.expiresAt,
            tags: options?.tags
        };

        if (this.config.enableLogging) {
            console.log(`Sending notification to user ${userId} via app ${appId}`);
        }

        const result = await this.multiTenantService.sendNotification(notification);

        if (!result.success) {
            throw new Error(`Failed to send notification: ${result.errors.map(e => e.error).join(', ')}`);
        }

        // Convert routing result to notification response
        const notificationResponse: NotificationResponse = {
            id: result.notificationId,
            appId: notification.appId,
            userId: notification.userId,
            type: notification.type,
            title: notification.title,
            content: notification.content,
            priority: notification.priority || NotificationPriority.NORMAL,
            channels: notification.channels || {},
            metadata: notification.metadata,
            status: NotificationStatus.DELIVERED,
            createdAt: new Date(),
            updatedAt: new Date(),
            sentAt: new Date(),
            tags: notification.tags
        };

        return notificationResponse;
    }

    /**
     * Send bulk notifications
     */
    async sendBulkNotifications(bulkRequest: BulkNotificationRequest): Promise<NotificationResponse[]> {
        const { notifications, appId, globalOptions } = bulkRequest;
        const results: NotificationResponse[] = [];

        if (this.config.enableLogging) {
            console.log(`Sending ${notifications.length} bulk notifications for app ${appId}`);
        }

        // Process notifications in batches to avoid overwhelming the system
        const batchSize = 10;
        for (let i = 0; i < notifications.length; i += batchSize) {
            const batch = notifications.slice(i, i + batchSize);

            const batchPromises = batch.map(async (notification) => {
                try {
                    return await this.sendNotification(
                        notification.userId,
                        notification.type,
                        notification.title,
                        notification.content,
                        notification.metadata,
                        {
                            appId,
                            ...globalOptions,
                            ...notification.options
                        }
                    );
                } catch (error) {
                    if (this.config.enableLogging) {
                        console.error(`Failed to send notification to user ${notification.userId}:`, error);
                    }

                    // Return a failed notification response
                    return {
                        id: `failed-${Date.now()}-${Math.random()}`,
                        appId,
                        userId: notification.userId,
                        type: notification.type,
                        title: notification.title,
                        content: notification.content,
                        priority: globalOptions?.priority || NotificationPriority.NORMAL,
                        channels: globalOptions?.channels || { email: true, inApp: true },
                        metadata: notification.metadata,
                        status: NotificationStatus.FAILED,
                        createdAt: new Date(),
                        updatedAt: new Date(),
                        tags: globalOptions?.tags
                    } as NotificationResponse;
                }
            });

            const batchResults = await Promise.allSettled(batchPromises);

            for (const result of batchResults) {
                if (result.status === 'fulfilled') {
                    results.push(result.value);
                }
            }

            // Small delay between batches to avoid rate limiting
            if (i + batchSize < notifications.length) {
                await this.delay(100);
            }
        }

        if (this.config.enableLogging) {
            const successCount = results.filter(r => r.status === NotificationStatus.DELIVERED).length;
            console.log(`Bulk notification completed: ${successCount}/${notifications.length} successful`);
        }

        return results;
    }

    /**
     * Send welcome notification
     */
    async sendWelcomeNotification(
        userId: string,
        metadata: NotificationMetadata,
        options?: SendNotificationOptions
    ): Promise<NotificationResponse> {
        return this.sendNotification(
            userId,
            NotificationType.WELCOME,
            'Welcome!',
            'We\'re excited to have you on board.',
            metadata,
            options
        );
    }

    /**
     * Send event reminder
     */
    async sendEventReminder(
        userId: string,
        eventName: string,
        eventTime: Date,
        metadata: NotificationMetadata,
        options?: SendNotificationOptions
    ): Promise<NotificationResponse> {
        const title = `Reminder: ${eventName}`;
        const content = `Don't forget! Your event "${eventName}" is scheduled for ${eventTime.toLocaleString()}`;

        return this.sendNotification(
            userId,
            NotificationType.EVENT_REMINDER,
            title,
            content,
            metadata,
            options
        );
    }

    /**
     * Send payment notification
     */
    async sendPaymentNotification(
        userId: string,
        amount: number,
        currency: string,
        isSuccess: boolean,
        metadata: NotificationMetadata,
        options?: SendNotificationOptions
    ): Promise<NotificationResponse> {
        const type = isSuccess ? NotificationType.PAYMENT_SUCCESSFUL : NotificationType.PAYMENT_FAILED;
        const title = isSuccess ? 'Payment Successful' : 'Payment Failed';
        const content = isSuccess
            ? `Your payment of ${currency}${amount} has been processed successfully.`
            : `Your payment of ${currency}${amount} could not be processed. Please try again.`;

        return this.sendNotification(
            userId,
            type,
            title,
            content,
            metadata,
            options
        );
    }

    /**
     * Send login notification
     */
    async sendLoginNotification(
        userId: string,
        deviceInfo: string,
        location: string,
        metadata: NotificationMetadata,
        options?: SendNotificationOptions
    ): Promise<NotificationResponse> {
        const title = 'New Login Detected';
        const content = `New login from ${deviceInfo} in ${location}. If this wasn't you, please secure your account.`;

        return this.sendNotification(
            userId,
            NotificationType.NEW_LOGIN,
            title,
            content,
            metadata,
            options
        );
    }

    /**
     * Update user preferences
     */
    async updateUserPreferences(
        userId: string,
        appId: string,
        preferences: Partial<NotificationPreferences>
    ): Promise<NotificationPreferences> {
        return this.multiTenantService.updateUserPreferences(userId, appId, preferences);
    }

    /**
     * Get user preferences
     */
    getUserPreferences(userId: string, appId: string): NotificationPreferences | null {
        return this.multiTenantService.getUserPreferences(userId, appId);
    }

    /**
     * Get notification history
     */
    getNotificationHistory(appId: string, limit?: number): NotificationResponse[] {
        return this.multiTenantService.getNotificationHistory(appId, limit);
    }

    /**
     * Get user notification history
     */
    getUserNotificationHistory(appId: string, userId: string, limit?: number): NotificationResponse[] {
        return this.multiTenantService.getUserNotificationHistory(appId, userId, limit);
    }

    /**
     * Get notification statistics
     */
    getNotificationStats(appId: string) {
        return this.multiTenantService.getNotificationStats(appId);
    }

    /**
     * Get provider status
     */
    getProviderStatus() {
        return this.providerFactory.getProviderStatus();
    }

    /**
     * Get app status
     */
    getAppStatus() {
        return this.multiTenantService.getAppStatus();
    }

    /**
     * Get registered apps
     */
    getRegisteredApps() {
        return this.multiTenantService.getRegisteredApps();
    }

    /**
     * Set default app ID
     */
    setDefaultApp(appId: string): void {
        if (!this.multiTenantService.isAppRegistered(appId)) {
            throw new Error(`App ${appId} is not registered`);
        }
        this.defaultAppId = appId;
    }

    /**
     * Get default app ID
     */
    getDefaultApp(): string | null {
        return this.defaultAppId;
    }

    /**
     * Check if multi-tenant mode is enabled
     */
    isMultiTenantEnabled(): boolean {
        return this.config.enableMultiTenant;
    }

    /**
     * Enable/disable logging
     */
    setLoggingEnabled(enabled: boolean): void {
        this.config.enableLogging = enabled;
    }

    /**
     * Enable/disable metrics
     */
    setMetricsEnabled(enabled: boolean): void {
        this.config.enableMetrics = enabled;
    }

    /**
     * Utility method for delays
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Export a default instance
export const notificationService = new NotificationService(); 