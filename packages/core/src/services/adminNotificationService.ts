import { AdminNotification } from '../types';

export interface AdminNotificationConfig {
    // Admin contact information
    adminEmail: string;
    adminName?: string;

    // Notification provider configurations
    novu?: {
        apiKey: string;
        appId: string;
        subscriberId?: string; // Admin's subscriber ID in Novu
    };

    resend?: {
        apiKey: string;
        fromEmail: string;
    };

    // Slack webhook (optional, for additional notifications)
    slack?: {
        webhookUrl: string;
        channel?: string;
        username?: string;
    };

    // Auto-approval settings
    autoApproveDomains?: string[];
    autoApproveEmails?: string[];
}

export interface AdminNotificationService {
    sendNotification(notification: AdminNotification): Promise<void>;
}

export class AdminNotificationService implements AdminNotificationService {
    private config: AdminNotificationConfig;

    constructor(config: AdminNotificationConfig) {
        this.config = config;
    }

    /**
     * Send admin notification using the best available method
     */
    async sendNotification(notification: AdminNotification): Promise<void> {
        try {
            // Try Novu first (if configured)
            if (this.config.novu) {
                await this.sendNovuNotification(notification);
                return;
            }

            // Fallback to Resend email
            if (this.config.resend) {
                await this.sendResendNotification(notification);
                return;
            }

            // Fallback to Slack webhook
            if (this.config.slack) {
                await this.sendSlackNotification(notification);
                return;
            }

            // Last resort: console log
            console.log('Admin notification (no providers configured):', notification);
        } catch (error) {
            console.error('Failed to send admin notification:', error);

            // Try fallback methods if primary method fails
            try {
                if (this.config.resend) {
                    await this.sendResendNotification(notification);
                } else if (this.config.slack) {
                    await this.sendSlackNotification(notification);
                }
            } catch (fallbackError) {
                console.error('All admin notification methods failed:', fallbackError);
            }
        }
    }

    /**
     * Send notification via Novu (preferred method)
     */
    private async sendNovuNotification(notification: AdminNotification): Promise<void> {
        if (!this.config.novu) return;

        try {
            // Use the notification system's own Novu integration
            const novuPayload = {
                name: 'admin-notification',
                to: {
                    subscriberId: this.config.novu.subscriberId || 'admin',
                    email: this.config.adminEmail,
                    firstName: this.config.adminName || 'Admin'
                },
                payload: {
                    type: notification.type,
                    appId: notification.appId,
                    appName: notification.appName,
                    contactEmail: notification.contactEmail,
                    contactName: notification.contactName,
                    businessType: notification.businessType,
                    expectedVolume: notification.expectedVolume,
                    message: this.generateNovuMessage(notification),
                    actionUrl: this.generateActionUrl(notification),
                    priority: notification.type === 'APP_REGISTRATION_REQUEST' ? 'HIGH' : 'NORMAL'
                }
            };

            // Send via Novu API
            const response = await fetch(`https://api.novu.co/v1/triggers/${novuPayload.name}`, {
                method: 'POST',
                headers: {
                    'Authorization': `ApiKey ${this.config.novu.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(novuPayload)
            });

            if (!response.ok) {
                throw new Error(`Novu API error: ${response.statusText}`);
            }

            console.log('Admin notification sent via Novu successfully');
        } catch (error) {
            console.error('Failed to send Novu notification:', error);
            throw error;
        }
    }

    /**
     * Send notification via Resend email
     */
    private async sendResendNotification(notification: AdminNotification): Promise<void> {
        if (!this.config.resend) return;

        try {
            const emailData = {
                from: this.config.resend.fromEmail,
                to: this.config.adminEmail,
                subject: this.generateEmailSubject(notification),
                html: this.generateEmailHTML(notification),
                text: this.generateEmailText(notification)
            };

            const response = await fetch('https://api.resend.com/emails', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.config.resend.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(emailData)
            });

            if (!response.ok) {
                throw new Error(`Resend API error: ${response.statusText}`);
            }

            console.log('Admin notification sent via Resend successfully');
        } catch (error) {
            console.error('Failed to send Resend notification:', error);
            throw error;
        }
    }

    /**
     * Send notification via Slack webhook (fallback method)
     */
    private async sendSlackNotification(notification: AdminNotification): Promise<void> {
        if (!this.config.slack) return;

        const message = this.getSlackMessage(notification);

        try {
            const response = await fetch(this.config.slack.webhookUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    text: message,
                    channel: this.config.slack.channel,
                    username: this.config.slack.username || 'Notification System',
                    icon_emoji: this.getSlackEmoji(notification.type)
                })
            });

            if (!response.ok) {
                throw new Error(`Slack API error: ${response.statusText}`);
            }

            console.log('Admin notification sent via Slack successfully');
        } catch (error) {
            console.error('Failed to send Slack notification:', error);
            throw error;
        }
    }

    /**
     * Generate Novu-specific message content
     */
    private generateNovuMessage(notification: AdminNotification): string {
        switch (notification.type) {
            case 'APP_REGISTRATION_REQUEST':
                return `New application registration request from ${notification.appName} (${notification.appId})`;
            case 'APP_APPROVED':
                return `Application ${notification.appName} has been approved`;
            case 'APP_REJECTED':
                return `Application ${notification.appName} has been rejected`;
            case 'APP_REVOKED':
                return `Application ${notification.appName} access has been revoked`;
            default:
                return `Admin notification: ${notification.type}`;
        }
    }

    /**
     * Generate action URL for Novu notifications
     */
    private generateActionUrl(notification: AdminNotification): string {
        // In a real system, this would link to your admin panel
        const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000';
        const adminUrl = `${baseUrl}/admin`;

        switch (notification.type) {
            case 'APP_REGISTRATION_REQUEST':
                return `${adminUrl}/applications/${notification.appId}/review`;
            case 'APP_APPROVED':
            case 'APP_REJECTED':
            case 'APP_REVOKED':
                return `${adminUrl}/applications/${notification.appId}`;
            default:
                return adminUrl;
        }
    }

    /**
     * Generate email subject line
     */
    private generateEmailSubject(notification: AdminNotification): string {
        switch (notification.type) {
            case 'APP_REGISTRATION_REQUEST':
                return `🚨 New App Registration: ${notification.appName}`;
            case 'APP_APPROVED':
                return `✅ App Approved: ${notification.appName}`;
            case 'APP_REJECTED':
                return `❌ App Rejected: ${notification.appName}`;
            case 'APP_REVOKED':
                return `🚫 App Access Revoked: ${notification.appName}`;
            default:
                return `Notification System Alert: ${notification.type}`;
        }
    }

    /**
     * Generate rich HTML email content
     */
    private generateEmailHTML(notification: AdminNotification): string {
        const statusColor = this.getStatusColor(notification.type);
        const statusIcon = this.getStatusIcon(notification.type);

        return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Admin Notification</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: ${statusColor}; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
          .status { display: inline-block; background: ${statusColor}; color: white; padding: 8px 16px; border-radius: 20px; font-weight: bold; }
          .app-info { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid ${statusColor}; }
          .contact-info { background: #e8f4fd; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .action-btn { display: inline-block; background: ${statusColor}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin-top: 15px; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${statusIcon} ${this.getStatusTitle(notification.type)}</h1>
            <div class="status">${notification.type.replace(/_/g, ' ')}</div>
          </div>

          <div class="content">
            <h2>Application Details</h2>
            <div class="app-info">
              <strong>App Name:</strong> ${notification.appName}<br>
              <strong>App ID:</strong> ${notification.appId}<br>
              <strong>Business Type:</strong> ${notification.businessType || 'Not specified'}<br>
              <strong>Expected Volume:</strong> ${notification.expectedVolume || 'Not specified'} notifications/month
            </div>

            <h3>Contact Information</h3>
            <div class="contact-info">
              <strong>Name:</strong> ${notification.contactName}<br>
              <strong>Email:</strong> ${notification.contactEmail}<br>
              ${notification.website ? `<strong>Website:</strong> ${notification.website}<br>` : ''}
            </div>

            ${notification.message ? `<p><strong>Message:</strong> ${notification.message}</p>` : ''}

            <a href="${this.generateActionUrl(notification)}" class="action-btn">
              Review Application
            </a>
          </div>

          <div class="footer">
            <p>This is an automated notification from your Notification System.</p>
            <p>Sent to: ${this.config.adminEmail}</p>
          </div>
        </div>
      </body>
      </html>
    `;
    }

    /**
     * Generate plain text email content
     */
    private generateEmailText(notification: AdminNotification): string {
        return `
${this.getStatusTitle(notification.type)}

Application Details:
- App Name: ${notification.appName}
- App ID: ${notification.appId}
- Business Type: ${notification.businessType || 'Not specified'}
- Expected Volume: ${notification.expectedVolume || 'Not specified'} notifications/month

Contact Information:
- Name: ${notification.contactName}
- Email: ${notification.contactEmail}
${notification.website ? `- Website: ${notification.website}` : ''}

${notification.message ? `Message: ${notification.message}` : ''}

Action Required: Review this application at ${this.generateActionUrl(notification)}

---
This is an automated notification from your Notification System.
Sent to: ${this.config.adminEmail}
    `.trim();
    }

    /**
     * Generate Slack message
     */
    private getSlackMessage(notification: AdminNotification): string {
        const statusIcon = this.getSlackEmoji(notification.type);
        const statusText = notification.type.replace(/_/g, ' ');

        let message = `${statusIcon} *${statusText}*\n\n`;
        message += `*App:* ${notification.appName} (\`${notification.appId}\`)\n`;
        message += `*Contact:* ${notification.contactName} (${notification.contactEmail})\n`;

        if (notification.businessType) {
            message += `*Business Type:* ${notification.businessType}\n`;
        }

        if (notification.expectedVolume) {
            message += `*Expected Volume:* ${notification.expectedVolume} notifications/month\n`;
        }

        if (notification.message) {
            message += `\n*Details:* ${notification.message}\n`;
        }

        message += `\n<${this.generateActionUrl(notification)}|Review Application>`;

        return message;
    }

    /**
     * Get status color for email styling
     */
    private getStatusColor(type: string): string {
        switch (type) {
            case 'APP_REGISTRATION_REQUEST':
                return '#f59e0b'; // Amber
            case 'APP_APPROVED':
                return '#10b981'; // Green
            case 'APP_REJECTED':
                return '#ef4444'; // Red
            case 'APP_REVOKED':
                return '#6b7280'; // Gray
            default:
                return '#3b82f6'; // Blue
        }
    }

    /**
     * Get status icon for email
     */
    private getStatusIcon(type: string): string {
        switch (type) {
            case 'APP_REGISTRATION_REQUEST':
                return '🚨';
            case 'APP_APPROVED':
                return '✅';
            case 'APP_REJECTED':
                return '❌';
            case 'APP_REVOKED':
                return '🚫';
            default:
                return '🔔';
        }
    }

    /**
     * Get status title for email
     */
    private getStatusTitle(type: string): string {
        switch (type) {
            case 'APP_REGISTRATION_REQUEST':
                return 'New Application Registration Request';
            case 'APP_APPROVED':
                return 'Application Approved';
            case 'APP_REJECTED':
                return 'Application Rejected';
            case 'APP_REVOKED':
                return 'Application Access Revoked';
            default:
                return 'Admin Notification';
        }
    }

    /**
     * Get Slack emoji for notification type
     */
    private getSlackEmoji(type: string): string {
        switch (type) {
            case 'APP_REGISTRATION_REQUEST':
                return ':bell:';
            case 'APP_APPROVED':
                return ':white_check_mark:';
            case 'APP_REJECTED':
                return ':x:';
            case 'APP_REVOKED':
                return ':no_entry:';
            default:
                return ':information_source:';
        }
    }
} 