import {
    AppRegistrationRequest,
    AppRegistrationResponse,
    AppApprovalRequest,
    AppApprovalResponse,
    AppAuthRequest,
    AppAuthResponse,
    AdminNotification,
    AppConfig,
    RateLimits
} from '../types';
import { MultiTenantNotificationService } from './multiTenantService';
import { ProviderFactory } from '../providers/factory';

export interface AdminNotificationService {
    sendNotification(notification: AdminNotification): Promise<void>;
}

export interface SecureAppRegistrationServiceConfig {
    adminEmail: string;
    adminSlackWebhook?: string;
    approvalRequired: boolean;
    autoApproveDomains?: string[]; // Auto-approve certain trusted domains
    defaultRateLimits: RateLimits;
    adminNotificationService: AdminNotificationService;
}

export class SecureAppRegistrationService {
    private multiTenantService: MultiTenantNotificationService;
    private providerFactory: ProviderFactory;
    private config: SecureAppRegistrationServiceConfig;
    private pendingRegistrations: Map<string, AppRegistrationRequest> = new Map();
    private approvedApps: Map<string, AppConfig> = new Map();
    private appApiKeys: Map<string, string> = new Map(); // appId -> apiKey
    private apiKeyToAppId: Map<string, string> = new Map(); // apiKey -> appId

    constructor(
        multiTenantService: MultiTenantNotificationService,
        providerFactory: ProviderFactory,
        config: SecureAppRegistrationServiceConfig
    ) {
        this.multiTenantService = multiTenantService;
        this.providerFactory = providerFactory;
        this.config = config;
    }

    /**
     * Register a new application (requires approval)
     */
    async registerApp(request: AppRegistrationRequest): Promise<AppRegistrationResponse> {
        try {
            // Validate request
            this.validateRegistrationRequest(request);

            // Check if app is already registered or pending
            if (this.appApiKeys.has(request.appId) || this.pendingRegistrations.has(request.appId)) {
                return {
                    success: false,
                    appId: request.appId,
                    status: 'REJECTED',
                    message: 'Application is already registered or pending approval'
                };
            }

            // Check if auto-approval applies
            if (this.shouldAutoApprove(request)) {
                return await this.autoApproveApp(request);
            }

            // Store pending registration
            this.pendingRegistrations.set(request.appId, request);

            // Send admin notification
            await this.notifyAdminForApproval(request);

            return {
                success: true,
                appId: request.appId,
                status: 'PENDING_APPROVAL',
                message: 'Application registration submitted for approval. You will be notified once approved.',
                estimatedReviewTime: '24-48 hours'
            };

        } catch (error) {
            return {
                success: false,
                appId: request.appId,
                status: 'REJECTED',
                message: `Registration failed: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
        }
    }

    /**
     * Authenticate an app using its API key
     */
    async authenticateApp(authRequest: AppAuthRequest): Promise<AppAuthResponse> {
        const appId = this.apiKeyToAppId.get(authRequest.apiKey);

        if (!appId || appId !== authRequest.appId) {
            return {
                success: false,
                appId: authRequest.appId,
                isAuthenticated: false,
                permissions: [],
                rateLimits: this.config.defaultRateLimits
            };
        }

        const appConfig = this.approvedApps.get(appId);
        if (!appConfig) {
            return {
                success: false,
                appId: authRequest.appId,
                isAuthenticated: false,
                permissions: [],
                rateLimits: this.config.defaultRateLimits
            };
        }

        return {
            success: true,
            appId: authRequest.appId,
            isAuthenticated: true,
            permissions: ['send_notifications', 'read_notifications', 'manage_preferences'],
            rateLimits: appConfig.rateLimits || this.config.defaultRateLimits
        };
    }

    /**
     * Approve or reject an application (admin only)
     */
    async approveApp(approvalRequest: AppApprovalRequest): Promise<AppApprovalResponse> {
        const pendingRequest = this.pendingRegistrations.get(approvalRequest.appId);

        if (!pendingRequest) {
            return {
                success: false,
                appId: approvalRequest.appId,
                status: 'REJECTED',
                message: 'Application not found in pending registrations'
            };
        }

        if (approvalRequest.action === 'APPROVE') {
            return await this.approveApplication(pendingRequest, approvalRequest);
        } else {
            return await this.rejectApplication(pendingRequest, approvalRequest);
        }
    }

    /**
     * Get all pending registrations (admin only)
     */
    getPendingRegistrations(): AppRegistrationRequest[] {
        return Array.from(this.pendingRegistrations.values());
    }

    /**
     * Get all approved applications (admin only)
     */
    getApprovedApps(): AppConfig[] {
        return Array.from(this.approvedApps.values());
    }

    /**
     * Revoke an application's access (admin only)
     */
    async revokeApp(appId: string, reason: string): Promise<boolean> {
        if (!this.approvedApps.has(appId)) {
            return false;
        }

        const apiKey = this.appApiKeys.get(appId);
        if (apiKey) {
            this.apiKeyToAppId.delete(apiKey);
            this.appApiKeys.delete(appId);
        }

        this.approvedApps.delete(appId);
        this.multiTenantService.unregisterApp(appId);

        // Notify admin of revocation
        await this.config.adminNotificationService.sendNotification({
            type: 'APP_USAGE_ALERT',
            appId,
            appName: 'Unknown',
            contactEmail: '<EMAIL>',
            contactName: 'System Admin',
            message: `Application ${appId} access revoked. Reason: ${reason}`,
            timestamp: new Date()
        });

        return true;
    }

    // Private methods

    private validateRegistrationRequest(request: AppRegistrationRequest): void {
        if (!request.appId || !request.name || !request.contactEmail || !request.contactName) {
            throw new Error('Missing required fields: appId, name, contactEmail, contactName');
        }

        if (Object.keys(request.providers).length === 0) {
            throw new Error('At least one provider must be configured');
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(request.contactEmail)) {
            throw new Error('Invalid contact email format');
        }

        // Validate appId format (alphanumeric and hyphens only)
        const appIdRegex = /^[a-zA-Z0-9-]+$/;
        if (!appIdRegex.test(request.appId)) {
            throw new Error('App ID can only contain letters, numbers, and hyphens');
        }
    }

    private shouldAutoApprove(request: AppRegistrationRequest): boolean {
        if (!this.config.autoApproveDomains) {
            return false;
        }

        const domain = request.contactEmail.split('@')[1];
        return this.config.autoApproveDomains.includes(domain);
    }

    private async autoApproveApp(request: AppRegistrationRequest): Promise<AppRegistrationResponse> {
        try {
            const apiKey = this.generateSecureApiKey();
            await this.completeApproval(request, apiKey, 'Auto-approved for trusted domain');

            return {
                success: true,
                appId: request.appId,
                status: 'APPROVED',
                message: 'Application auto-approved and registered successfully'
            };
        } catch (error) {
            return {
                success: false,
                appId: request.appId,
                status: 'REJECTED',
                message: `Auto-approval failed: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
        }
    }

    private async approveApplication(
        request: AppRegistrationRequest,
        approvalRequest: AppApprovalRequest
    ): Promise<AppApprovalResponse> {
        try {
            const apiKey = this.generateSecureApiKey();
            await this.completeApproval(request, apiKey, approvalRequest.adminNotes || 'Approved by admin');

            // Remove from pending
            this.pendingRegistrations.delete(request.appId);

            // Notify admin of successful approval
            await this.config.adminNotificationService.sendNotification({
                type: 'APP_APPROVAL_NEEDED',
                appId: request.appId,
                appName: request.name,
                contactEmail: request.contactEmail,
                contactName: request.contactName,
                message: `Application ${request.name} has been approved and registered successfully. API Key: ${apiKey}`,
                actionUrl: `/admin/apps/${request.appId}`,
                timestamp: new Date()
            });

            return {
                success: true,
                appId: request.appId,
                status: 'APPROVED',
                apiKey,
                message: 'Application approved and registered successfully'
            };

        } catch (error) {
            return {
                success: false,
                appId: request.appId,
                status: 'REJECTED',
                message: `Approval failed: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
        }
    }

    private async rejectApplication(
        request: AppRegistrationRequest,
        approvalRequest: AppApprovalRequest
    ): Promise<AppApprovalResponse> {
        // Remove from pending
        this.pendingRegistrations.delete(request.appId);

        // Notify admin of rejection
        await this.config.adminNotificationService.sendNotification({
            type: 'APP_APPROVAL_NEEDED',
            appId: request.appId,
            appName: request.name,
            contactEmail: request.contactEmail,
            contactName: request.contactName,
            message: `Application ${request.name} has been rejected. Reason: ${approvalRequest.reason || 'No reason provided'}`,
            actionUrl: `/admin/apps/${request.appId}`,
            timestamp: new Date()
        });

        return {
            success: true,
            appId: request.appId,
            status: 'REJECTED',
            message: `Application rejected. Reason: ${approvalRequest.reason || 'No reason provided'}`
        };
    }

    private async completeApproval(request: AppRegistrationRequest, apiKey: string, adminNotes: string): Promise<void> {
        // Store API key mappings
        this.appApiKeys.set(request.appId, apiKey);
        this.apiKeyToAppId.set(apiKey, request.appId);

        // Register with multi-tenant service
        const appConfig = await this.multiTenantService.registerApp({
            appId: request.appId,
            name: request.name,
            description: request.description,
            providers: request.providers,
            fallbackStrategy: request.fallbackStrategy,
            maxRetries: request.maxRetries,
            retryDelay: request.retryDelay,
            rateLimits: request.rateLimits
        });

        // Store approved app config
        this.approvedApps.set(request.appId, appConfig);
    }

    private async notifyAdminForApproval(request: AppRegistrationRequest): Promise<void> {
        const notification: AdminNotification = {
            type: 'APP_REGISTRATION_REQUEST',
            appId: request.appId,
            appName: request.name,
            contactEmail: request.contactEmail,
            contactName: request.contactName,
            message: `New application registration request from ${request.name} (${request.contactName} at ${request.contactEmail})`,
            actionUrl: `/admin/registrations/${request.appId}`,
            timestamp: new Date()
        };

        await this.config.adminNotificationService.sendNotification(notification);
    }

    private generateSecureApiKey(): string {
        // Generate a cryptographically secure API key
        if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
            // Browser environment
            const array = new Uint8Array(32);
            crypto.getRandomValues(array);
            return `spark_${Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')}`;
        } else {
            // Node.js environment - use a fallback for now
            const timestamp = Date.now().toString(36);
            const random = Math.random().toString(36).substring(2);
            return `spark_${timestamp}_${random}`;
        }
    }
} 