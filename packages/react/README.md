# @sparkstrand/notifications-react

React components and hooks for the Spark Strand notification system, designed for web applications.

## 🚀 **Quick Start**

```typescript
import { 
    NotificationCenter, 
    NotificationBadge,
    useNotifications,
    useNotificationPreferences 
} from '@sparkstrand/notifications-react';

// In your component
function MyComponent() {
    const { notifications, sendNotification } = useNotifications();
    const { preferences, updatePreferences } = useNotificationPreferences();

    return (
        <NotificationCenter>
            <NotificationBadge count={notifications.length} />
            {/* Your app content */}
        </NotificationCenter>
    );
}
```

## 🌐 **Features**

- **In-App Notifications**: Real-time notification display
- **User Preferences**: Channel and frequency controls
- **Notification Center**: Centralized notification management
- **Badge Support**: Unread notification counters
- **Responsive Design**: Works on all screen sizes
- **Accessibility**: WCAG compliant components

## 🔧 **Installation**

```bash
npm install @sparkstrand/notifications-react
```

## 🎯 **Core Components**

### **NotificationCenter**

The main wrapper component that provides notification context:

```typescript
import { NotificationCenter } from '@sparkstrand/notifications-react';

export default function App() {
    return (
        <NotificationCenter>
            {/* Your app components */}
        </NotificationCenter>
    );
}
```

### **NotificationBadge**

Displays unread notification count:

```typescript
import { NotificationBadge } from '@sparkstrand/notifications-react';

function Header() {
    return (
        <header>
            <h1>My App</h1>
            <NotificationBadge count={5} />
        </header>
    );
}
```

### **NotificationItem**

Individual notification display:

```typescript
import { NotificationItem } from '@sparkstrand/notifications-react';

function NotificationList() {
    return (
        <div>
            {notifications.map(notification => (
                <NotificationItem
                    key={notification.id}
                    notification={notification}
                    onPress={() => handleNotificationPress(notification)}
                />
            ))}
        </div>
    );
}
```

### **NotificationList**

List of notifications with built-in rendering:

```typescript
import { NotificationList } from '@sparkstrand/notifications-react';

function NotificationsPage() {
    return (
        <NotificationList
            notifications={notifications}
            onNotificationPress={handlePress}
            onNotificationDismiss={handleDismiss}
        />
    );
}
```

### **NotificationPreferences**

User preference management:

```typescript
import { NotificationPreferences } from '@sparkstrand/notifications-react';

function SettingsPage() {
    return (
        <NotificationPreferences
            preferences={preferences}
            onPreferencesChange={updatePreferences}
        />
    );
}
```

## 🪝 **Hooks**

### **useNotifications**

Main hook for notification operations:

```typescript
import { useNotifications } from '@sparkstrand/notifications-react';

function MyComponent() {
    const {
        notifications,
        sendNotification,
        markAsRead,
        markAllAsRead,
        deleteNotification,
        isLoading,
        error
    } = useNotifications();

    const handleSendNotification = async () => {
        await sendNotification({
            type: 'EVENT_REMINDER',
            title: 'Event Tomorrow!',
            content: 'Don\'t forget your event',
            metadata: { email: '<EMAIL>' }
        });
    };

    return (
        <div>
            <p>Notifications: {notifications.length}</p>
            <button onClick={handleSendNotification}>Send Test</button>
        </div>
    );
}
```

### **useNotificationPreferences**

Manage user notification preferences:

```typescript
import { useNotificationPreferences } from '@sparkstrand/notifications-react';

function PreferencesComponent() {
    const {
        preferences,
        updatePreferences,
        isLoading,
        error
    } = useNotificationPreferences();

    const toggleEmail = () => {
        updatePreferences({ email: !preferences.email });
    };

    return (
        <div>
            <label>
                <input
                    type="checkbox"
                    checked={preferences.email}
                    onChange={toggleEmail}
                />
                Email Notifications
            </label>
        </div>
    );
}
```

## 🎨 **Customization**

### **Styling Components**

All components accept standard CSS classes and inline styles:

```typescript
<NotificationBadge 
    count={5}
    className="custom-badge"
    style={{ backgroundColor: 'red', borderRadius: '10px' }}
/>
```

### **Custom Rendering**

Override default rendering with custom components:

```typescript
<NotificationList
    notifications={notifications}
    renderNotification={(notification) => (
        <CustomNotificationItem notification={notification} />
    )}
/>
```

## 🔧 **Configuration**

### **Environment Variables**

```bash
# Your app's notification service configuration
NOTIFICATION_API_URL=https://your-api.com
```

### **App Configuration**

```typescript
// In your app initialization
import { configureNotifications } from '@sparkstrand/notifications-react';

configureNotifications({
    apiUrl: 'https://your-api.com',
    appId: 'your-app-id',
    enableInAppNotifications: true
});
```

## 🧪 **Testing**

### **Test Notifications**

```typescript
import { testNotification } from '@sparkstrand/notifications-react';

// Send a test notification
await testNotification({
    type: 'TEST',
    title: 'Test Notification',
    content: 'This is a test notification'
});
```

### **Mock Mode**

```typescript
import { enableMockMode } from '@sparkstrand/notifications-react';

// Enable mock mode for testing
enableMockMode();
```

## 🚀 **Integration with Core System**

This package integrates seamlessly with the `@sparkstrand/notifications-core` package:

```typescript
// In your backend
import { notificationService } from '@sparkstrand/notifications-core';

// Send notification to web app
await notificationService.sendNotification(
    'user-123',
    'EVENT_REMINDER',
    'Event Tomorrow!',
    'Don\'t forget your event',
    { userId: 'user-123' },
    { channels: { inApp: true } }
);
```

## 📚 **Additional Resources**

- **Core Package**: [@sparkstrand/notifications-core](../core/README.md)
- **API Documentation**: [API Reference](../core/README.md)
- **Examples**: [Usage Examples](../core/examples/)

---

**Built for React web applications with comprehensive notification support and seamless integration with the Spark Strand notification system.** 