{"name": "@sparkstrand/notifications-react", "version": "0.0.0", "description": "React and Next.js integration for the Spark Strand notifications system", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint . --max-warnings 0", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@novu/react": "3.7.0", "@sparkstrand/notifications-core": "workspace:*"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "devDependencies": {"@types/node": "^22.10.7", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9", "react": "^19.0.0", "react-dom": "^19.0.0", "tsup": "^8.3.5", "typescript": "^5.7.3"}, "files": ["dist/**"], "publishConfig": {"access": "restricted"}, "repository": {"type": "git", "url": "https://github.com/sparkstrand/notifications.git", "directory": "packages/react"}, "keywords": ["notifications", "react", "hooks", "components", "novu", "inbox"], "license": "MIT"}