import React, { useState } from 'react';
import { NovuInbox, NovuInboxExample } from '../index';

export const NovuInboxDemo: React.FC = () => {
    const [config, setConfig] = useState({
        appId: 'demo-app',
        apiKey: 'demo-api-key',
        userId: 'demo-user-123'
    });

    const [showExample, setShowExample] = useState(false);

    return (
        <div className="novu-inbox-demo">
            <header className="demo-header">
                <h1>🚀 Novu Inbox Demo</h1>
                <p>Experience the power of our Novu inbox integration</p>
            </header>

            <main className="demo-content">
                {/* Configuration Section */}
                <section className="config-section">
                    <h2>Configuration</h2>
                    <div className="config-form">
                        <div className="form-group">
                            <label htmlFor="appId">App ID:</label>
                            <input
                                id="appId"
                                type="text"
                                value={config.appId}
                                onChange={(e) => setConfig(prev => ({ ...prev, appId: e.target.value }))}
                                placeholder="Enter your app ID"
                            />
                        </div>

                        <div className="form-group">
                            <label htmlFor="apiKey">API Key:</label>
                            <input
                                id="apiKey"
                                type="text"
                                value={config.apiKey}
                                onChange={(e) => setConfig(prev => ({ ...prev, apiKey: e.target.value }))}
                                placeholder="Enter your API key"
                            />
                        </div>

                        <div className="form-group">
                            <label htmlFor="userId">User ID:</label>
                            <input
                                id="userId"
                                type="text"
                                value={config.userId}
                                onChange={(e) => setConfig(prev => ({ ...prev, userId: e.target.value }))}
                                placeholder="Enter user ID"
                            />
                        </div>
                    </div>
                </section>

                {/* Basic Novu Inbox */}
                <section className="basic-inbox-section">
                    <h2>Basic Novu Inbox</h2>
                    <p>Simple notification bell with basic functionality</p>

                    <div className="inbox-container">
                        <NovuInbox
                            appId={config.appId}
                            apiKey={config.apiKey}
                            userId={config.userId}
                            theme="light"
                            position="top"
                            showUserPreferences={true}
                        />
                    </div>
                </section>

                {/* Enhanced Example */}
                <section className="enhanced-section">
                    <h2>Enhanced Example</h2>
                    <p>Advanced integration with custom handlers and status display</p>

                    <button
                        onClick={() => setShowExample(!showExample)}
                        className="toggle-button"
                    >
                        {showExample ? 'Hide' : 'Show'} Enhanced Example
                    </button>

                    {showExample && (
                        <div className="enhanced-container">
                            <NovuInboxExample
                                appId={config.appId}
                                apiKey={config.apiKey}
                                userId={config.userId}
                            />
                        </div>
                    )}
                </section>

                {/* Features List */}
                <section className="features-section">
                    <h2>Features</h2>
                    <div className="features-grid">
                        <div className="feature-card">
                            <h3>🔔 Real-time Notifications</h3>
                            <p>Get instant updates via WebSocket connections</p>
                        </div>

                        <div className="feature-card">
                            <h3>🎨 Beautiful UI</h3>
                            <p>Professional notification center with light/dark themes</p>
                        </div>

                        <div className="feature-card">
                            <h3>⚙️ User Preferences</h3>
                            <p>Customizable notification settings and preferences</p>
                        </div>

                        <div className="feature-card">
                            <h3>📱 Responsive Design</h3>
                            <p>Works perfectly on all devices and screen sizes</p>
                        </div>

                        <div className="feature-card">
                            <h3>🔒 Secure Integration</h3>
                            <p>Secure API key authentication and user isolation</p>
                        </div>

                        <div className="feature-card">
                            <h3>🚀 Production Ready</h3>
                            <p>Built for production with error handling and fallbacks</p>
                        </div>
                    </div>
                </section>

                {/* Usage Instructions */}
                <section className="usage-section">
                    <h2>How to Use</h2>
                    <div className="instructions">
                        <ol>
                            <li>
                                <strong>Configure:</strong> Set your app ID, API key, and user ID above
                            </li>
                            <li>
                                <strong>Click the Bell:</strong> Click the notification bell icon to open the inbox
                            </li>
                            <li>
                                <strong>View Notifications:</strong> Browse through your notifications
                            </li>
                            <li>
                                <strong>Interact:</strong> Click on notifications to mark them as read
                            </li>
                            <li>
                                <strong>Customize:</strong> Use the settings to adjust your preferences
                            </li>
                        </ol>
                    </div>
                </section>
            </main>

            <footer className="demo-footer">
                <p>
                    <strong>Ready to integrate?</strong> Check out our{' '}
                    <a href="../docs/NOVU_INBOX_QUICK_START.md">Quick Start Guide</a> and{' '}
                    <a href="../docs/NOVU_INBOX_INTEGRATION.md">Full Documentation</a>
                </p>
            </footer>
        </div>
    );
};

export default NovuInboxDemo; 