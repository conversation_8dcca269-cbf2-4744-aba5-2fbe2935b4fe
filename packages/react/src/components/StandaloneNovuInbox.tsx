import React from 'react';
import { NovuProvider, PopoverNotificationCenter, NotificationBell, IMessage } from '@novu/notification-center';

export interface StandaloneNovuInboxProps {
    appId: string;
    apiKey: string;
    userId: string;
    subscriberId?: string;
    theme?: 'light' | 'dark';
    position?: 'top' | 'bottom';
    showUserPreferences?: boolean;
    onNotificationClick?: (notification: IMessage) => void;
    onUnreadCountChange?: (count: number) => void;
    className?: string;
    style?: React.CSSProperties;
}

export const StandaloneNovuInbox: React.FC<StandaloneNovuInboxProps> = ({
    appId,
    apiKey,
    userId,
    subscriberId,
    theme = 'light',
    position = 'top',
    showUserPreferences = true,
    onNotificationClick,
    onUnreadCountChange,
    className,
    style
}) => {
    const novuSubscriberId = subscriberId || `${userId}-${appId}`;

    // Handle notification click
    const handleNotificationClick = (notification: IMessage) => {
        if (onNotificationClick) {
            onNotificationClick(notification);
        }
    };

    // Handle notification received
    const handleNotificationReceived = (notification: IMessage) => {
        console.log('New notification received:', notification);
    };

    // Handle notification seen
    const handleNotificationSeen = (notification: IMessage) => {
        console.log('Notification seen:', notification);
    };

    return (
        <NovuProvider
            backendUrl={process.env.NEXT_PUBLIC_NOVU_BACKEND_URL || 'https://api.novu.co'}
            socketUrl={process.env.NEXT_PUBLIC_NOVU_SOCKET_URL || 'https://ws.novu.co'}
            applicationIdentifier={appId}
            subscriberId={novuSubscriberId}
            onNotificationClick={handleNotificationClick}
            onNotificationReceived={handleNotificationReceived}
            onNotificationSeen={handleNotificationSeen}
        >
            <div className={`novu-inbox-container ${className || ''}`} style={style}>
                <PopoverNotificationCenter
                    position={position}
                    theme={theme}
                    showUserPreferences={showUserPreferences}
                    colorScheme={theme}
                >
                    <NotificationBell
                        colorScheme={theme}
                        className="novu-notification-bell"
                    />
                </PopoverNotificationCenter>
            </div>
        </NovuProvider>
    );
};

export default StandaloneNovuInbox; 