import React, { useState, useEffect } from 'react';
import { NovuInbox } from './NovuInbox';
import { IMessage } from '@novu/notification-center';

export interface NovuInboxExampleProps {
    appId: string;
    apiKey: string;
    userId: string;
}

export const NovuInboxExample: React.FC<NovuInboxExampleProps> = ({
    appId,
    apiKey,
    userId
}) => {
    const [notificationCount, setNotificationCount] = useState(0);
    const [lastNotification, setLastNotification] = useState<IMessage | null>(null);
    const [isConnected, setIsConnected] = useState(false);

    // Simulate connection status
    useEffect(() => {
        const timer = setTimeout(() => setIsConnected(true), 1000);
        return () => clearTimeout(timer);
    }, []);

    // Handle notification clicks
    const handleNotificationClick = (notification: IMessage) => {
        console.log('Notification clicked:', notification);
        setLastNotification(notification);

        // You can add custom logic here:
        // - Navigate to specific pages
        // - Update application state
        // - Trigger actions
        // - Mark as read in your backend
    };

    // Handle unread count changes
    const handleUnreadCountChange = (count: number) => {
        setNotificationCount(count);
        console.log('Unread notifications:', count);
    };

    return (
        <div className="novu-inbox-example">
            {/* Connection Status */}
            <div className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
                {isConnected ? '🟢 Connected to Novu' : '🟡 Connecting...'}
            </div>

            {/* Notification Count Display */}
            <div className="notification-info">
                <span className="count-badge">
                    {notificationCount} unread
                </span>
            </div>

            {/* Novu Inbox Integration */}
            <NovuInbox
                appId={appId}
                apiKey={apiKey}
                userId={userId}
                theme="light"
                position="top"
                showUserPreferences={true}
                onNotificationClick={handleNotificationClick}
                onUnreadCountChange={handleUnreadCountChange}
            />

            {/* Last Notification Display */}
            {lastNotification && (
                <div className="last-notification">
                    <h4>Last Clicked Notification:</h4>
                    <div className="notification-details">
                        <strong>{lastNotification.title}</strong>
                        <p>{lastNotification.content}</p>
                        <small>Received: {new Date().toLocaleTimeString()}</small>
                    </div>
                </div>
            )}

            {/* Usage Instructions */}
            <div className="usage-instructions">
                <h4>How to Use:</h4>
                <ol>
                    <li>Click the notification bell icon above</li>
                    <li>View your notifications in the popup</li>
                    <li>Click on notifications to mark them as read</li>
                    <li>Use the settings to customize your preferences</li>
                </ol>
            </div>
        </div>
    );
};

export default NovuInboxExample; 