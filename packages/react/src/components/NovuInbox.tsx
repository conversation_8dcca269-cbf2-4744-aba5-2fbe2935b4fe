import React, { useEffect, useState } from 'react';
import { NovuProvider, PopoverNotificationCenter, NotificationBell, IMessage } from '@novu/notification-center';

export interface NovuInboxProps {
    appId: string;
    apiKey: string;
    userId: string;
    subscriberId?: string;
    theme?: 'light' | 'dark';
    position?: 'top' | 'bottom';
    showUserPreferences?: boolean;
    onNotificationClick?: (notification: IMessage) => void;
    onUnreadCountChange?: (count: number) => void;
    className?: string;
    style?: React.CSSProperties;
}

export interface NovuInboxConfig {
    backendUrl?: string;
    socketUrl?: string;
    applicationIdentifier: string;
    subscriberId: string;
    subscriberEmail?: string;
    subscriberFirstName?: string;
    subscriberLastName?: string;
    subscriberPhone?: string;
    subscriberAvatar?: string;
    subscriberLocale?: string;
}

export const NovuInbox: React.FC<NovuInboxProps> = ({
    appId,
    apiKey,
    userId,
    subscriberId,
    theme = 'light',
    position = 'top',
    showUserPreferences = true,
    onNotificationClick,
    onUnreadCountChange,
    className,
    style
}) => {
    const [novuSubscriberId, setNovuSubscriberId] = useState<string>(subscriberId || `${userId}-${appId}`);
    const [unreadCount, setUnreadCount] = useState(0);

    // Update unread count when it changes
    useEffect(() => {
        if (onUnreadCountChange) {
            onUnreadCountChange(unreadCount);
        }
    }, [unreadCount, onUnreadCountChange]);

    // Create Novu configuration
    const novuConfig: NovuInboxConfig = {
        backendUrl: process.env.NEXT_PUBLIC_NOVU_BACKEND_URL || 'https://api.novu.co',
        socketUrl: process.env.NEXT_PUBLIC_NOVU_SOCKET_URL || 'https://ws.novu.co',
        applicationIdentifier: appId,
        subscriberId: novuSubscriberId,
        subscriberLocale: 'en',
    };

    // Handle notification click
    const handleNotificationClick = (notification: IMessage) => {
        // Call custom handler if provided
        if (onNotificationClick) {
            onNotificationClick(notification);
        }
    };

    // Handle notification received
    const handleNotificationReceived = (notification: IMessage) => {
        // You can add custom logic here when new notifications arrive
        console.log('New notification received:', notification);
    };

    // Handle notification seen
    const handleNotificationSeen = (notification: IMessage) => {
        // You can add custom logic here when notifications are seen
        console.log('Notification seen:', notification);
    };

    return (
        <NovuProvider
            backendUrl={novuConfig.backendUrl}
            socketUrl={novuConfig.socketUrl}
            applicationIdentifier={novuConfig.applicationIdentifier}
            subscriberId={novuConfig.subscriberId}
            subscriberEmail={novuConfig.subscriberEmail}
            subscriberFirstName={novuConfig.subscriberFirstName}
            subscriberLastName={novuConfig.subscriberLastName}
            subscriberPhone={novuConfig.subscriberPhone}
            subscriberAvatar={novuConfig.subscriberAvatar}
            subscriberLocale={novuConfig.subscriberLocale}
            onNotificationClick={handleNotificationClick}
            onNotificationReceived={handleNotificationReceived}
            onNotificationSeen={handleNotificationSeen}
        >
            <div className={`novu-inbox-container ${className || ''}`} style={style}>
                <PopoverNotificationCenter
                    position={position}
                    theme={theme}
                    showUserPreferences={showUserPreferences}
                    colorScheme={theme}
                >
                    <NotificationBell
                        colorScheme={theme}
                        unseenCount={unreadCount}
                        className="novu-notification-bell"
                    />
                </PopoverNotificationCenter>
            </div>
        </NovuProvider>
    );
};

export default NovuInbox; 