import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
    CreateNotificationRequest,
    BaseNotification,
    NotificationPreferences,
    NotificationType,
    DeliveryChannels,
    NotificationStatus,
    NotificationPriority,
} from '@sparkstrand/notifications-core';

// Configuration interface for the notifications system
export interface NotificationsConfig {
    apiUrl: string;
    appId: string;
    userId: string;
    autoConnect?: boolean;
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
}

// Hook return interfaces
export interface UseNotificationsReturn {
    // State
    notifications: BaseNotification[];
    unreadCount: number;
    isLoading: boolean;
    error: string | null;
    isConnected: boolean;

    // Actions
    sendNotification: (notification: Omit<CreateNotificationRequest, 'appId'>) => Promise<boolean>;
    markAsRead: (notificationId: string) => Promise<boolean>;
    markAllAsRead: () => Promise<boolean>;
    deleteNotification: (notificationId: string) => Promise<boolean>;
    refreshNotifications: () => Promise<void>;

    // Real-time
    connect: () => void;
    disconnect: () => void;

    // Utilities
    clearError: () => void;
}

export interface UseNotificationPreferencesReturn {
    preferences: NotificationPreferences | null;
    isLoading: boolean;
    error: string | null;
    fetchPreferences: () => Promise<void>;
    updatePreferences: (updates: Partial<NotificationPreferences>) => Promise<boolean>;
}

export interface UseNotificationReturn {
    notification: BaseNotification | null;
    isLoading: boolean;
    error: string | null;
    fetchNotification: () => Promise<void>;
}

export interface UseNotificationStatsReturn {
    stats: {
        total: number;
        unread: number;
        read: number;
        byType: Record<NotificationType, number>;
        byChannel: Record<keyof DeliveryChannels, number>;
    };
    isLoading: boolean;
    error: string | null;
    fetchStats: () => Promise<void>;
}

/**
 * Main hook for managing notifications in React applications
 */
export function useNotifications(config: NotificationsConfig): UseNotificationsReturn {
    const [notifications, setNotifications] = useState<BaseNotification[]>([]);
    const [unreadCount, setUnreadCount] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isConnected, setIsConnected] = useState(false);

    const reconnectTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
    const reconnectAttemptsRef = useRef(0);
    const eventSourceRef = useRef<EventSource | undefined>(undefined);

    const {
        apiUrl,
        appId,
        userId,
        autoConnect = true,
        reconnectInterval = 5000,
        maxReconnectAttempts = 5,
    } = config;

    // API helper functions
    const apiCall = useCallback(async <T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<T> => {
        const url = `${apiUrl}${endpoint}`;
        const response = await fetch(url, {
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers,
            },
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        return response.json();
    }, [apiUrl]);

    // Fetch notifications from API
    const fetchNotifications = useCallback(async () => {
        try {
            setIsLoading(true);
            setError(null);

            const response = await apiCall<{ success: boolean; data?: { notifications: BaseNotification[] } }>(
                `/api/notifications?appId=${appId}&userId=${userId}&page=1&limit=100`
            );

            if (response.success && response.data) {
                setNotifications(response.data.notifications);
                setUnreadCount(response.data.notifications.filter(n => !n.isRead).length);
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch notifications');
        } finally {
            setIsLoading(false);
        }
    }, [apiCall, appId, userId]);

    // Send notification
    const sendNotification = useCallback(async (
        notification: Omit<CreateNotificationRequest, 'appId'>
    ): Promise<boolean> => {
        try {
            setError(null);

            const response = await apiCall<{ success: boolean }>('/api/notifications', {
                method: 'POST',
                body: JSON.stringify({
                    ...notification,
                    appId,
                }),
            });

            if (response.success) {
                // Refresh notifications to show the new one
                await fetchNotifications();
                return true;
            }

            return false;
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to send notification');
            return false;
        }
    }, [apiCall, appId, fetchNotifications]);

    // Mark notification as read
    const markAsRead = useCallback(async (notificationId: string): Promise<boolean> => {
        try {
            setError(null);

            const response = await apiCall<{ success: boolean }>('/api/notifications/mark-read', {
                method: 'POST',
                body: JSON.stringify({
                    appId,
                    userId,
                    notificationId,
                }),
            });

            if (response.success) {
                setNotifications(prev =>
                    prev.map(n =>
                        n.id === notificationId ? { ...n, isRead: true, readAt: new Date() } : n
                    )
                );
                setUnreadCount(prev => Math.max(0, prev - 1));
                return true;
            }

            return false;
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to mark notification as read');
            return false;
        }
    }, [apiCall, appId, userId]);

    // Mark all notifications as read
    const markAllAsRead = useCallback(async (): Promise<boolean> => {
        try {
            setError(null);

            const response = await apiCall<{ success: boolean }>('/api/notifications/mark-all-read', {
                method: 'POST',
                body: JSON.stringify({
                    appId,
                    userId,
                }),
            });

            if (response.success) {
                setNotifications(prev =>
                    prev.map(n => ({ ...n, isRead: true, readAt: new Date() }))
                );
                setUnreadCount(0);
                return true;
            }

            return false;
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to mark all notifications as read');
            return false;
        }
    }, [apiCall, appId, userId]);

    // Delete notification
    const deleteNotification = useCallback(async (notificationId: string): Promise<boolean> => {
        try {
            setError(null);

            const response = await apiCall<{ success: boolean }>(`/api/notifications/${notificationId}`, {
                method: 'DELETE',
                body: JSON.stringify({
                    appId,
                    userId,
                }),
            });

            if (response.success) {
                setNotifications(prev => prev.filter(n => n.id !== notificationId));
                setUnreadCount(prev => {
                    const notification = notifications.find(n => n.id === notificationId);
                    return notification && !notification.isRead ? prev - 1 : prev;
                });
                return true;
            }

            return false;
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to delete notification');
            return false;
        }
    }, [apiCall, appId, userId, notifications]);

    // Refresh notifications
    const refreshNotifications = useCallback(async () => {
        await fetchNotifications();
    }, [fetchNotifications]);

    // Connect to real-time updates
    const connect = useCallback(() => {
        if (eventSourceRef.current) {
            eventSourceRef.current.close();
        }

        try {
            const eventSource = new EventSource(
                `${apiUrl}/api/notifications/stream?appId=${appId}&userId=${userId}`
            );

            eventSource.onopen = () => {
                setIsConnected(true);
                reconnectAttemptsRef.current = 0;
            };

            eventSource.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    if (data.type === 'notification') {
                        setNotifications(prev => {
                            const existingIndex = prev.findIndex(n => n.id === data.notification.id);
                            if (existingIndex >= 0) {
                                const updated = [...prev];
                                updated[existingIndex] = data.notification;
                                return updated;
                            } else {
                                return [data.notification, ...prev];
                            }
                        });

                        if (!data.notification.isRead) {
                            setUnreadCount(prev => prev + 1);
                        }
                    }
                } catch (err) {
                    console.error('Failed to parse notification event:', err);
                }
            };

            eventSource.onerror = () => {
                setIsConnected(false);
                eventSource.close();

                // Attempt to reconnect
                if (reconnectAttemptsRef.current < maxReconnectAttempts) {
                    reconnectAttemptsRef.current++;
                    reconnectTimeoutRef.current = setTimeout(connect, reconnectInterval);
                }
            };

            eventSourceRef.current = eventSource;
        } catch (err) {
            setError('Failed to connect to notification stream');
        }
    }, [apiUrl, appId, userId, reconnectInterval, maxReconnectAttempts]);

    // Disconnect from real-time updates
    const disconnect = useCallback(() => {
        if (eventSourceRef.current) {
            eventSourceRef.current.close();
            eventSourceRef.current = undefined as EventSource | undefined;
        }

        if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
        }

        setIsConnected(false);
    }, []);

    // Clear error
    const clearError = useCallback(() => {
        setError(null);
    }, []);

    // Initial fetch and auto-connect
    useEffect(() => {
        fetchNotifications();

        if (autoConnect) {
            connect();
        }

        return () => {
            disconnect();
        };
    }, [fetchNotifications, autoConnect, connect, disconnect]);

    return {
        notifications,
        unreadCount,
        isLoading,
        error,
        isConnected,
        sendNotification,
        markAsRead,
        markAllAsRead,
        deleteNotification,
        refreshNotifications,
        connect,
        disconnect,
        clearError,
    };
}

/**
 * Hook for managing notification preferences
 */
export function useNotificationPreferences(config: NotificationsConfig): UseNotificationPreferencesReturn {
    const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const { apiUrl, appId, userId } = config;

    // Fetch preferences
    const fetchPreferences = useCallback(async () => {
        try {
            setIsLoading(true);
            setError(null);

            const response = await fetch(
                `${apiUrl}/api/preferences?appId=${appId}&userId=${userId}`
            );

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data) {
                    setPreferences(data.data);
                }
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch preferences');
        } finally {
            setIsLoading(false);
        }
    }, [apiUrl, appId, userId]);

    // Update preferences
    const updatePreferences = useCallback(async (
        updates: Partial<NotificationPreferences>
    ): Promise<boolean> => {
        try {
            setError(null);

            const response = await fetch(`${apiUrl}/api/preferences`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    appId,
                    userId,
                    ...updates,
                }),
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data) {
                    setPreferences(data.data);
                    return true;
                }
            }

            return false;
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to update preferences');
            return false;
        }
    }, [apiUrl, appId, userId]);

    // Load preferences on mount
    useEffect(() => {
        fetchPreferences();
    }, [fetchPreferences]);

    return {
        preferences,
        isLoading,
        error,
        fetchPreferences,
        updatePreferences,
    };
}

/**
 * Hook for managing a single notification
 */
export function useNotification(
    notificationId: string,
    config: NotificationsConfig
): UseNotificationReturn {
    const [notification, setNotification] = useState<BaseNotification | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const { apiUrl, appId, userId } = config;

    // Fetch single notification
    const fetchNotification = useCallback(async () => {
        try {
            setIsLoading(true);
            setError(null);

            const response = await fetch(
                `${apiUrl}/api/notifications/${notificationId}?appId=${appId}&userId=${userId}`
            );

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data) {
                    setNotification(data.data);
                }
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch notification');
        } finally {
            setIsLoading(false);
        }
    }, [apiUrl, appId, userId, notificationId]);

    // Load notification on mount
    useEffect(() => {
        if (notificationId) {
            fetchNotification();
        }
    }, [notificationId, fetchNotification]);

    return {
        notification,
        isLoading,
        error,
        fetchNotification,
    };
}

/**
 * Hook for notification statistics
 */
export function useNotificationStats(config: NotificationsConfig): UseNotificationStatsReturn {
    const [stats, setStats] = useState({
        total: 0,
        unread: 0,
        read: 0,
        byType: {} as Record<NotificationType, number>,
        byChannel: {} as Record<keyof DeliveryChannels, number>,
    });
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const { apiUrl, appId, userId } = config;

    // Fetch statistics
    const fetchStats = useCallback(async () => {
        try {
            setIsLoading(true);
            setError(null);

            const response = await fetch(
                `${apiUrl}/api/notifications/stats?appId=${appId}&userId=${userId}`
            );

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data) {
                    setStats(data.data);
                }
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch statistics');
        } finally {
            setIsLoading(false);
        }
    }, [apiUrl, appId, userId]);

    // Load stats on mount
    useEffect(() => {
        fetchStats();
    }, [fetchStats]);

    return {
        stats,
        isLoading,
        error,
        fetchStats,
    };
}
