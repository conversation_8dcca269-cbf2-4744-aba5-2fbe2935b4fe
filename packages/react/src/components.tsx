import React, { useState, useCallback, useEffect } from 'react';
import {
    BaseNotification,
    NotificationPreferences,
    DeliveryChannels,
    NotificationType,
    NotificationPriority,
    NotificationStatus,
} from '@sparkstrand/notifications-core';
import { useNotifications, useNotificationPreferences } from './hooks';

// Types for component props
export interface NotificationCenterProps {
    config: {
        apiUrl: string;
        appId: string;
        userId: string;
        autoConnect?: boolean;
    };
    className?: string;
    showUnreadCount?: boolean;
    maxNotifications?: number;
    onNotificationClick?: (notification: BaseNotification) => void;
    onMarkAllRead?: () => void;
    onPreferencesChange?: (preferences: NotificationPreferences) => void;
}

export interface NotificationItemProps {
    notification: BaseNotification;
    onMarkAsRead?: (notificationId: string) => void;
    onDelete?: (notificationId: string) => void;
    onClick?: (notification: BaseNotification) => void;
    className?: string;
    showActions?: boolean;
}

export interface NotificationListProps {
    notifications: BaseNotification[];
    onMarkAsRead?: (notificationId: string) => void;
    onDelete?: (notificationId: string) => void;
    onNotificationClick?: (notification: BaseNotification) => void;
    className?: string;
    emptyMessage?: string;
    maxHeight?: string;
}

export interface NotificationBadgeProps {
    count: number;
    className?: string;
    showZero?: boolean;
    maxCount?: number;
}

export interface NotificationPreferencesProps {
    preferences: NotificationPreferences;
    onPreferencesChange: (preferences: NotificationPreferences) => void;
    className?: string;
    showQuietHours?: boolean;
}

// Utility functions
const getPriorityColor = (priority: NotificationPriority): string => {
    switch (priority) {
        case NotificationPriority.URGENT:
            return 'border-red-500 bg-red-50';
        case NotificationPriority.HIGH:
            return 'border-orange-500 bg-orange-50';
        case NotificationPriority.NORMAL:
            return 'border-blue-500 bg-blue-50';
        case NotificationPriority.LOW:
            return 'border-gray-500 bg-gray-50';
        default:
            return 'border-gray-300 bg-white';
    }
};

const getStatusIcon = (status: NotificationStatus): string => {
    switch (status) {
        case NotificationStatus.READ:
            return '✓';
        case NotificationStatus.DELIVERED:
            return '📨';
        case NotificationStatus.FAILED:
            return '❌';
        case NotificationStatus.CANCELLED:
            return '🚫';
        default:
            return '⏳';
    }
};

const getTypeIcon = (type: NotificationType): string => {
    if (type.includes('PAYMENT')) return '💰';
    if (type.includes('EVENT')) return '📅';
    if (type.includes('CHAT')) return '💬';
    if (type.includes('WELCOME')) return '👋';
    if (type.includes('LOGIN')) return '🔐';
    if (type.includes('PASSWORD')) return '🔑';
    return '📢';
};

// NotificationBadge Component
export const NotificationBadge: React.FC<NotificationBadgeProps> = ({
    count,
    className = '',
    showZero = false,
    maxCount = 99,
}) => {
    if (count === 0 && !showZero) return null;

    const displayCount = count > maxCount ? `${maxCount}+` : count.toString();

    return (
        <span
            className={`inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full ${className}`}
        >
            {displayCount}
        </span>
    );
};

// NotificationItem Component
export const NotificationItem: React.FC<NotificationItemProps> = ({
    notification,
    onMarkAsRead,
    onDelete,
    onClick,
    className = '',
    showActions = true,
}) => {
    const handleClick = useCallback(() => {
        if (onClick) {
            onClick(notification);
        }
        if (!notification.isRead && onMarkAsRead) {
            onMarkAsRead(notification.id);
        }
    }, [notification, onClick, onMarkAsRead]);

    const handleMarkAsRead = useCallback((e: React.MouseEvent) => {
        e.stopPropagation();
        if (onMarkAsRead) {
            onMarkAsRead(notification.id);
        }
    }, [notification.id, onMarkAsRead]);

    const handleDelete = useCallback((e: React.MouseEvent) => {
        e.stopPropagation();
        if (onDelete) {
            onDelete(notification.id);
        }
    }, [notification.id, onDelete]);

    const priorityColor = getPriorityColor(notification.priority);
    const statusIcon = getStatusIcon(notification.status);
    const typeIcon = getTypeIcon(notification.type);

    return (
        <div
            className={`p-4 border-l-4 rounded-r-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer ${priorityColor} ${className} ${!notification.isRead ? 'bg-white' : 'bg-gray-50'
                }`}
            onClick={handleClick}
        >
            <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                    <div className="flex-shrink-0 text-lg">{typeIcon}</div>
                    <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                            <h4 className={`text-sm font-medium ${!notification.isRead ? 'text-gray-900' : 'text-gray-700'}`}>
                                {notification.title}
                            </h4>
                            <span className="text-xs text-gray-500">{statusIcon}</span>
                        </div>
                        <p className={`text-sm ${!notification.isRead ? 'text-gray-800' : 'text-gray-600'}`}>
                            {notification.content}
                        </p>
                        {notification.metadata && Object.keys(notification.metadata).length > 0 && (
                            <div className="mt-2 text-xs text-gray-500">
                                {Object.entries(notification.metadata).map(([key, value]) => (
                                    <span key={key} className="mr-3">
                                        {key}: {String(value)}
                                    </span>
                                ))}
                            </div>
                        )}
                        <div className="mt-2 text-xs text-gray-400">
                            {new Date(notification.createdAt).toLocaleString()}
                        </div>
                    </div>
                </div>

                {showActions && (
                    <div className="flex items-center space-x-2 ml-4">
                        {!notification.isRead && (
                            <button
                                onClick={handleMarkAsRead}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                                title="Mark as read"
                            >
                                ✓
                            </button>
                        )}
                        <button
                            onClick={handleDelete}
                            className="text-gray-400 hover:text-red-600 transition-colors"
                            title="Delete notification"
                        >
                            ×
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

// NotificationList Component
export const NotificationList: React.FC<NotificationListProps> = ({
    notifications,
    onMarkAsRead,
    onDelete,
    onNotificationClick,
    className = '',
    emptyMessage = 'No notifications',
    maxHeight = '400px',
}) => {
    if (notifications.length === 0) {
        return (
            <div className={`text-center py-8 text-gray-500 ${className}`}>
                {emptyMessage}
            </div>
        );
    }

    return (
        <div
            className={`overflow-y-auto ${className}`}
            style={{ maxHeight }}
        >
            {notifications.map((notification) => (
                <NotificationItem
                    key={notification.id}
                    notification={notification}
                    onMarkAsRead={onMarkAsRead}
                    onDelete={onDelete}
                    onClick={onNotificationClick}
                    className="mb-3"
                />
            ))}
        </div>
    );
};

// NotificationPreferences Component
export const NotificationPreferences: React.FC<NotificationPreferencesProps> = ({
    preferences,
    onPreferencesChange,
    className = '',
    showQuietHours = true,
}) => {
    const handleChannelChange = useCallback((channel: keyof DeliveryChannels, enabled: boolean) => {
        onPreferencesChange({
            ...preferences,
            channels: {
                ...preferences.channels,
                [channel]: enabled,
            },
        });
    }, [preferences, onPreferencesChange]);

    const handleQuietHoursChange = useCallback((enabled: boolean, startTime?: string, endTime?: string) => {
        onPreferencesChange({
            ...preferences,
            quietHours: {
                ...preferences.quietHours,
                enabled,
                startTime: startTime || preferences.quietHours?.startTime || '22:00',
                endTime: endTime || preferences.quietHours?.endTime || '08:00',
            },
        });
    }, [preferences, onPreferencesChange]);

    const channels: Array<{ key: keyof DeliveryChannels; label: string; icon: string }> = [
        { key: 'inApp', label: 'In-App', icon: '📱' },
        { key: 'email', label: 'Email', icon: '📧' },
        { key: 'sms', label: 'SMS', icon: '📱' },
        { key: 'push', label: 'Push', icon: '🔔' },
        { key: 'webhook', label: 'Webhook', icon: '🔗' },
        { key: 'slack', label: 'Slack', icon: '💬' },
        { key: 'discord', label: 'Discord', icon: '🎮' },
        { key: 'telegram', label: 'Telegram', icon: '✈️' },
    ];

    return (
        <div className={`space-y-6 ${className}`}>
            <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Channels</h3>
                <div className="grid grid-cols-2 gap-4">
                    {channels.map(({ key, label, icon }) => (
                        <label key={key} className="flex items-center space-x-3 cursor-pointer">
                            <input
                                type="checkbox"
                                checked={preferences.channels[key]}
                                onChange={(e) => handleChannelChange(key, e.target.checked)}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="text-sm font-medium text-gray-700">
                                {icon} {label}
                            </span>
                        </label>
                    ))}
                </div>
            </div>

            {showQuietHours && (
                <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Quiet Hours</h3>
                    <div className="space-y-4">
                        <label className="flex items-center space-x-3 cursor-pointer">
                            <input
                                type="checkbox"
                                checked={preferences.quietHours?.enabled || false}
                                onChange={(e) => handleQuietHoursChange(e.target.checked)}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="text-sm font-medium text-gray-700">Enable quiet hours</span>
                        </label>

                        {preferences.quietHours?.enabled && (
                            <div className="grid grid-cols-2 gap-4 ml-7">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Start Time
                                    </label>
                                    <input
                                        type="time"
                                        value={preferences.quietHours?.startTime || '22:00'}
                                        onChange={(e) => handleQuietHoursChange(true, e.target.value)}
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        End Time
                                    </label>
                                    <input
                                        type="time"
                                        value={preferences.quietHours?.endTime || '08:00'}
                                        onChange={(e) => handleQuietHoursChange(true, undefined, e.target.value)}
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    />
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

// NotificationCenter Component
export const NotificationCenter: React.FC<NotificationCenterProps> = ({
    config,
    className = '',
    showUnreadCount = true,
    maxNotifications = 10,
    onNotificationClick,
    onMarkAllRead,
    onPreferencesChange,
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [showPreferences, setShowPreferences] = useState(false);

    const {
        notifications,
        unreadCount,
        isLoading,
        error,
        isConnected,
        markAllAsRead,
        deleteNotification,
        refreshNotifications,
        connect,
        disconnect,
        clearError,
    } = useNotifications(config);

    const {
        preferences,
        updatePreferences,
        isLoading: preferencesLoading,
        error: preferencesError,
    } = useNotificationPreferences(config);

    const handleMarkAllRead = useCallback(async () => {
        const success = await markAllAsRead();
        if (success && onMarkAllRead) {
            onMarkAllRead();
        }
    }, [markAllAsRead, onMarkAllRead]);

    const handleNotificationClick = useCallback((notification: BaseNotification) => {
        if (onNotificationClick) {
            onNotificationClick(notification);
        }
    }, [onNotificationClick]);

    const handlePreferencesChange = useCallback((newPreferences: NotificationPreferences) => {
        updatePreferences(newPreferences);
        if (onPreferencesChange) {
            onPreferencesChange(newPreferences);
        }
    }, [updatePreferences, onPreferencesChange]);

    const toggleOpen = useCallback(() => {
        setIsOpen(!isOpen);
        if (!isOpen) {
            refreshNotifications();
        }
    }, [isOpen, refreshNotifications]);

    const togglePreferences = useCallback(() => {
        setShowPreferences(!showPreferences);
    }, [showPreferences]);

    useEffect(() => {
        if (isOpen) {
            connect();
        } else {
            disconnect();
        }
    }, [isOpen, connect, disconnect]);

    const displayedNotifications = notifications.slice(0, maxNotifications);

    return (
        <div className={`relative ${className}`}>
            {/* Notification Bell Button */}
            <button
                onClick={toggleOpen}
                className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md transition-colors"
                aria-label="Open notifications"
            >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>

                {showUnreadCount && unreadCount > 0 && (
                    <NotificationBadge
                        count={unreadCount}
                        className="absolute -top-1 -right-1"
                    />
                )}
            </button>

            {/* Notification Panel */}
            {isOpen && (
                <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                    {/* Header */}
                    <div className="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
                        <h3 className="text-lg font-medium text-gray-900">Notifications</h3>
                        <div className="flex items-center space-x-2">
                            <button
                                onClick={togglePreferences}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                                title="Preferences"
                            >
                                ⚙️
                            </button>
                            <button
                                onClick={toggleOpen}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                                title="Close"
                            >
                                ×
                            </button>
                        </div>
                    </div>

                    {/* Connection Status */}
                    {error && (
                        <div className="px-4 py-2 bg-red-50 border-b border-red-200">
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-red-600">{error}</span>
                                <button
                                    onClick={clearError}
                                    className="text-red-400 hover:text-red-600"
                                >
                                    ×
                                </button>
                            </div>
                        </div>
                    )}

                    {/* Content */}
                    <div className="max-h-96 overflow-hidden">
                        {showPreferences ? (
                            <div className="p-4">
                                <h4 className="text-md font-medium text-gray-900 mb-4">Notification Settings</h4>
                                {preferencesLoading ? (
                                    <div className="text-center py-4 text-gray-500">Loading preferences...</div>
                                ) : preferencesError ? (
                                    <div className="text-center py-4 text-red-500">Error loading preferences</div>
                                ) : preferences ? (
                                    <NotificationPreferences
                                        preferences={preferences}
                                        onPreferencesChange={handlePreferencesChange}
                                    />
                                ) : (
                                    <div className="text-center py-4 text-gray-500">No preferences found</div>
                                )}
                            </div>
                        ) : (
                            <div className="p-4">
                                {isLoading ? (
                                    <div className="text-center py-8 text-gray-500">Loading notifications...</div>
                                ) : (
                                    <>
                                        {unreadCount > 0 && (
                                            <div className="mb-4 flex items-center justify-between">
                                                <span className="text-sm text-gray-600">
                                                    {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
                                                </span>
                                                <button
                                                    onClick={handleMarkAllRead}
                                                    className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
                                                >
                                                    Mark all as read
                                                </button>
                                            </div>
                                        )}

                                        <NotificationList
                                            notifications={displayedNotifications}
                                            onMarkAsRead={markAllAsRead}
                                            onDelete={deleteNotification}
                                            onNotificationClick={handleNotificationClick}
                                            emptyMessage="No notifications yet"
                                        />

                                        {notifications.length > maxNotifications && (
                                            <div className="mt-4 text-center">
                                                <button
                                                    onClick={refreshNotifications}
                                                    className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
                                                >
                                                    Load more notifications
                                                </button>
                                            </div>
                                        )}
                                    </>
                                )}
                            </div>
                        )}
                    </div>

                    {/* Footer */}
                    <div className="px-4 py-2 border-t border-gray-200 bg-gray-50 rounded-b-lg">
                        <div className="flex items-center justify-between text-xs text-gray-500">
                            <span>
                                {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
                            </span>
                            <span>
                                {notifications.length} total
                            </span>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

// Components are already exported individually above
