// Re-export core types
export * from '@sparkstrand/notifications-core';

// React specific types
export interface NotificationsConfig {
    apiUrl: string;
    appId: string;
    userId: string;
    autoConnect?: boolean;
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
}

// Hook return interfaces
export interface UseNotificationsReturn {
    // State
    notifications: any[];
    unreadCount: number;
    isLoading: boolean;
    error: string | null;
    isConnected: boolean;

    // Actions
    sendNotification: (notification: any) => Promise<boolean>;
    markAsRead: (notificationId: string) => Promise<boolean>;
    markAllAsRead: () => Promise<boolean>;
    deleteNotification: (notificationId: string) => Promise<boolean>;
    refreshNotifications: () => Promise<void>;

    // Real-time
    connect: () => void;
    disconnect: () => void;

    // Utilities
    clearError: () => void;
}

export interface UseNotificationPreferencesReturn {
    preferences: any;
    isLoading: boolean;
    error: string | null;
    fetchPreferences: () => Promise<void>;
    updatePreferences: (updates: any) => Promise<boolean>;
}

export interface UseNotificationReturn {
    notification: any;
    isLoading: boolean;
    error: string | null;
    fetchNotification: () => Promise<void>;
}

export interface UseNotificationStatsReturn {
    stats: {
        total: number;
        unread: number;
        read: number;
        byType: Record<string, number>;
        byChannel: Record<string, number>;
    };
    isLoading: boolean;
    error: string | null;
    fetchStats: () => Promise<void>;
}

// Component prop interfaces
export interface NotificationCenterProps {
    config: NotificationsConfig;
    style?: any;
    showUnreadCount?: boolean;
    maxNotifications?: number;
    onNotificationPress?: (notification: any) => void;
    onMarkAllRead?: () => void;
    onPreferencesChange?: (preferences: any) => void;
}

export interface NotificationItemProps {
    notification: any;
    onMarkAsRead?: (notificationId: string) => void;
    onDelete?: (notificationId: string) => void;
    onPress?: (notification: any) => void;
    style?: any;
    showActions?: boolean;
}

export interface NotificationListProps {
    notifications: any[];
    onMarkAsRead?: (notificationId: string) => void;
    onDelete?: (notificationId: string) => void;
    onNotificationPress?: (notification: any) => void;
    style?: any;
    emptyMessage?: string;
    maxHeight?: number;
}

export interface NotificationBadgeProps {
    count: number;
    style?: any;
    showZero?: boolean;
    maxCount?: number;
}

export interface NotificationPreferencesProps {
    preferences: any;
    onPreferencesChange: (preferences: any) => void;
    style?: any;
    showQuietHours?: boolean;
}
