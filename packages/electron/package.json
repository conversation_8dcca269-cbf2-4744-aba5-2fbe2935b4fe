{"name": "@sparkstrand/notifications-electron", "version": "0.0.0", "description": "Electron integration for the Spark Strand notifications system", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint . --max-warnings 0", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@sparkstrand/notifications-core": "workspace:*"}, "peerDependencies": {"electron": ">=20.0.0"}, "devDependencies": {"@types/node": "^22.10.7", "eslint": "^9", "tsup": "^8.3.5", "typescript": "^5.7.3"}, "files": ["dist/**"], "publishConfig": {"access": "restricted"}, "repository": {"type": "git", "url": "https://github.com/sparkstrand/notifications.git", "directory": "packages/electron"}, "keywords": ["notifications", "electron", "desktop"], "license": "MIT"}