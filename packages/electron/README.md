# @sparkstrand/notifications-electron

Electron integration for the Spark Strand notification system, designed for desktop applications.

## 🚀 **Quick Start**

```typescript
import { 
    setupNotifications,
    showSystemNotification,
    useNotifications 
} from '@sparkstrand/notifications-electron';

// In your main process
setupNotifications({
    appName: 'Your Desktop App',
    enableSystemNotifications: true
});

// In your renderer process
function MyComponent() {
    const { notifications, sendNotification } = useNotifications();

    const handleShowNotification = () => {
        showSystemNotification({
            title: 'Event Reminder',
            body: 'Don\'t forget your event tomorrow!',
            icon: './assets/icon.png'
        });
    };

    return (
        <div>
            <p>Notifications: {notifications.length}</p>
            <button onClick={handleShowNotification}>Show Notification</button>
        </div>
    );
}
```

## 🖥️ **Features**

- **System Notifications**: Native desktop notifications
- **In-App Notifications**: Real-time notification display
- **Cross-Platform**: Works on Windows, macOS, and Linux
- **Notification Actions**: Clickable notifications with actions
- **Badge Support**: Unread notification counters
- **Tray Integration**: System tray notification support

## 🔧 **Installation**

```bash
npm install @sparkstrand/notifications-electron
```

## 🎯 **Core Functions**

### **setupNotifications (Main Process)**

Initialize the notification system in the main process:

```typescript
import { setupNotifications } from '@sparkstrand/notifications-electron';

// In main.ts
setupNotifications({
    appName: 'Your Desktop App',
    enableSystemNotifications: true,
    enableTrayNotifications: true,
    defaultIcon: './assets/icon.png'
});
```

### **showSystemNotification (Main Process)**

Display a system notification:

```typescript
import { showSystemNotification } from '@sparkstrand/notifications-electron';

// In main.ts
showSystemNotification({
    title: 'Event Reminder',
    body: 'Don\'t forget your event tomorrow!',
    icon: './assets/icon.png',
    actions: [
        { text: 'View', action: 'view' },
        { text: 'Dismiss', action: 'dismiss' }
    ],
    onClick: () => {
        // Handle notification click
        mainWindow.show();
        mainWindow.focus();
    }
});
```

### **useNotifications (Renderer Process)**

Hook for managing notifications in renderer process:

```typescript
import { useNotifications } from '@sparkstrand/notifications-electron';

function MyComponent() {
    const {
        notifications,
        sendNotification,
        markAsRead,
        markAllAsRead,
        deleteNotification,
        isLoading,
        error
    } = useNotifications();

    const handleSendNotification = async () => {
        await sendNotification({
            type: 'EVENT_REMINDER',
            title: 'Event Tomorrow!',
            content: 'Don\'t forget your event',
            metadata: { userId: 'user-123' }
        });
    };

    return (
        <div>
            <p>Notifications: {notifications.length}</p>
            <button onClick={handleSendNotification}>Send Test</button>
        </div>
    );
}
```

## 🔔 **System Notifications**

### **Basic Notification**

```typescript
import { showSystemNotification } from '@sparkstrand/notifications-electron';

showSystemNotification({
    title: 'Hello World',
    body: 'This is a test notification'
});
```

### **Notification with Actions**

```typescript
showSystemNotification({
    title: 'New Message',
    body: 'You have a new message from John',
    actions: [
        { text: 'Reply', action: 'reply' },
        { text: 'Mark as Read', action: 'mark-read' }
    ],
    onClick: () => {
        // Handle notification click
    },
    onAction: (action) => {
        switch (action) {
            case 'reply':
                // Handle reply action
                break;
            case 'mark-read':
                // Handle mark as read action
                break;
        }
    }
});
```

### **Notification with Custom Icon**

```typescript
showSystemNotification({
    title: 'Payment Successful',
    body: 'Your payment has been processed',
    icon: './assets/success-icon.png',
    silent: false,
    sound: './assets/notification-sound.wav'
});
```

## 🎨 **Customization**

### **Notification Styling**

```typescript
// Custom notification appearance
showSystemNotification({
    title: 'Custom Notification',
    body: 'This notification has custom styling',
    icon: './assets/custom-icon.png',
    badge: './assets/badge.png',
    tag: 'unique-notification-id',
    requireInteraction: true,
    silent: false
});
```

### **Notification Actions**

```typescript
showSystemNotification({
    title: 'Meeting Reminder',
    body: 'Team meeting in 5 minutes',
    actions: [
        { text: 'Join Now', action: 'join' },
        { text: 'Snooze 5 min', action: 'snooze' },
        { text: 'Cancel', action: 'cancel' }
    ],
    onAction: (action) => {
        switch (action) {
            case 'join':
                // Open meeting link
                break;
            case 'snooze':
                // Snooze for 5 minutes
                break;
            case 'cancel':
                // Cancel the meeting
                break;
        }
    }
});
```

## 🔧 **Configuration**

### **Environment Variables**

```bash
# Your app's notification service configuration
NOTIFICATION_API_URL=https://your-api.com
```

### **App Configuration**

```typescript
// In your app initialization
import { configureNotifications } from '@sparkstrand/notifications-electron';

configureNotifications({
    apiUrl: 'https://your-api.com',
    appId: 'your-app-id',
    enableSystemNotifications: true,
    enableTrayNotifications: true
});
```

## 📱 **Platform-Specific Features**

### **Windows**

- Toast notifications
- Action center integration
- Custom notification sounds
- Badge support

### **macOS**

- Native notifications
- Notification center
- Do not disturb mode
- Custom notification actions

### **Linux**

- Desktop notifications
- System tray integration
- Custom notification daemons
- Sound support

## 🧪 **Testing**

### **Test Notifications**

```typescript
import { testNotification } from '@sparkstrand/notifications-electron';

// Send a test notification
await testNotification({
    type: 'TEST',
    title: 'Test Notification',
    content: 'This is a test notification'
});
```

### **Mock Mode**

```typescript
import { enableMockMode } from '@sparkstrand/notifications-electron';

// Enable mock mode for testing
enableMockMode();
```

## 🚀 **Integration with Core System**

This package integrates seamlessly with the `@sparkstrand/notifications-core` package:

```typescript
// In your backend
import { notificationService } from '@sparkstrand/notifications-core';

// Send notification to desktop app
await notificationService.sendNotification(
    'user-123',
    'EVENT_REMINDER',
    'Event Tomorrow!',
    'Don\'t forget your event',
    { userId: 'user-123' },
    { channels: { inApp: true } }
);
```

## 📚 **Additional Resources**

- **Core Package**: [@sparkstrand/notifications-core](../core/README.md)
- **API Documentation**: [API Reference](../core/README.md)
- **Examples**: [Usage Examples](../core/examples/)

---

**Built for Electron desktop applications with comprehensive notification support and seamless integration with the Spark Strand notification system.** 