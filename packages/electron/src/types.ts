import {
    BaseNotification,
    NotificationPreferences,
    DeliveryChannels,
    NotificationType,
    NotificationPriority,
    NotificationStatus,
    CreateNotificationRequest,
    UpdateNotificationRequest,
} from '@sparkstrand/notifications-core';

// Electron-specific configuration
export interface ElectronNotificationsConfig {
    apiUrl: string;
    appId: string;
    userId?: string;
    enableSystemNotifications?: boolean;
    enableTrayNotifications?: boolean;
    enableSound?: boolean;
    defaultSound?: string;
    notificationTimeout?: number;
    maxNotifications?: number;
    enableQuietHours?: boolean;
    quietHoursStart?: string; // HH:mm format
    quietHoursEnd?: string; // HH:mm format
}

// Main process notification manager
export interface MainProcessNotificationManager {
    initialize(): Promise<void>;
    showNotification(notification: BaseNotification): Promise<void>;
    hideNotification(notificationId: string): Promise<void>;
    updateNotification(notification: BaseNotification): Promise<void>;
    clearAllNotifications(): Promise<void>;
    setPreferences(preferences: NotificationPreferences): Promise<void>;
    getPreferences(): Promise<NotificationPreferences>;
    enableSystemNotifications(enabled: boolean): Promise<void>;
    enableTrayNotifications(enabled: boolean): Promise<void>;
    enableSound(enabled: boolean): Promise<void>;
    setSound(soundFile: string): Promise<void>;
    setQuietHours(start: string, end: string): Promise<void>;
    enableQuietHours(enabled: boolean): Promise<void>;
}

// Renderer process notification manager
export interface RendererProcessNotificationManager {
    initialize(): Promise<void>;
    showNotification(notification: BaseNotification): Promise<void>;
    hideNotification(notificationId: string): Promise<void>;
    updateNotification(notification: BaseNotification): Promise<void>;
    clearAllNotifications(): Promise<void>;
    setPreferences(preferences: NotificationPreferences): Promise<void>;
    getPreferences(): Promise<NotificationPreferences>;
    onNotificationClick(callback: (notification: BaseNotification) => void): void;
    onNotificationClose(callback: (notificationId: string) => void): void;
    onPreferencesChange(callback: (preferences: NotificationPreferences) => void): void;
}

// System notification options
export interface SystemNotificationOptions {
    title: string;
    body: string;
    icon?: string;
    silent?: boolean;
    timeout?: number;
    actions?: Array<{
        type: 'button';
        text: string;
        action: string;
    }>;
    onClick?: () => void;
    onClose?: () => void;
    onAction?: (action: string) => void;
}

// Tray notification options
export interface TrayNotificationOptions {
    title: string;
    body: string;
    icon?: string;
    timeout?: number;
    onClick?: () => void;
    onClose?: () => void;
}

// Sound notification options
export interface SoundNotificationOptions {
    file: string;
    volume?: number;
    loop?: boolean;
    timeout?: number;
}

// IPC message types
export interface IPCNotificationMessage {
    type: 'show' | 'hide' | 'update' | 'clear' | 'preferences';
    data: any;
}

export interface IPCNotificationResponse {
    success: boolean;
    data?: any;
    error?: string;
}

// Notification window options
export interface NotificationWindowOptions {
    width: number;
    height: number;
    x?: number;
    y?: number;
    alwaysOnTop?: boolean;
    skipTaskbar?: boolean;
    frame?: boolean;
    transparent?: boolean;
    resizable?: boolean;
    minimizable?: boolean;
    maximizable?: boolean;
    closable?: boolean;
    show?: boolean;
}

// Notification badge options
export interface NotificationBadgeOptions {
    text: string;
    backgroundColor?: string;
    textColor?: string;
    show?: boolean;
}

// Export core types for convenience
export type {
    BaseNotification,
    NotificationPreferences,
    DeliveryChannels,
    NotificationType,
    NotificationPriority,
    NotificationStatus,
    CreateNotificationRequest,
    UpdateNotificationRequest,
};
