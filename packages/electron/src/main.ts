import { ipc<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tray, Menu, nativeImage, app, shell } from 'electron';
import { join } from 'path';
import {
    MainProcessNotificationManager,
    ElectronNotificationsConfig,
    SystemNotificationOptions,
    TrayNotificationOptions,
    SoundNotificationOptions,
    IPCNotificationMessage,
    IPCNotificationResponse,
    BaseNotification,
    NotificationPreferences,
} from './types';

export class ElectronMainProcessNotificationManager implements MainProcessNotificationManager {
    private config: ElectronNotificationsConfig;
    private tray: Tray | null = null;
    private notificationWindows: Map<string, BrowserWindow> = new Map();
    private activeNotifications: Map<string, BaseNotification> = new Map();
    private preferences: NotificationPreferences | null = null;
    private isInitialized = false;

    constructor(config: ElectronNotificationsConfig) {
        this.config = {
            enableSystemNotifications: true,
            enableTrayNotifications: true,
            enableSound: true,
            defaultSound: 'default',
            notificationTimeout: 5000,
            maxNotifications: 10,
            enableQuietHours: false,
            quietHoursStart: '22:00',
            quietHoursEnd: '08:00',
            ...config,
        };
    }

    async initialize(): Promise<void> {
        if (this.isInitialized) return;

        try {
            // Setup IPC handlers
            this.setupIPCHandlers();

            // Initialize tray if enabled
            if (this.config.enableTrayNotifications) {
                await this.initializeTray();
            }

            // Load preferences
            await this.loadPreferences();

            this.isInitialized = true;
            console.log('Electron main process notification manager initialized');
        } catch (error) {
            console.error('Failed to initialize Electron notification manager:', error);
            throw error;
        }
    }

    private setupIPCHandlers(): void {
        ipcMain.handle('notifications:show', async (_, notification: BaseNotification) => {
            try {
                await this.showNotification(notification);
                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('notifications:hide', async (_, notificationId: string) => {
            try {
                await this.hideNotification(notificationId);
                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('notifications:update', async (_, notification: BaseNotification) => {
            try {
                await this.updateNotification(notification);
                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('notifications:clear', async () => {
            try {
                await this.clearAllNotifications();
                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('notifications:preferences:get', async () => {
            try {
                const preferences = await this.getPreferences();
                return { success: true, data: preferences };
            } catch (error) {
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('notifications:preferences:set', async (_, preferences: NotificationPreferences) => {
            try {
                await this.setPreferences(preferences);
                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        });
    }

    private async initializeTray(): Promise<void> {
        try {
            const iconPath = join(__dirname, 'assets', 'tray-icon.png');
            const icon = nativeImage.createFromPath(iconPath);

            this.tray = new Tray(icon);
            this.tray.setToolTip('Notifications');

            // Create tray menu
            const contextMenu = Menu.buildFromTemplate([
                {
                    label: 'Show Notifications',
                    click: () => this.showNotificationCenter(),
                },
                {
                    label: 'Preferences',
                    click: () => this.showPreferences(),
                },
                { type: 'separator' },
                {
                    label: 'Quit',
                    click: () => app.quit(),
                },
            ]);

            this.tray.setContextMenu(contextMenu);
            this.tray.on('click', () => this.showNotificationCenter());
        } catch (error) {
            console.error('Failed to initialize tray:', error);
        }
    }

    async showNotification(notification: BaseNotification): Promise<void> {
        if (this.isQuietHoursActive()) {
            console.log('Quiet hours active, skipping notification:', notification.id);
            return;
        }

        // Store notification
        this.activeNotifications.set(notification.id, notification);

        // Limit active notifications
        if (this.activeNotifications.size > this.config.maxNotifications!) {
            const oldestId = this.activeNotifications.keys().next().value;
            await this.hideNotification(oldestId);
        }

        // Show system notification if enabled
        if (this.config.enableSystemNotifications) {
            await this.showSystemNotification(notification);
        }

        // Show tray notification if enabled
        if (this.config.enableTrayNotifications && this.tray) {
            await this.showTrayNotification(notification);
        }

        // Play sound if enabled
        if (this.config.enableSound) {
            await this.playNotificationSound(notification);
        }

        // Create notification window
        await this.createNotificationWindow(notification);

        // Auto-hide after timeout
        setTimeout(() => {
            this.hideNotification(notification.id);
        }, this.config.notificationTimeout);
    }

    async hideNotification(notificationId: string): Promise<void> {
        // Remove from active notifications
        this.activeNotifications.delete(notificationId);

        // Close notification window
        const window = this.notificationWindows.get(notificationId);
        if (window) {
            window.close();
            this.notificationWindows.delete(notificationId);
        }

        // Update tray badge
        this.updateTrayBadge();
    }

    async updateNotification(notification: BaseNotification): Promise<void> {
        // Update stored notification
        this.activeNotifications.set(notification.id, notification);

        // Update notification window
        const window = this.notificationWindows.get(notification.id);
        if (window) {
            window.webContents.send('notification:update', notification);
        }

        // Update tray if needed
        if (this.tray) {
            this.updateTrayBadge();
        }
    }

    async clearAllNotifications(): Promise<void> {
        // Clear all active notifications
        for (const notificationId of this.activeNotifications.keys()) {
            await this.hideNotification(notificationId);
        }
    }

    async setPreferences(preferences: NotificationPreferences): Promise<void> {
        this.preferences = preferences;

        // Update configuration based on preferences
        this.config.enableSystemNotifications = preferences.channels.email?.enabled ?? true;
        this.config.enableTrayNotifications = preferences.channels.inApp?.enabled ?? true;
        this.config.enableSound = preferences.channels.push?.enabled ?? true;
        this.config.enableQuietHours = preferences.quietHours?.enabled ?? false;

        if (preferences.quietHours?.startTime && preferences.quietHours?.endTime) {
            this.config.quietHoursStart = preferences.quietHours.startTime;
            this.config.quietHoursEnd = preferences.quietHours.endTime;
        }

        // Save preferences to storage
        await this.savePreferences();
    }

    async getPreferences(): Promise<NotificationPreferences> {
        if (!this.preferences) {
            await this.loadPreferences();
        }
        return this.preferences!;
    }

    async enableSystemNotifications(enabled: boolean): Promise<void> {
        this.config.enableSystemNotifications = enabled;
    }

    async enableTrayNotifications(enabled: boolean): Promise<void> {
        this.config.enableTrayNotifications = enabled;
        if (enabled && !this.tray) {
            await this.initializeTray();
        } else if (!enabled && this.tray) {
            this.tray.destroy();
            this.tray = null;
        }
    }

    async enableSound(enabled: boolean): Promise<void> {
        this.config.enableSound = enabled;
    }

    async setSound(soundFile: string): Promise<void> {
        this.config.defaultSound = soundFile;
    }

    async setQuietHours(start: string, end: string): Promise<void> {
        this.config.quietHoursStart = start;
        this.config.quietHoursEnd = end;
    }

    async enableQuietHours(enabled: boolean): Promise<void> {
        this.config.enableQuietHours = enabled;
    }

    private async showSystemNotification(notification: BaseNotification): Promise<void> {
        try {
            // Cross-platform system notification implementation
            const { Notification } = require('electron');

            if (!Notification.isSupported()) {
                console.log('System notifications not supported on this platform');
                return;
            }

            const systemNotification = new Notification({
                title: notification.title,
                body: notification.message,
                icon: this.config.defaultIcon || undefined,
                silent: this.config.enableSounds,
                timeoutType: 'default',
                actions: [
                    {
                        type: 'button',
                        text: 'View',
                    },
                    {
                        type: 'button',
                        text: 'Dismiss',
                    }
                ]
            });

            // Handle notification actions
            systemNotification.on('action', (event: any, index: number) => {
                if (index === 0) {
                    // View action - show notification details
                    this.onNotificationClick(notification);
                }
                // Dismiss action - do nothing, notification will close
            });

            // Handle notification click
            systemNotification.on('click', () => {
                this.onNotificationClick(notification);
            });

            // Show the notification
            systemNotification.show();

            console.log('System notification displayed:', notification.title);
        } catch (error) {
            console.error('Failed to show system notification:', error);
            // Fallback to tray notification
            await this.showTrayNotification(notification);
        }
    }

    private async showTrayNotification(notification: BaseNotification): Promise<void> {
        if (!this.tray) return;

        const options: TrayNotificationOptions = {
            title: notification.title,
            body: notification.message,
            timeout: this.config.notificationTimeout,
            onClick: () => this.onNotificationClick(notification),
        };

        // Show tray notification (platform-specific implementation)
        this.tray.displayBalloon(options);
    }

    private async playNotificationSound(notification: BaseNotification): Promise<void> {
        const options: SoundNotificationOptions = {
            file: this.config.defaultSound!,
            volume: 0.5,
            loop: false,
            timeout: 2000,
        };

        // Play sound (platform-specific implementation)
        console.log('Playing notification sound:', options.file);
    }

    private async createNotificationWindow(notification: BaseNotification): Promise<void> {
        const window = new BrowserWindow({
            width: 350,
            height: 120,
            x: undefined,
            y: undefined,
            alwaysOnTop: true,
            skipTaskbar: true,
            frame: false,
            transparent: true,
            resizable: false,
            minimizable: false,
            maximizable: false,
            closable: true,
            show: false,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: join(__dirname, 'preload.js'),
            },
        });

        // Position window (top-right corner)
        const { screen } = require('electron');
        const primaryDisplay = screen.getPrimaryDisplay();
        const { width, height } = primaryDisplay.workAreaSize;

        window.setPosition(width - 370, 20);

        // Load notification HTML
        await window.loadFile(join(__dirname, 'notification.html'));

        // Send notification data
        window.webContents.on('did-finish-load', () => {
            window.webContents.send('notification:data', notification);
            window.show();
        });

        // Store window reference
        this.notificationWindows.set(notification.id, window);

        // Handle window close
        window.on('closed', () => {
            this.notificationWindows.delete(notification.id);
        });
    }

    private updateTrayBadge(): void {
        if (!this.tray) return;

        const count = this.activeNotifications.size;
        if (count > 0) {
            this.tray.setToolTip(`${count} notification${count > 1 ? 's' : ''}`);
        } else {
            this.tray.setToolTip('Notifications');
        }
    }

    private isQuietHoursActive(): boolean {
        if (!this.config.enableQuietHours) return false;

        const now = new Date();
        const currentTime = now.getHours() * 60 + now.getMinutes();

        const [startHour, startMinute] = this.config.quietHoursStart!.split(':').map(Number);
        const [endHour, endMinute] = this.config.quietHoursEnd!.split(':').map(Number);

        const startTime = startHour * 60 + startMinute;
        const endTime = endHour * 60 + endMinute;

        if (startTime <= endTime) {
            return currentTime >= startTime && currentTime <= endTime;
        } else {
            // Overnight quiet hours
            return currentTime >= startTime || currentTime <= endTime;
        }
    }

    private onNotificationClick(notification: BaseNotification): void {
        // Handle notification click (e.g., focus app, open specific window)
        console.log('Notification clicked:', notification.id);
    }

    private showNotificationCenter(): void {
        // Show main notification center window
        console.log('Showing notification center');
    }

    private showPreferences(): void {
        // Show preferences window
        console.log('Showing preferences');
    }

    private async loadPreferences(): Promise<void> {
        // Load preferences from storage (e.g., electron-store)
        this.preferences = {
            channels: {
                email: { enabled: true, priority: 'NORMAL' },
                sms: { enabled: false, priority: 'HIGH' },
                push: { enabled: true, priority: 'NORMAL' },
                inApp: { enabled: true, priority: 'NORMAL' },
                webhook: { enabled: false, priority: 'LOW' },
                slack: { enabled: false, priority: 'NORMAL' },
                discord: { enabled: false, priority: 'NORMAL' },
                telegram: { enabled: false, priority: 'NORMAL' },
            },
            quietHours: {
                enabled: false,
                startTime: '22:00',
                endTime: '08:00',
            },
            global: {
                enabled: true,
                priority: 'NORMAL',
            },
        };
    }

    private async savePreferences(): Promise<void> {
        // Save preferences to storage (e.g., electron-store)
        console.log('Saving preferences:', this.preferences);
    }
}
