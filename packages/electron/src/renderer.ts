import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import {
    RendererProcessNotificationManager,
    ElectronNotificationsConfig,
    BaseNotification,
    NotificationPreferences,
    CreateNotificationRequest,
    UpdateNotificationRequest,
} from './types';

export class ElectronRendererProcessNotificationManager implements RendererProcessNotificationManager {
    private config: ElectronNotificationsConfig;
    private isInitialized = false;
    private notificationClickCallbacks: Array<(notification: BaseNotification) => void> = [];
    private notificationCloseCallbacks: Array<(notificationId: string) => void> = [];
    private preferencesChangeCallbacks: Array<(preferences: NotificationPreferences) => void> = [];
    private notificationCache: Map<string, BaseNotification> = new Map();

    constructor(config: ElectronNotificationsConfig) {
        this.config = config;
    }

    async initialize(): Promise<void> {
        if (this.isInitialized) return;

        try {
            // Setup IPC listeners
            this.setupIPCListeners();

            // Test connection to main process
            const response = await ipcRenderer.invoke('notifications:preferences:get');
            if (!response.success) {
                throw new Error('Failed to connect to main process');
            }

            this.isInitialized = true;
            console.log('Electron renderer process notification manager initialized');
        } catch (error) {
            console.error('Failed to initialize Electron renderer notification manager:', error);
            throw error;
        }
    }

    private setupIPCListeners(): void {
        // Listen for notification updates from main process
        ipcRenderer.on('notification:data', (_, notification: BaseNotification) => {
            this.handleNotificationData(notification);
        });

        ipcRenderer.on('notification:update', (_, notification: BaseNotification) => {
            this.handleNotificationUpdate(notification);
        });

        // Listen for preferences changes
        ipcRenderer.on('preferences:changed', (_, preferences: NotificationPreferences) => {
            this.handlePreferencesChange(preferences);
        });
    }

    async showNotification(notification: BaseNotification): Promise<void> {
        if (!this.isInitialized) {
            throw new Error('Notification manager not initialized');
        }

        const response = await ipcRenderer.invoke('notifications:show', notification);
        if (!response.success) {
            throw new Error(response.error || 'Failed to show notification');
        }
    }

    async hideNotification(notificationId: string): Promise<void> {
        if (!this.isInitialized) {
            throw new Error('Notification manager not initialized');
        }

        const response = await ipcRenderer.invoke('notifications:hide', notificationId);
        if (!response.success) {
            throw new Error(response.error || 'Failed to hide notification');
        }
    }

    async updateNotification(notification: BaseNotification): Promise<void> {
        if (!this.isInitialized) {
            throw new Error('Notification manager not initialized');
        }

        const response = await ipcRenderer.invoke('notifications:update', notification);
        if (!response.success) {
            throw new Error(response.error || 'Failed to update notification');
        }
    }

    async clearAllNotifications(): Promise<void> {
        if (!this.isInitialized) {
            throw new Error('Notification manager not initialized');
        }

        const response = await ipcRenderer.invoke('notifications:clear');
        if (!response.success) {
            throw new Error(response.error || 'Failed to clear notifications');
        }
    }

    async setPreferences(preferences: NotificationPreferences): Promise<void> {
        if (!this.isInitialized) {
            throw new Error('Notification manager not initialized');
        }

        const response = await ipcRenderer.invoke('notifications:preferences:set', preferences);
        if (!response.success) {
            throw new Error(response.error || 'Failed to set preferences');
        }
    }

    async getPreferences(): Promise<NotificationPreferences> {
        if (!this.isInitialized) {
            throw new Error('Notification manager not initialized');
        }

        const response = await ipcRenderer.invoke('notifications:preferences:get');
        if (!response.success) {
            throw new Error(response.error || 'Failed to get preferences');
        }

        return response.data;
    }

    onNotificationClick(callback: (notification: BaseNotification) => void): void {
        this.notificationClickCallbacks.push(callback);
    }

    onNotificationClose(callback: (notificationId: string) => void): void {
        this.notificationCloseCallbacks.push(callback);
    }

    onPreferencesChange(callback: (preferences: NotificationPreferences) => void): void {
        this.preferencesChangeCallbacks.push(callback);
    }

    private handleNotificationData(notification: BaseNotification): void {
        // Handle new notification data
        console.log('Received notification data:', notification);
    }

    private handleNotificationUpdate(notification: BaseNotification): void {
        // Handle notification updates
        console.log('Received notification update:', notification);
    }

    private handlePreferencesChange(preferences: NotificationPreferences): void {
        // Notify all registered callbacks
        this.preferencesChangeCallbacks.forEach(callback => {
            try {
                callback(preferences);
            } catch (error) {
                console.error('Error in preferences change callback:', error);
            }
        });
    }

    // Utility methods for renderer process
    async createNotification(request: CreateNotificationRequest): Promise<BaseNotification> {
        // Create a notification object from the request
        const notification: BaseNotification = {
            id: this.generateId(),
            title: request.title,
            message: request.message,
            type: request.type || 'INFO',
            priority: request.priority || 'NORMAL',
            channels: request.channels || ['inApp'],
            status: 'PENDING',
            createdAt: new Date(),
            updatedAt: new Date(),
            scheduledFor: request.scheduledFor,
            expiresAt: request.expiresAt,
            metadata: request.metadata || {},
            recipientId: request.recipientId,
            senderId: request.senderId,
            appId: this.config.appId,
        };

        // Show the notification
        await this.showNotification(notification);
        return notification;
    }

    async updateNotificationById(notificationId: string, updates: UpdateNotificationRequest): Promise<BaseNotification> {
        // Get current notification (this would typically come from a local store)
        const currentNotification = await this.getNotificationFromLocalStore(notificationId);
        if (!currentNotification) {
            throw new Error(`Notification with id ${notificationId} not found`);
        }

        // Apply updates
        const updatedNotification: BaseNotification = {
            ...currentNotification,
            ...updates,
            updatedAt: new Date(),
        };

        // Update the notification
        await this.updateNotification(updatedNotification);
        return updatedNotification;
    }

    private async getNotificationFromLocalStore(notificationId: string): Promise<BaseNotification | null> {
        try {
            // Implementation for local notification storage
            // This integrates with the actual local store or cache

            // Check if we have the notification in memory cache
            if (this.notificationCache.has(notificationId)) {
                return this.notificationCache.get(notificationId)!;
            }

            // Check localStorage for persistent storage
            const storedNotifications = localStorage.getItem('notifications');
            if (storedNotifications) {
                const notifications = JSON.parse(storedNotifications);
                const notification = notifications.find((n: BaseNotification) => n.id === notificationId);

                if (notification) {
                    // Cache it for future access
                    this.notificationCache.set(notificationId, notification);
                    return notification;
                }
            }

            // If not found locally, try to fetch from main process
            const response = await ipcRenderer.invoke('notification:get', notificationId);
            if (response.success && response.data) {
                const notification = response.data;
                // Cache it locally
                this.notificationCache.set(notificationId, notification);
                this.saveToLocalStorage(notification);
                return notification;
            }

            return null;
        } catch (error) {
            console.error('Error getting notification from local store:', error);
            return null;
        }
    }

    private saveToLocalStorage(notification: BaseNotification): void {
        try {
            // Save notification to localStorage for persistence
            const storedNotifications = localStorage.getItem('notifications');
            let notifications = storedNotifications ? JSON.parse(storedNotifications) : [];

            // Update existing or add new
            const existingIndex = notifications.findIndex((n: BaseNotification) => n.id === notification.id);
            if (existingIndex >= 0) {
                notifications[existingIndex] = notification;
            } else {
                notifications.push(notification);
            }

            // Limit storage to last 100 notifications
            if (notifications.length > 100) {
                notifications = notifications.slice(-100);
            }

            localStorage.setItem('notifications', JSON.stringify(notifications));
        } catch (error) {
            console.error('Error saving to localStorage:', error);
        }
    }

    private generateId(): string {
        return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Cleanup method
    destroy(): void {
        // Remove all IPC listeners
        ipcRenderer.removeAllListeners('notification:data');
        ipcRenderer.removeAllListeners('notification:update');
        ipcRenderer.removeAllListeners('preferences:changed');

        // Clear callbacks
        this.notificationClickCallbacks = [];
        this.notificationCloseCallbacks = [];
        this.preferencesChangeCallbacks = [];

        this.isInitialized = false;
    }
}
