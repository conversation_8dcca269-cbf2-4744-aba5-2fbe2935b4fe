# 🚀 Spark Strand Notifications System

A comprehensive, production-ready notification system with multi-tenant architecture, vendor-agnostic providers, and secure API key management.

## ✨ Features

- **🔐 Secure Multi-Tenant Architecture** - Isolated applications with secure API key authentication
- **🔄 Vendor Agnostic** - Switch between Novu, Resend, Twilio, and custom providers seamlessly
- **📱 Multi-Platform Support** - React, React Native, Electron, and Node.js packages
- **⚡ Real-time Delivery** - WebSocket support for instant notifications
- **🎯 Smart Fallback Strategies** - Intelligent provider failover for reliability
- **📊 Comprehensive Monitoring** - Health checks, rate limiting, and analytics
- **🚀 Production Ready** - Built-in security, error handling, and scalability

## 🆕 **Novu Inbox Integration**

**NEW!** Beautiful, professional notification center using Novu's inbox components:

```tsx
import { NovuInbox } from '@sparkstrand/notifications-react';

<NovuInbox
    appId="my-app"
    apiKey="your-api-key"
    userId="user-123"
    theme="light"
    position="top"
/>
```

**Features:**
- 🎨 Beautiful UI with light/dark themes
- 📱 Responsive design for all devices
- ⚡ Real-time updates via WebSocket
- ⚙️ User preference management
- 🔔 Unread count tracking
- 🎯 Customizable positioning and behavior

**✨ Zero additional dependencies!** Our package includes everything you need.

**[Quick Start Guide](docs/NOVU_INBOX_QUICK_START.md)** | **[Full Documentation](docs/NOVU_INBOX_INTEGRATION.md)** | **[Implementation Guide](docs/NOVU_INBOX_IMPLEMENTATION.md)**

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client Apps   │    │  Notification    │    │   Providers     │
│                 │    │     System       │    │                 │
│ • React        │◄──►│ • Multi-tenant   │◄──►│ • Novu          │
│ • React Native │    │ • API Gateway    │    │ • Resend        │
│ • Electron     │    │ • Rate Limiting  │    │ • Twilio        │
│ • Node.js      │    │ • Authentication  │    │ • Custom        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### **1. Install Dependencies**

```bash
# Clone the repository
git clone https://github.com/sparkstrand/notifications.git
cd notifications

# Install dependencies
yarn install
```

### **2. Set Environment Variables**

```bash
# Copy environment template
cp apps/api/env.example apps/api/.env

# Configure your environment
# See ENVIRONMENT_VARIABLES.md for details
```

### **3. Setup Database**

```bash
# Generate Prisma client
yarn workspace @sparkstrand/notifications-api db:generate

# Push schema to database
yarn workspace @sparkstrand/notifications-api db:push

# Seed with initial data
yarn workspace @sparkstrand/notifications-api db:seed
```

### **4. Start the System**

```bash
# Build all packages
yarn build

# Start the API server
yarn workspace @sparkstrand/notifications-api start
```

### **5. Integrate in Your App**

```bash
# Install the React package
npm install @sparkstrand/notifications-react

# Use the components
import { NovuInbox } from '@sparkstrand/notifications-react';
```

## 📦 Packages

| Package | Description | Status |
|---------|-------------|---------|
| [`@sparkstrand/notifications-core`](./packages/core) | Core types, interfaces, and services | ✅ Ready |
| [`@sparkstrand/notifications-react`](./packages/react) | React hooks and components | ✅ Ready |
| [`@sparkstrand/notifications-react-native`](./packages/react-native) | React Native integration | ✅ Ready |
| [`@sparkstrand/notifications-electron`](./packages/electron) | Electron main and renderer | ✅ Ready |
| [`@sparkstrand/notifications-api`](./apps/api) | Next.js API server | ✅ Ready |

## 🔧 Configuration

### **Environment Variables**

```bash
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/notifications"

# Security
JWT_SECRET="your-super-secret-jwt-key"
ENCRYPTION_KEY="your-32-character-encryption-key"

# Providers
NOVU_API_KEY="your-novu-api-key"
RESEND_API_KEY="your-resend-api-key"
TWILIO_ACCOUNT_SID="your-twilio-sid"
TWILIO_AUTH_TOKEN="your-twilio-token"
```

### **Application Registration**

```typescript
// Register a new application
const response = await fetch('/api/applications', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        name: 'My App',
        description: 'Application description',
        defaultProvider: 'novu',
        fallbackStrategy: 'failover'
    })
});
```

## 📚 Documentation

- **[Developer Guide](docs/DEVELOPER_GUIDE.md)** - Complete development setup
- **[Integration Guide](docs/INTEGRATION.md)** - API integration examples
- **[Admin Guide](docs/ADMIN_GUIDE.md)** - System administration
- **[Environment Variables](docs/ENVIRONMENT_VARIABLES.md)** - Configuration reference
- **[Novu Inbox Integration](docs/NOVU_INBOX_INTEGRATION.md)** - Beautiful notification center
- **[Quick Start - Novu Inbox](docs/NOVU_INBOX_QUICK_START.md)** - Get started in 5 minutes
- **[Implementation Guide](docs/NOVU_INBOX_IMPLEMENTATION.md)** - Production-ready setup

## 🧪 Testing

```bash
# Run all tests
yarn test

# Test specific package
yarn workspace @sparkstrand/notifications-core test

# Test with coverage
yarn test:coverage
```

## 🚀 Deployment

### **Automated Deployment**

```bash
# Run the deployment script
./deploy.sh

# Or manually
yarn build
yarn workspace @sparkstrand/notifications-api start
```

### **Production Checklist**

- [ ] Environment variables configured
- [ ] Database migrated and seeded
- [ ] SSL certificates installed
- [ ] Rate limiting configured
- [ ] Monitoring enabled
- [ ] Backup strategy implemented

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the [docs](./docs) folder
- **Issues**: [GitHub Issues](https://github.com/sparkstrand/notifications/issues)
- **Discussions**: [GitHub Discussions](https://github.com/sparkstrand/notifications/discussions)

---

**Built with ❤️ by the Spark Strand team**

Transform your notification experience with a system that's secure, scalable, and beautiful.

